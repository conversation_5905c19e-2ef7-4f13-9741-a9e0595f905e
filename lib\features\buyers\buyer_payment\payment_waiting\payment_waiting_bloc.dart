import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_payment/buyer_payment_status/buyer_payment_status_screen.dart';
import 'package:swadesic/features/data_model/shopping_cart_quantity_data_model/shopping_cart_quantity_data_model.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/buyer_payment_options_responses/payment_status_check.dart';
import 'package:swadesic/services/buyer_payment_services/buyer_payment_services.dart';
import 'package:swadesic/services/buyer_payment_services/upi_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:webview_flutter/webview_flutter.dart';

class PaymentWaitingBloc {
  // region Common Methods
  BuildContext context;
  final String paymentType;
  bool helloVisible = false;
  late BuyerPaymentServices buyerPaymentServices;
  late PaymentStatusCheckResponse paymentStatusCheckResponse;
  String paymentStatus = "PENDING";

//TXN_FAILURE
  //PENDING

  // endregion
  //region Controller
  final screenRefreshCtrl = StreamController<bool>.broadcast();
  late WebViewController webViewController;
  //endregion

  // region | Constructor |
  PaymentWaitingBloc(this.context, this.paymentType);
  // endregion

  // region Init
  void init() async {
    buyerPaymentServices = BuyerPaymentServices();
    await Future.delayed(const Duration(seconds: 3));

    checkStatus();
    //netBankingTransaction();
    // if(paymentType == "bank"){
    //   await Future.delayed(const Duration(milliseconds:1500 ));
    //   goToWebView('https://netbanking.kotak.com/knb2/');
    // }
  }
  // endregion

  //region Call upi status every 2 second
  void checkStatus() async {
    if (paymentStatus == 'TXN_SUCCESS') {
      // screenRefreshCtrl.sink.add(true);
      goToPaymentStatusScreen();
      return;
    } else if (paymentStatus == 'TXN_FAILURE') {
      // screenRefreshCtrl.sink.add(false);
      goToPaymentStatusScreen();
      return;
    } else {
      await Future.delayed(const Duration(milliseconds: 100));
      paymentStatusCheck();
    }
  }
  //endregion

  //region Payment status check
  paymentStatusCheck() async {
    //region Try
    try {
      paymentStatusCheckResponse =
          await buyerPaymentServices.paymentStatusCheck();
      paymentStatus =
          paymentStatusCheckResponse.data!.body!.resultInfo!.resultStatus!;
      //print(paymentStatus);
      checkStatus();
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      //print(error.message);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);

      return;
    } catch (error) {
      //print(error);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }
  //endregion

  //region  Html web
  String bankWeb() {
    return AppConstants.htmlForm;
  }
  //endregion

//region Go to Buyer payment Status Screen
  void goToPaymentStatusScreen() {
    var screen = BuyerPaymentStatus(
      paymentStatus: paymentStatus,
      paymentStatusCheckResponse: paymentStatusCheckResponse,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    // Navigator.push(context, route).then((value) {
    //
    // });
    Navigator.pushReplacement(context, route).then((value) {});
  }
//endregion

//region Dispose
  void dispose() {
    screenRefreshCtrl.close();
    paymentStatus == 'TXN_FAILURE';
  }
//endregion
}
