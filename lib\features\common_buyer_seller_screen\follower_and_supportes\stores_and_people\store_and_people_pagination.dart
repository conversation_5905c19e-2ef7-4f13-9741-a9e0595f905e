import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/recommended_store_and_user/recommended_store_and_user_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/follower_and_supportes/stores_and_people/stores_and_people_bloc.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/data_model/recommended_store_and_user_data_model/recommended_sore_and_user_data_model.dart';
import 'package:swadesic/features/post/feed/feed_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/recommended_store_and_user/recommended_store_and_user.dart';
import 'package:swadesic/services/followers_and_supporters_service/followers_and_supporters_service.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/recommended_store_and_user_service/recommended_store_and_user_service.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class StoreAndPeoplePagination {
  //region Context
  late BuildContext context;
  late StoreAndPeopleBloc storeAndPeopleBloc;

//region Constructor
  StoreAndPeoplePagination(this.context, this.storeAndPeopleBloc) {
    //Scroll listener
    storeAndPeopleBloc.scrollController.addListener(() {
      //If pagination ended and value notifier is true then return
      if (storeAndPeopleBloc.isPaginationEnded ||
          storeAndPeopleBloc.paginationIsLoadingValueNotifier.value == true) {
        return;
      }
      scrollListener();
    });
  }

//endregion

  //region Scroll listener
  void scrollListener() async {
    if (storeAndPeopleBloc.scrollController.position.pixels >=
        storeAndPeopleBloc.scrollController.position.maxScrollExtent) {
      //Increase offset
      storeAndPeopleBloc.offset =
          storeAndPeopleBloc.limit + storeAndPeopleBloc.offset;
      // Fetch more feed posts when list is scrolled to the bottom
      await getStoreAndPeoplePagination();
    }
  }

  //endregion

  //region Get pagination feeds
  Future<void> getStoreAndPeoplePagination() async {
    try {
      //Pagination loading
      storeAndPeopleBloc.paginationIsLoadingValueNotifier.value =
          StoreAndPeoplePaginationState.Loading;
      // await Future.delayed(Duration(seconds: 2));
      //Api call
      List<UserAndStoreInfo> paginationData =
          await FollowersAndSupportersService().followersAndSupporters(
              reference: storeAndPeopleBloc.reference,
              entityType: storeAndPeopleBloc.entityType,
              followEnum: storeAndPeopleBloc.requiredList,
              limit: storeAndPeopleBloc.limit,
              offset: storeAndPeopleBloc.offset);
      // List<RecommendedStoreAndUser> paginationData = await RecommendedStoreAndUserServices().getRecommendedStoreAndUser(isRecommendedStore: true, limit:storeAndPeopleBloc.limit, offset: storeAndPeopleBloc.offset);
      storeAndPeopleBloc.storesAndPeople.addAll(paginationData);
      //If paginationData list is empty
      if (paginationData.isEmpty) {
        //Pagination ended
        storeAndPeopleBloc.isPaginationEnded = true;
        //Pagination Empty
        storeAndPeopleBloc.paginationIsLoadingValueNotifier.value =
            StoreAndPeoplePaginationState.End;
      }
      //Assign the name to the un-register contact
      storeAndPeopleBloc.assignTheNameToUnRegisterContact();
      //Pagination Stop loading
      storeAndPeopleBloc.paginationIsLoadingValueNotifier.value =
          StoreAndPeoplePaginationState.Success;
      //Success
      storeAndPeopleBloc.storeAndPeopleCtrl.sink
          .add(StoreAndPeopleState.Success);
    } on ApiErrorResponseMessage catch (error) {
      // isLoadingPaginationData = false;
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      //Pagination End
      storeAndPeopleBloc.paginationIsLoadingValueNotifier.value =
          StoreAndPeoplePaginationState.End;
    } catch (error) {
      // isLoadingPaginationData = false;
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
      //Pagination End
      storeAndPeopleBloc.paginationIsLoadingValueNotifier.value =
          StoreAndPeoplePaginationState.End;
    }
  }
//endregion
}
