import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/recommended_store_and_user/recommended_store_and_user_bloc.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/data_model/recommended_store_and_user_data_model/recommended_sore_and_user_data_model.dart';
import 'package:swadesic/features/post/feed/feed_bloc.dart';
import 'package:swadesic/features/post/liked_user_or_stores/liked_user_or_store_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/recommended_store_and_user/recommended_store_and_user.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/recommended_store_and_user_service/recommended_store_and_user_service.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum LikedUserOrStoresPaginationState { Loading, Done, Empty }

class LikedUserOrStoresPagination {
  //region Context
  late BuildContext context;
  late LikedUserOrStoreBloc likedUserOrStoreBloc;
  int limit = 30;
  int offset = 30;

  // bool isLoadingPaginationData = false;
  LikedUserOrStoresPaginationState currentPaginationState =
      LikedUserOrStoresPaginationState.Loading;

  //endregion

  //region Controller
  final likedStoreOrUserPaginationStateCtrl =
      StreamController<LikedUserOrStoresPaginationState>.broadcast();

  //endregion

//region Constructor
  LikedUserOrStoresPagination(this.context, this.likedUserOrStoreBloc) {
    //Add initial state in pagination state
    likedStoreOrUserPaginationStateCtrl.sink
        .add(LikedUserOrStoresPaginationState.Done);
    //Scroll listener
    likedUserOrStoreBloc.scrollController.addListener(() {
      scrollListener();
    });
    //Pagination controller listener
    likedStoreOrUserPaginationStateCtrl.stream
        .listen((LikedUserOrStoresPaginationState state) {
      feedPaginationControllerListener(state: state);
    });
  }

//endregion

  //region Feed page Controller listener
  void feedPaginationControllerListener(
      {required LikedUserOrStoresPaginationState state}) {
    currentPaginationState = state;
    //print("Status of pagination${state.name}");
  }

  //endregion

  //region Scroll listener
  void scrollListener() async {
    // Fetch data instance
    // RecommendedStoreAndUserDataModel recommendedStoreAndUserDataModel = Provider.of<RecommendedStoreAndUserDataModel>(context);

    //If Page state is empty then return
    if (currentPaginationState == LikedUserOrStoresPaginationState.Empty) {
      // //print("Feed pagination is empty");
      return;
    }

    //1. If list of data is not smaller then 10 and m
    //2. Position pixel is 200 less then max scroll
    if (
        // storeOrUserList.length >= 10 &&
        likedUserOrStoreBloc.scrollController.offset >=
                likedUserOrStoreBloc
                    .scrollController.position.maxScrollExtent &&
            !likedUserOrStoreBloc.scrollController.position.outOfRange) {
      //Increase offset
      offset = limit + offset;
      // Fetch more feed posts when list is scrolled to the bottom
      await getPaginationFeeds();
    }
  }

  //endregion

  //region Get pagination feeds
  Future<void> getPaginationFeeds() async {
    // Get reference to the RecommendedStoreAndUserDataModel
    var recommendedStoreAndUserDataModel =
        Provider.of<RecommendedStoreAndUserDataModel>(context, listen: false);
    try {
      //Loading
      likedStoreOrUserPaginationStateCtrl.sink
          .add(LikedUserOrStoresPaginationState.Loading);
      //Api call
      // List<UserAndStoreInfo> recommendedStoreList = await RecommendedStoreAndUserServices().getRecommendedStoreAndUser(isRecommendedStore: isRecommendedStore, limit: 10, offset: offset);
      //Clear store in data model
      // recommendedStoreAndUserDataModel.clearAllStoreAndUserList();
      //Add recommended store to data model
      // recommendedStoreAndUserDataModel.addRecommendedStoreOrUser(storeList: recommendedStoreList);

      //If feed list is empty
      // if (recommendedStoreList.isEmpty) {
      //   // recommendedStoreAndUserBloc.showRecommendedStore = true;
      //   //Current state
      //   // currentPaginationState = FeedPaginationState.Empty;
      //   // isLoadingPaginationData = false;
      //   return likedStoreOrUserPaginationStateCtrl.sink.add(LikedUserOrStoresPaginationState.Empty);
      // }
      // isLoadingPaginationData = false;
      //Done
      likedStoreOrUserPaginationStateCtrl.sink
          .add(LikedUserOrStoresPaginationState.Done);
    } on ApiErrorResponseMessage catch (error) {
      // isLoadingPaginationData = false;
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      //Done
      likedStoreOrUserPaginationStateCtrl.sink
          .add(LikedUserOrStoresPaginationState.Done);
    } catch (error) {
      // isLoadingPaginationData = false;
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
      //Done
      likedStoreOrUserPaginationStateCtrl.sink
          .add(LikedUserOrStoresPaginationState.Done);
    }
  }
//endregion
}
