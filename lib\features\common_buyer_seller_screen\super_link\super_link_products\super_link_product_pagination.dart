import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/recommended_store_and_user/recommended_store_and_user_bloc.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/search_recommended_products/search_recommended_products_bloc.dart';
import 'package:swadesic/features/buyers/buyer_view_store/store_products/store_product_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/super_link/super_link_bloc.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/data_model/recommended_store_and_user_data_model/recommended_sore_and_user_data_model.dart';
import 'package:swadesic/features/post/feed/feed_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/recommended_store_and_user/recommended_store_and_user.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/recommended_store_and_user_service/recommended_store_and_user_service.dart';
import 'package:swadesic/services/store_product_services/store_product_services.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum SuperLinkProductPaginationState { Loading, Done }

class SuperLinkProductPagination {
  //region Context
  late BuildContext context;
  late SuperLinkBloc superLinkBloc;
  // bool isLoadingPaginationData = false;
  SuperLinkProductPaginationState currentApiCallStatus =
      SuperLinkProductPaginationState.Done;

  //endregion

  //region Controller
  final superLinkStoreProductPaginationCtrl =
      StreamController<SuperLinkProductPaginationState>.broadcast();
  //endregion

//region Constructor
  SuperLinkProductPagination(this.context, this.superLinkBloc);
//endregion

  //region On pagination loading visible
  void onPaginationLoadingVisible() async {
    await getProducts();
  }

  //endregion

  //region Get pagination products
  Future<void> getProducts() async {
    //Get reference to Product data model
    var productDataModel =
        Provider.of<ProductDataModel>(context, listen: false);
    try {
      //If api call status is Loading then return
      if (currentApiCallStatus == SuperLinkProductPaginationState.Loading) {
        return;
      }
      //Loading
      superLinkStoreProductPaginationCtrl.sink
          .add(SuperLinkProductPaginationState.Loading);
      //Current api call status is Loading
      currentApiCallStatus = SuperLinkProductPaginationState.Loading;
      //Api call
      // List<Product> productList = await RecommendedStoreAndUserServices().getRecommendedProducts(limit: 5, offset: storeProductBloc.storeProductList.length);
      var data = await StoreProductServices().getBuyerStoreProduct(
          storeReference: superLinkBloc.reference,
          limit: 10,
          offset: superLinkBloc.storeProductList.length);
      //Add in search recommended bloc recommendedProductList
      superLinkBloc.storeProductList.addAll(data.data!);
      //Add recommended store to data model
      productDataModel.addProductIntoList(products: data.data!);
      //If feed list is empty
      if (data.data!.isEmpty) {
        //Current api call status is Done
        currentApiCallStatus = SuperLinkProductPaginationState.Done;
        //Empty
        return superLinkStoreProductPaginationCtrl.sink
            .add(SuperLinkProductPaginationState.Done);
      }
      //Current api call status is Done
      currentApiCallStatus = SuperLinkProductPaginationState.Done;
    } on ApiErrorResponseMessage catch (error) {
      // isLoadingPaginationData = false;
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      //Done
      superLinkStoreProductPaginationCtrl.sink
          .add(SuperLinkProductPaginationState.Done);
      //Current api call status is Done
      currentApiCallStatus = SuperLinkProductPaginationState.Done;
    } catch (error) {
      // isLoadingPaginationData = false;
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
      //Done
      superLinkStoreProductPaginationCtrl.sink
          .add(SuperLinkProductPaginationState.Done);
      //Current api call status is Done
      currentApiCallStatus = SuperLinkProductPaginationState.Done;
    }
  }
//endregion
}
