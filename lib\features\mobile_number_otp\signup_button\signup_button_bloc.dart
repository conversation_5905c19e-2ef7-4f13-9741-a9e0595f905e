import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/mobile_number_otp/login_and_signup_with_google.dart';
import 'package:swadesic/features/mobile_number_otp/login_otp/login_otp.dart';
import 'package:swadesic/features/mobile_number_otp/mobile_number_otp_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/login_response/email_sign_in_sign_up_response.dart';
import 'package:swadesic/model/login_response/login_token_info.dart';
import 'package:swadesic/model/login_response/mobile_number_response.dart';
import 'package:swadesic/services/signin_services/signin_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum SignUpBlocState {
  Loading,
  LoadingStop,
}

class SignUpBloc {
  //region Common variable
  final BuildContext context;
  final MobileNumberOtpBloc mobileNumberOtpBloc;

  final bool isRegisterUser;
  //endregion

  //region Controller
  final isButtonLoadingCtrl = StreamController<bool>.broadcast();
  final signUpBlocCtrl = StreamController<SignUpBlocState>.broadcast();
  //endregion

  //region Constructor
  SignUpBloc(this.context, this.mobileNumberOtpBloc, this.isRegisterUser);
  //endregion

  //region Init
  void init() {}

  //endregion

  //region Email login
  void emailLogin() async {
    if (!mobileNumberOtpBloc.emailFormKey.currentState!.validate() ||
        mobileNumberOtpBloc.emailAddressTextCtrl.text.trim().isEmpty) {
      context.mounted
          ? CommonMethods.toastMessage(
              AppStrings.enterValidEmailAddress, context)
          : null;
      return;
    }
    try {
      //Loading
      signUpBlocCtrl.sink.add(SignUpBlocState.Loading);
      //Body
      Map<String, dynamic> body = {
        "phonenumber": "+91${mobileNumberOtpBloc.mobileNumberTextCtrl.text}",
        "email": mobileNumberOtpBloc.emailAddressTextCtrl.text.trim()
      };
      //Get access token and otp field visible status
      EmailSignInSignUpResponse emailSignInSignUpResponse =
          await SignInServices().emailLogin(body: body);
      //Push to otp screen
      goToLogInOtpScreen(
          email: mobileNumberOtpBloc.emailAddressTextCtrl.text.trim(),
          phoneNumber: "+91${mobileNumberOtpBloc.mobileNumberTextCtrl.text}",
          isEmailOtp: emailSignInSignUpResponse.sentEmailOtp!,
          isPhoneOtp: emailSignInSignUpResponse.sentPhonenumberOtp!);
      //Loading stop
      signUpBlocCtrl.sink.add(SignUpBlocState.LoadingStop);
    } on ApiErrorResponseMessage catch (error) {
      //Loading stop
      signUpBlocCtrl.sink.add(SignUpBlocState.LoadingStop);
      context.mounted
          ? CommonMethods.toastMessage(error.message!, context,
              toastShowTimer: 5)
          : null;
    } catch (error) {
      //Loading stop
      signUpBlocCtrl.sink.add(SignUpBlocState.LoadingStop);
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
    }
  }

//endregion

  //region Go to login otp screen
  void goToLogInOtpScreen(
      {required String email,
      required String phoneNumber,
      required bool isEmailOtp,
      required bool isPhoneOtp}) {
    var screen = LoginOtpScreen(
      email: email,
      phoneNumber: phoneNumber,
      isEmailOtp: isEmailOtp,
      isPhoneOtp: isPhoneOtp,
      isRegisterUser: isRegisterUser,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

  ///Google login
//region Google sign in
  void googleSignIn() async {
    //Loading
    signUpBlocCtrl.sink.add(SignUpBlocState.Loading);
    //Open google sign in request
    await LoginAndSignupWithGoogle(
            context,
            "+91${mobileNumberOtpBloc.mobileNumberTextCtrl.text}",
            mobileNumberOtpBloc,
            isButtonLoadingCtrl,
            isRegisterUser)
        .openGoogleLoginDialog();
    //Loading stop
    signUpBlocCtrl.sink.add(SignUpBlocState.LoadingStop);
  }
//endregion
}
