import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_screen.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_screen.dart';
import 'package:swadesic/features/notification/notification_screen.dart';
import 'package:swadesic/features/post/add_post/add_post_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/features/seller/seller_home/seller_home_screen.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/seller/seller_accounts/seller_accounts_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/create_store_options/create_store_options.dart';
import 'package:swadesic/features/common_buyer_seller_screen/messaging_and_requests/all_messages/all_messages_screen.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/store_dashboard_bloc.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';

/// Navigation actions implementation
class NavigationActions {
  static BuildContext? _context;
  static List<GlobalKey<NavigatorState>>? _navigatorKeys;

  /// Initialize with context and navigator keys
  static void initialize(BuildContext context,
      [List<GlobalKey<NavigatorState>>? navigatorKeys]) {
    _context = context;
    _navigatorKeys = navigatorKeys;
  }

  /// Navigate within a specific tab with proper back button handling
  static void navigateInTab(int tabIndex, Widget screen) {
    if (_navigatorKeys != null && tabIndex < _navigatorKeys!.length) {
      final navigator = _navigatorKeys![tabIndex].currentState;
      if (navigator != null) {
        navigator.push(MaterialPageRoute(
          builder: (context) => PopScope(
            canPop: true,
            child: screen,
          ),
        ));
        return;
      }
    }

    // Fallback to regular navigation if tab navigation fails
    if (_context != null) {
      Navigator.push(
          _context!,
          MaterialPageRoute(
            builder: (context) => PopScope(
              canPop: true,
              child: screen,
            ),
          ));
    }
  }

  /// Navigate within a specific tab with proper back button handling
  static void _navigateInTab(int tabIndex, Widget screen) {
    if (_navigatorKeys != null && tabIndex < _navigatorKeys!.length) {
      final navigator = _navigatorKeys![tabIndex].currentState;
      if (navigator != null) {
        navigator.push(MaterialPageRoute(
          builder: (context) => PopScope(
            canPop: true,
            child: screen,
          ),
        ));
        return;
      }
    }

    // Fallback to regular navigation if tab navigation fails
    if (_context != null) {
      Navigator.push(
          _context!,
          MaterialPageRoute(
            builder: (context) => PopScope(
              canPop: true,
              child: screen,
            ),
          ));
    }
  }

  // Screen builders
  static Widget buildBuyerHomeScreen() => const BuyerHomeScreen();

  static Widget buildBuyerSearchScreen() => const BuyerSearchScreen();

  static Widget buildBuyerNotificationScreen() => const NotificationScreen();

  static Widget buildBuyerAddScreen() => const AddPostScreen();

  static Widget buildBuyerProfileScreen() {
    return AppConstants.appData.userReference != null &&
            !CommonMethods().isStaticUser()
        ? UserProfileScreen(
            userReference: AppConstants.appData.userReference!,
            fromBottomNavigation: true,
            key: Key("profile_${AppConstants.appData.userReference}"),
          )
        : const SizedBox();
  }

  static Widget buildSellerHomeScreen() => const SellerHomeScreen();

  static Widget buildSellerSearchScreen() => const BuyerSearchScreen();

  static Widget buildSellerOrdersScreen() {
    return AppConstants.appData.storeId != null
        ? SellerAllOrdersScreen(
            storeId: AppConstants.appData.storeId!,
            isFromBottomNavigation: true,
          )
        : const SizedBox();
  }

  static Widget buildSellerNotificationScreen() {
    return AppConstants.appData.storeId != null
        ? NotificationScreen(storeId: AppConstants.appData.storeId!)
        : const NotificationScreen();
  }

  static Widget buildSellerAddScreen() => const AddPostScreen();

  static Widget buildSellerProfileScreen() {
    return AppConstants.appData.storeReference != null
        ? BuyerViewStoreScreen(
            isLeadingVisible: false,
            isStoreOwnerView: true,
            isFromBottomNavigation: true,
            storeReference: AppConstants.appData.storeReference!,
          )
        : const SizedBox();
  }

  // Buyer gesture actions
  static void buyerHomeLongPress() {
    // Open Messaging Home screen in Home tab (index 0)
    _navigateInTab(0, const AllMessagesScreen());
  }

  static void buyerSearchLongPress() {
    // Open search page with search bar automatically focused in Search tab (index 1)
    _navigateInTab(1, const BuyerSearchScreen());
    debugPrint('Navigated to search screen with auto-focus intent');
  }

  static void buyerNotificationLongPress() {
    // Mark all personal notifications as read + show toast
    _markAllNotificationsAsRead(isStore: false);
    if (_context != null) {
      ScaffoldMessenger.of(_context!).showSnackBar(
        const SnackBar(content: Text("All notifications marked as read")),
      );
    }
  }

  static void buyerAddLongPress() {
    if (_context == null) return;
    // Trigger create store button
    CommonMethods.appMinimumBottomSheets(
      context: _context!,
      screen: const CreateStoreOptions(),
    );
  }

  static void buyerProfileLongPress() {
    // Show Switch accounts page in Profile tab (index 4)
    if (CommonMethods().isStaticUser()) {
      AppConstants.isSignInScreenOpenedFromProfileTab = true;
      CommonMethods().goToSignUpFlow();
      return;
    }
    if (AppConstants.appData.userReference != null) {
      Navigator.push(
          _context!,
          MaterialPageRoute(
            builder: (context) => SellerAccountsScreen(
              userReference: AppConstants.appData.userReference!,
            ),
          ));
    }
  }

  // Seller gesture actions
  static void sellerHomeLongPress() {
    // Open Messaging Home screen in Home tab (index 0)
    _navigateInTab(0, const AllMessagesScreen());
  }

  static void sellerSearchLongPress() {
    // Open search page with search bar automatically focused in Search tab (index 1)
    _navigateInTab(1, const BuyerSearchScreen());
    debugPrint('Navigated to search screen with auto-focus intent');
  }

  static void sellerOrdersLongPress() {
    // Open Account balance page using StoreDashBoardBloc in Orders tab (index 2)
    if (AppConstants.appData.storeReference != null && _context != null) {
      final storeDashBoardBloc =
          StoreDashBoardBloc(_context!, AppConstants.appData.storeReference!);
      storeDashBoardBloc.goToAccountBalance();
    } else if (_context != null) {
      ScaffoldMessenger.of(_context!).showSnackBar(
        const SnackBar(content: Text("Store reference not available")),
      );
    }
  }

  static void sellerNotificationLongPress() {
    // Mark all store notifications as read + show toast
    _markAllNotificationsAsRead(isStore: true);
    if (_context != null) {
      ScaffoldMessenger.of(_context!).showSnackBar(
        const SnackBar(content: Text("All notifications marked as read")),
      );
    }
  }

  static void sellerAddLongPress() {
    if (_context == null) return;
    // Trigger create store button (same as switch accounts page)
    CommonMethods.appMinimumBottomSheets(
      context: _context!,
      screen: const CreateStoreOptions(),
    );
  }

  static void sellerProfileLongPress() {
    // Show Switch accounts page in Profile tab (index 5 for seller)
    if (_context != null && AppConstants.appData.userReference != null) {
      HapticFeedback.lightImpact();
      Navigator.push(
          _context!,
          MaterialPageRoute(
            builder: (context) => SellerAccountsScreen(
              userReference: AppConstants.appData.userReference!,
            ),
          ));
    }
  }

  // Double tap actions (both navigations)
  static void doubleTapAction(Widget screen, int tabIndex) {
    // Open the corresponding page and reload/refresh it in the specific tab
    if (_navigatorKeys != null && tabIndex < _navigatorKeys!.length) {
      final navigator = _navigatorKeys![tabIndex].currentState;
      if (navigator != null) {
        navigator.pushReplacement(MaterialPageRoute(
          builder: (context) => PopScope(
            canPop: true,
            child: screen,
          ),
        ));
        return;
      }
    }

    // Fallback to regular navigation if tab navigation fails
    if (_context != null) {
      Navigator.pushReplacement(
          _context!,
          MaterialPageRoute(
            builder: (context) => PopScope(
              canPop: true,
              child: screen,
            ),
          ));
    }
  }

  // Helper methods
  static void _markAllNotificationsAsRead({required bool isStore}) {
    // Implementation to mark notifications as read
    // This would typically involve calling an API or updating local state
    debugPrint(
        'Marking all ${isStore ? 'store' : 'user'} notifications as read');
  }
}
