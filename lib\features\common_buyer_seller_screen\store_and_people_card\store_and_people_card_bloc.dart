import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/recommended_store_and_user/recommended_store_and_user.dart';
import 'package:swadesic/services/find_your_customers_services/find_your_customers_services.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class StoreAndPeopleCardBloc {
  // region Common Variables
  BuildContext context;
  // endregion

  //region Controller
  final refreshUi = StreamController<bool>.broadcast();
  //endregion

  // region | Constructor |
  StoreAndPeopleCardBloc(this.context);

  // endregion

  // region Init
  init() async {}

// endregion

  ///Single follow and support
  //region On tap Follow and support
  void onTapFollowAndSupport(
      {required UserAndStoreInfo recommendedStoreAndUser}) async {
    try {
      recommendedStoreAndUser.followStatus = await FindYourCustomersServices()
          .followAndUnFollow(reference: recommendedStoreAndUser.reference!);

      //Increase or decrease support or follow count
      //If status is Following the increase by 1 else reduce by 1
      if (recommendedStoreAndUser.followStatus!.toLowerCase() ==
              FollowEnum.FOLLOWING.name.toLowerCase() ||
          recommendedStoreAndUser.followStatus!.toLowerCase() ==
              FollowEnum.SUPPORTING.name.toLowerCase()) {
        recommendedStoreAndUser.followersOrSupportersCount =
            recommendedStoreAndUser.followersOrSupportersCount! + 1;
      } else {
        recommendedStoreAndUser.followersOrSupportersCount =
            recommendedStoreAndUser.followersOrSupportersCount! > 0
                ? recommendedStoreAndUser.followersOrSupportersCount! - 1
                : 0;
      }

      //Refresh ui
      refreshUi.sink.add(true);
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message!, context)
          : null;
    } catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
    }
  }
  //endregion

  //region On tap icon
  onTapIcon({required UserAndStoreInfo recommendedStoreAndUser}) {
    var screen = recommendedStoreAndUser.entityType == EntityType.STORE.name
        ? BuyerViewStoreScreen(
            storeReference: recommendedStoreAndUser.reference)
        : UserProfileScreen(
            userReference: recommendedStoreAndUser.reference!,
          );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

  //region Dispose
  void dispose() {
    refreshUi.close();
  }

//endregion
}
