import 'package:flutter/material.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/all_store_notification.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/user_or_store_notification.dart';
import 'package:swadesic/features/navigation/core/base_navigation.dart';
import 'package:swadesic/features/navigation/widgets/custom_bottom_nav.dart';
import 'package:swadesic/features/navigation/config/navigation_config.dart';
import 'package:swadesic/features/navigation/config/navigation_actions.dart';
import 'package:swadesic/features/seller/seller_home/seller_home_screen.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_screen.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_screen.dart';
import 'package:swadesic/features/notification/notification_screen.dart';
import 'package:swadesic/features/post/add_post/add_post_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/auto_hide_navigation_service.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/bottom_navigations/store_bottom_navigation/store_bottom_navigation_bloc.dart';
import 'package:swadesic/util/app_images.dart';

/// Seller navigation implementation with Home, Search, Orders, Notifications, Add + Profile tabs
class SellerNavigation extends BaseNavigation {
  const SellerNavigation({super.key});

  @override
  State<SellerNavigation> createState() => _SellerNavigationState();
}

class _SellerNavigationState extends BaseNavigationState<SellerNavigation> {
  // Tab configuration
  static const List<String> _tabIcons = [
    // Icons.home,
    AppImages.homeIconOutlined,
    AppImages.searchIconOutlined,
    AppImages.ordersIconOutlined,
    AppImages.notificationIconOutlined,
    AppImages.addIconOutlined,
  ];

  static const List<String> _tabIconsFilled = [
    AppImages.homeIconFilled,
    AppImages.searchIcon2Filled,
    AppImages.ordersIconFilled,
    AppImages.notificationIconFilled,
    AppImages.addIconOutlined,
  ];

  static const List<String> _tabNames = [
    "Home",
    "Search",
    "Orders",
    "Notifications",
    "Add",
  ];
  late final StoreBottomNavigationBloc storeBottomNavigationBloc;

  // Using Consumer widgets instead of caching providers

  @override
  void initState() {
    super.initState();
    // Set up app constants for seller navigation
    AppConstants.userStoreCommonBottomNavigationContext = context;
    AppConstants.isBottomNavigationMounted.value = true;

    // Set seller navigation to Profile tab (index 5) by default for store accounts
    AppConstants.storePersistentTabController.index = 5;
    // Set the internal currentIndex to Profile tab (5) for seller navigation
    final initialIndex = _getInitialTabIndex();
    currentIndex = initialIndex;
    debugPrint('SellerNavigation: Setting initial tab index to $initialIndex');

    storeBottomNavigationBloc = StoreBottomNavigationBloc(context);
    storeBottomNavigationBloc.init();

    // Initialize navigation actions with context and navigator keys
    NavigationActions.initialize(context, navigatorKeys);

    // Cache provider data once to avoid repeated lookups
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _cacheProviderData();
        // Ensure currentSelectedTabContext is set for the initial tab
        _initializeCurrentTabContext();
      }
    });
  }

  /// Initialize the current tab context for the initial tab to ensure deep links work
  void _initializeCurrentTabContext() {
    try {
      // Get the navigator key for the current tab
      final currentTabKey = navigatorKeys[currentIndex];
      if (currentTabKey.currentContext != null) {
        AppConstants.currentSelectedTabContext = currentTabKey.currentContext!;
        debugPrint('SellerNavigation: Initialized currentSelectedTabContext for tab $currentIndex');
      } else {
        // If context is not available yet, try again after a short delay
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted && currentTabKey.currentContext != null) {
            AppConstants.currentSelectedTabContext = currentTabKey.currentContext!;
            debugPrint('SellerNavigation: Delayed initialization of currentSelectedTabContext for tab $currentIndex');
          }
        });
      }
    } catch (e) {
      debugPrint('SellerNavigation: Error initializing currentSelectedTabContext: $e');
    }
  }

  /// Get the initial tab index for seller navigation
  /// Store accounts should start on Profile tab (index 5)
  int _getInitialTabIndex() {
    // Check if we have a stored tab index from account switching
    final storedIndex = AppConstants.storePersistentTabController.index;
    debugPrint(
        'SellerNavigation._getInitialTabIndex: storedIndex=$storedIndex');

    // If the stored index is valid and is the Profile tab (5), use it
    if (storedIndex == 5) {
      debugPrint('SellerNavigation._getInitialTabIndex: Using stored index 5');
      return 5;
    }

    // For store accounts, default to Profile tab
    debugPrint(
        'SellerNavigation._getInitialTabIndex: Defaulting to Profile tab (5)');
    return 5; // Profile tab index
  }

  void _cacheProviderData() {
    // No longer needed as we're using Consumer widgets
  }

  @override
  List<Widget> get tabPages {
    // Get tab screens from centralized configuration
    final configScreens = NavigationConfig.getTabScreens(NavigationType.seller);
    // Add profile screen
    configScreens.add(NavigationActions.buildSellerProfileScreen());
    return configScreens;
  }

  @override
  Widget buildCustomBottomNav() {
    // No need to cache store info as we'll use Consumer to listen for changes

    final isProfileTab = currentIndex == tabCount - 1;
    return CustomBottomNav(
      currentIndex: isProfileTab
          ? 4
          : currentIndex, // Show last grouped tab as selected when on profile
      onTabSelected: (index) => _handleTabSelected(index),
      onDoubleTap: (index) => _handleDoubleTap(index),
      onHorizontalSwipe: onHorizontalSwipe,
      onLongPress: (index) => _handleLongPress(index),
      onIconSwipe: (index) => _handleIconSwipe(index),
      tabIcons: NavigationConfig.getTabIcons(NavigationType.seller),
      tabIconsFilled:
          NavigationConfig.getTabIcons(NavigationType.seller, filled: true),
      tabNames: NavigationConfig.getTabNames(NavigationType.seller),
      groupedTabCount: 5, // 5 tabs in the grouped section for seller
      profileWidget: Consumer<SellerOwnStoreInfoDataModel>(
        builder: (context, storeModel, _) => ProfileAvatar(
          isSelected: isProfileTab,
          isStore: true,
          imageUrl: storeModel.storeInfoResponse?.icon,
          key: ValueKey<String>(
              'store_avatar_${storeModel.storeInfoResponse?.storeReference ?? 'default'}'),
        ),
      ),
    );
  }

  // Gesture handlers using centralized configuration
  void _handleTabSelected(int index) {
    if (index == 4) {
      // Add button tapped - pass the current tab's context
      storeBottomNavigationBloc.addPostAndProductBottomSheet(
        context: context,
        tabContext: navigatorKeys[currentIndex].currentContext,
      );
    } else if (index < 5) {
      // Update the persistent tab controller
      AppConstants.storePersistentTabController.index = index;

      NavigationConfig.executeGesture(
          NavigationType.seller, index, GestureType.tap);
      onTabSelected(index == 4 ? tabCount - 1 : index);
    } else {
      onTabSelected(index == 4 ? tabCount - 1 : index);
    }
  }

  void _handleDoubleTap(int index) {
    // if (index < 5) {
    //   NavigationConfig.executeGesture(NavigationType.seller, index, GestureType.doubleTap);
    // } else {
    //   // Profile tab double tap
    //   NavigationActions.doubleTapAction(NavigationActions.buildSellerProfileScreen(), 5);
    // }
    // resetTab(index == 4 ? tabCount - 1 : index);
  }

  void _handleLongPress(int index) {
    if (index < 5) {
      NavigationConfig.executeGesture(
          NavigationType.seller, index, GestureType.longPress);
    } else {
      // Profile tab long press
      NavigationActions.sellerProfileLongPress();
    }
  }

  void _handleIconSwipe(int index) {
    if (index < 5) {
      NavigationConfig.executeGesture(
          NavigationType.seller, index, GestureType.swipe);
    }
    onIconSwipe(index == 4 ? tabCount - 1 : index);
  }

  @override
  void onProfileTabSelected() {
    // After Profile switching behavior: Show profile tab showing Store page
    // This means when user taps profile, they stay on the profile tab
    // No special switching behavior needed for seller
  }

  @override
  void dispose() {
    AppConstants.isBottomNavigationMounted.value = false;
    super.dispose();
  }
}

/// Wrapper widget to handle auto-hide navigation for seller screens
class SellerNavigationWrapper extends StatefulWidget {
  final Widget child;
  final ScrollController? scrollController;

  const SellerNavigationWrapper({
    super.key,
    required this.child,
    this.scrollController,
  });

  @override
  State<SellerNavigationWrapper> createState() =>
      _SellerNavigationWrapperState();
}

class _SellerNavigationWrapperState extends State<SellerNavigationWrapper> {
  final AutoHideNavigationService _autoHideService =
      AutoHideNavigationService();

  @override
  void initState() {
    super.initState();
    _autoHideService.enableAutoHide();
    if (widget.scrollController != null) {
      _autoHideService.attachToScrollController(widget.scrollController!);
    }
  }

  @override
  void dispose() {
    _autoHideService.disableAutoHide();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
