// import 'dart:async';
//
// import 'package:flutter/material.dart';
// import 'package:swadesic/features/bottom_navigations/user_bottom_navigation.dart';
// import 'package:swadesic/features/buyers/buyer_home/buyer_home_screen.dart';
// import 'package:swadesic/features/buyers/buyer_onboarding/buyer_on_boarding/buyer_on_boarding_screen.dart';
// import 'package:swadesic/model/api_response_message.dart';
// import 'package:swadesic/model/login_response/check_otp_response.dart';
// import 'package:swadesic/model/login_response/mobile_number_response.dart';
// import 'package:swadesic/model/user_details_response/user_details_response.dart';
// import 'package:swadesic/services/cache_storage/cache_storage_service.dart';
// import 'package:swadesic/services/cache_storage/storage_keys.dart';
// import 'package:swadesic/services/signin_services/signin_services.dart';
// import 'package:swadesic/services/user_detail_services/user_detail_services.dart';
// import 'package:swadesic/util/app_constants.dart';
// import 'package:swadesic/util/app_strings.dart';
// import 'package:swadesic/util/common_methods.dart';
//
// enum MobileOtpState { Loading, Success, Failed }
//
// class OtpBloc {
//   // region Common Methods
//   BuildContext context;
//   int startTime = 90;
//   late SignInServices signInServices;
//   late MobileNumberResponse mobileNumberResponse;
//   late CheckOtpResponse checkOtpResponse;
//   late CacheStorageService cacheStorageService;
//   bool isUserExist = true;
//   late UserDetailsServices userDetailsServices;
//   late GetUserDetailsResponse userDetailsResponse;
//   String otpText = "";
//   late Timer timer;
//
//
//
//
//
//   // endregion
//
//   //region Controller
//   final countDownCtrl = StreamController<bool>.broadcast();
//   final otpFieldVisibleCtrl = StreamController<bool>.broadcast();
//   final mobileOtpCtrl = StreamController<MobileOtpState>.broadcast();
//   final referralCtrl = StreamController<bool>.broadcast();
//   //endregion
//
//   //region Text Editing Controller
//   TextEditingController mobileNumberTextCtrl = TextEditingController();
//   TextEditingController referralTextCtrl = TextEditingController();
//
//   //endregion
//
//   // region | Constructor |
//   OtpBloc(this.context);
//
//   // endregion
//
//
//
//   // region Init
//   void init() {
//     userDetailsServices = UserDetailsServices();
//     signInServices = SignInServices();
//     cacheStorageService = CacheStorageService();
//     countDown();
//
//
//
//   }
//
//   // endregion
//
//
//   //region Send Otp
//   void sendOtp()async{
//     try{
//       //Close KeyBord
//       CommonMethods.closeKeyboard(context);
//       //Check Mobile Number Is 10 Digit or not
//       String pattern = r'(^(?:[+0]9)?[0-9]{10}$)';
//       RegExp regExp = RegExp(pattern);
//       if(!regExp.hasMatch(mobileNumberTextCtrl.text)){
//         CommonMethods.toastMessage("Enter valid mobile number", context);
//         return;
//       }
//       //Loading State
//       mobileOtpCtrl.sink.add(MobileOtpState.Loading);
//       //Api Call and save response on mobileNumberResponse
//       mobileNumberResponse = await signInServices.getMobileNumber("+91${mobileNumberTextCtrl.text}");
//
//       //Save user is new or not
//       isUserExist = mobileNumberResponse.userExist!;
//       //Start countDown and visible otp field and Success State
//       mobileOtpCtrl.sink.add(MobileOtpState.Success);
//       countDown();
//       return otpFieldVisibleCtrl.sink.add(true);
//     }
//      on ApiErrorResponseMessage catch (error) {
//       CommonMethods.toastMessage(AppStrings.commonErrorMessage, context, commonErrorMessage: true);
//       return;
//       //print(error.message);
//       mobileOtpCtrl.sink.add(MobileOtpState.Failed);
//       CommonMethods.toastMessage(AppStrings.error, context);
//       //
//       // var snackBar = const SnackBar(content: Text(AppStrings.error));
//       // ScaffoldMessenger.of(context).showSnackBar(snackBar);
//       return;
//     }
//     catch(error){
//       //print(error);
//       mobileOtpCtrl.sink.add(MobileOtpState.Failed);
//       CommonMethods.toastMessage(AppStrings.error, context);
//       //
//       // var snackBar = const SnackBar(content: Text(AppStrings.error));
//       // ScaffoldMessenger.of(context).showSnackBar(snackBar);
//       return;
//     }
//
//
//   }
//   //endregion
//
//   //region Countdown
//   void countDown()async{
//
//     const oneSec = Duration(seconds: 1);
//     timer = Timer.periodic(
//       oneSec,
//           (Timer timer) {
//         if (startTime == 0) {
//           timer.cancel();
//           countDownCtrl.sink.add(true);
//
//         } else {
//           startTime --;
//           countDownCtrl.sink.add(true);
//         }
//
//       },
//     );
//
//
//
//     // startTime = 90;
//     // for(startTime;startTime>= 0;startTime--){
//     //   await Future.delayed(const Duration(seconds: 1));
//     //   countDownCtrl.sink.add(startTime);
//     //   if(startTime == 0){
//     //     return;
//     //   }
//     // }
//   }
// //endregion
//
//
//   //region OnTap Mobile number field
//   onTapMobileNumberField(){
//     startTime = 90;
//     otpFieldVisibleCtrl.sink.add(false);
//     //Stop timer
//
//     timer.isActive?timer.cancel():null;
//
//   }
//   //endregion
//
//   //region Verify otp
//   void verifyOtp()async{
//     try{
//       //Check Otp Filed is 6 Digit
//       if(otpText.length < 6){
//         CommonMethods.toastMessage("Enter a valid otp", context);
//         return;
//       }
//       //Loading State
//       mobileOtpCtrl.sink.add(MobileOtpState.Loading);
//       //region Api Call
//       checkOtpResponse = await signInServices.checkOtp(otpText, "+91${mobileNumberTextCtrl.text}",referralCode: referralTextCtrl.text);
//       //endregion
//       //region Save USER ID to Share Preferences
//       await cacheStorageService.saveString(StorageKeys.userId, checkOtpResponse.userid.toString());
//       //region Save USER Reference to Share Preferences
//       await cacheStorageService.saveString(StorageKeys.userReference, checkOtpResponse.userReference!);
//       //Save user Id to global
//       AppConstants.appData.userId = checkOtpResponse.userid!;
//       //Save user reference to global
//       AppConstants.appData.userReference = checkOtpResponse.userReference!;
//       //Get user detail api call
//       getUserDetails();
//
//
//
//       //endregion
//     }
//     on ApiErrorResponseMessage catch(error){
//       mobileOtpCtrl.sink.add(MobileOtpState.Failed);
//       otpText = "";
//       CommonMethods.toastMessage(error.message.toString(), context);
//
//       // var snackBar = SnackBar(content: Text(error.message.toString()));
//       // ScaffoldMessenger.of(context).showSnackBar(snackBar);
//       return;
//     }
//     catch(error){
//       mobileOtpCtrl.sink.add(MobileOtpState.Failed);
//       otpText = "";
//       CommonMethods.toastMessage(error.toString(), context);
//       //
//       // var snackBar = SnackBar(content: Text(error.toString()));
//       // ScaffoldMessenger.of(context).showSnackBar(snackBar);
//       return;
//     }
//
//   }
//   //endregion
//
//
//
//   //region Get user details
//   getUserDetails()async{
//     try{
//       userDetailsResponse = await userDetailsServices.getLoggedInUserDetail();
//       //region Go to Buyer Home Screen or Buyer On boarding
//       if(checkOtpResponse.message=="welcome back"){
//         CommonMethods.saveAllData(userDetailsResponse);
//         goToBottomNavigation();
//       }
//       else{
//         CommonMethods.saveAllData(userDetailsResponse);
//         goToBuyerOnBoarding();
//       }
//
//       // if(userDetailsResponse.data!.profileComplete!){
//       //   saveAllData();
//       //   goToBottomNavigation();
//       // }
//       // if(userDetailsResponse.data!.profileComplete==null){
//       //   saveAllData();
//       //   goToBottomNavigation();
//       // }
//       // else{
//       //   goToBuyerOnBoarding();
//       // }
//
//     }
//     on ApiErrorResponseMessage catch(error){
//       //print(error.message);
//       var snackBar = SnackBar(content: Text(error.message.toString()));
//       ScaffoldMessenger.of(context).showSnackBar(snackBar);
//       return;
//     }
//     catch(error){
//       //print(error);
//       var snackBar = SnackBar(content: Text(error.toString()));
//       ScaffoldMessenger.of(context).showSnackBar(snackBar);
//       return;
//     }
//
//
//   }
//   //endregion
//
//
//
//
//
//
//
//   //region Go to Bottom Navigation
//   void goToBottomNavigation(){
//     Navigator.of(context).pushAndRemoveUntil(MaterialPageRoute(builder: (context) =>
//         UserBottomNavigation()), (Route<dynamic> route) => false);
//     // var screen =   UserBottomNavigation();
//     // var route = MaterialPageRoute(builder: (context) => screen);
//     // Navigator.push(context, route);
//   }
//   //endregion
//
//   //region Go to Buyer on boarding screen
//   void goToBuyerOnBoarding(){
//     Navigator.of(context).pushAndRemoveUntil(MaterialPageRoute(builder: (context) =>
//         BuyerOnBoardingScreen()), (Route<dynamic> route) => false);
//     // var screen =   BuyerOnBoardingScreen();
//     // var route = MaterialPageRoute(builder: (context) => screen);
//     // Navigator.push(context, route);
//   }
// //endregion
//
//
//
//
//
//
// }
