import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/post/feed/feed_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum FeedPaginationState { Loading, Done, Empty, Null }

class FeedPagination {
  //region Context
  late BuildContext context;
  late FeedBloc feedBloc;
  int limit = 5;
  bool _disposed = false;
  FeedPaginationState currentApiCallStatus = FeedPaginationState.Done;

  // bool isLoadingPaginationData = false;
  FeedPaginationState currentPaginationState = FeedPaginationState.Loading;

  //endregion

  //region Controller
  final feedPaginationStateCtrl =
      StreamController<FeedPaginationState>.broadcast();

  //endregion

//region Constructor
  FeedPagination(this.context, this.feedBloc) {
    //Add initial state in pagination state
    feedPaginationStateCtrl.sink.add(FeedPaginationState.Done);
    //Scroll listener
    feedBloc.scrollController.addListener(() {
      if (!_disposed) {
        scrollListener();
      }
    });
    //Pagination controller listener
    feedPaginationStateCtrl.stream.listen((FeedPaginationState state) {
      if (!_disposed) {
        feedPaginationControllerListener(state: state);
      }
    });
  }

  void dispose() {
    _disposed = true;
    feedPaginationStateCtrl.close();
  }

//endregion

  //region Feed page Controller listener
  void feedPaginationControllerListener({required FeedPaginationState state}) {
    currentPaginationState = state;
    //print("Status of pagination${state.name}");
  }

  //endregion

  //region Scroll listener
  void scrollListener() async {
    //If Page state is empty then return
    if (currentPaginationState == FeedPaginationState.Empty) {
      // //print("Feed pagination is empty");
      return;
    }

    double screenHeight = MediaQuery.of(context).size.height;
    double tenthOfScreenHeight = screenHeight * 0.1;
    double scrollPositionToTrigger =
        feedBloc.scrollController.position.maxScrollExtent -
            tenthOfScreenHeight;

    //1. If list of data is not smaller then 10 and m
    //2. Position pixel is 200 less then max scroll
    if (
        // feedBloc.scrollController.offset >= feedBloc.scrollController.position.maxScrollExtent &&
        // !feedBloc.scrollController.position.outOfRange
        feedBloc.feedList.length >= 10 &&
            feedBloc.scrollController.offset >=
                feedBloc.scrollController.position.maxScrollExtent &&
            !feedBloc.scrollController.position.outOfRange) {
      //Increase offset
      // Fetch more feed posts when list is scrolled to the bottom
      await getPaginationFeeds();
    }
  }

  //endregion

  //region Get pagination feeds
  Future<void> getPaginationFeeds() async {
    // Get reference to the PostDataModel
    var postDataModel = Provider.of<PostDataModel>(context, listen: false);
    //Get reference to Product data model
    var productDataModel =
        Provider.of<ProductDataModel>(context, listen: false);
    try {
      //If api call status is Loading then return
      if (currentApiCallStatus == FeedPaginationState.Loading) {
        return;
      }
      //Loading
      feedPaginationStateCtrl.sink.add(FeedPaginationState.Loading);
      //Current api call status is Loading
      currentApiCallStatus = FeedPaginationState.Loading;
      //Api call - use the new lean feed API
      List<dynamic> feedList = await PostService().getLeanFeeds(
          limit: limit, offset: feedBloc.feedList.length, context: context);

      //Add feed to the feed list variable
      feedBloc.feedList.addAll(feedList);
      //Filter post and product
      for (var postAndProduct in feedList) {
        if (postAndProduct is PostDetail) {
          //Add data in post data model feed
          postDataModel.addPostIntoList(postList: [postAndProduct]);
          // //print('Post: ${postAndProduct.postOrCommentReference}');
        } else if (postAndProduct is Product) {
          //Add data to product data model
          productDataModel.addProductIntoList(products: [postAndProduct]);
          // //print('Product: ${postAndProduct.productReference}');
        }
      }
      //If feed list is empty
      if (feedList.isEmpty) {
        //Empty
        feedPaginationStateCtrl.sink.add(FeedPaginationState.Empty);
        //Current api call status is Empty
        currentApiCallStatus = FeedPaginationState.Empty;
        return;
      }
      //Done
      feedPaginationStateCtrl.sink.add(FeedPaginationState.Done);
      //Current api call status is Done
      currentApiCallStatus = FeedPaginationState.Done;
    } on ApiErrorResponseMessage catch (error) {
      // isLoadingPaginationData = false;
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      //Done
      feedPaginationStateCtrl.sink.add(FeedPaginationState.Done);
    } catch (error) {
      // isLoadingPaginationData = false;
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
      //Done
      feedPaginationStateCtrl.sink.add(FeedPaginationState.Done);
    }
  }
//endregion
}
