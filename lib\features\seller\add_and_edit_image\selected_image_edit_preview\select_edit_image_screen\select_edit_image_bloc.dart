import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';


class SelectEditImageBloc {
  // region Common Variables
  BuildContext context;
  final ImagePicker picker = ImagePicker();

  // var selectedImage;
   List<XFile> selectedImage = [];

  // endregion


  //region Controller
  //endregion

  // region | Constructor |
  SelectEditImageBloc(this.context);

  // endregion

  // region Init
  void init() async{


  }
// endregion


//region Open Gallery
  void openGallery()async{
    try{
      List <XFile> galleryImage = (await picker.pickMultiImage(
          requestFullMetadata: true
      ));
      //If image is not empty
      if(galleryImage.isNotEmpty){
        //Pop and send image back
        Navigator.pop(context,galleryImage);
      }
    }
    catch(e){
      Navigator.pop(context);
    }

  }
//endregion


//region Open Camera
  void openCamera()async{
    try{
      XFile imageFromCamera = (await picker.pickImage(source: ImageSource.camera, requestFullMetadata: true))!;
      //Pop and send image back
      context.mounted?Navigator.pop(context, [imageFromCamera]):null;
        }
    catch(e){
      Navigator.pop(context);
    }

  }

//endregion




}

