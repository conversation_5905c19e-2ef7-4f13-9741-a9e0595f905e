import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_orders_screen.dart';
import 'package:swadesic/features/buyers/recently_visited_store/recently_visited_store_screen.dart';
import 'package:swadesic/features/buyers/supported_stores/supported_stores_screen.dart';
import 'package:swadesic/features/buyers/user_reward_and_invitees/user_reward_and_invitees_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/affiliate_program/affiliate_program_with_appBar.dart';
import 'package:swadesic/features/common_buyer_seller_screen/app_web_view/app_web_view.dart';
import 'package:swadesic/features/common_buyer_seller_screen/create_store_options/create_store_options.dart';
import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/find_your_customers_screen.dart';
import 'package:swadesic/features/seller/membership/membership_screen.dart';
import 'package:swadesic/features/seller/seller_accounts/seller_accounts_screen.dart';
import 'package:swadesic/features/seller/seller_onboarding/seller_onboarding_get_started/seller_onboarding_get_started_screen.dart';
import 'package:swadesic/features/support/support_screen.dart';
import 'package:swadesic/features/user_profile/user_settings/user_settings.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';


class HomeAccessBloc {
  // region Common Variables
  BuildContext context;



  // endregion


  //region Controller

  final refreshCtrl = StreamController<bool>.broadcast();

  //endregion

  //region Text Controller

  //endregion

  // region | Constructor |
  HomeAccessBloc(this.context);
  // endregion

  // region Init
  void init() {

  }
// endregion


  //region Go to membership screen
  void goToMembership(){
    //If non register user
    
    if(CommonMethods().isStaticUser()){
      CommonMethods().goToSignUpFlow();
      return ;
    }
    var screen = const MembershipScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

  //region Go to User settings
  void goToUserSetting(){
    //If non register user
    
    if(CommonMethods().isStaticUser()){
      CommonMethods().goToSignUpFlow();
      return ;
    }
    var screen = const UserProfileSettingsScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion



  //region On Tap Go to support
  onTapGoToSupport({required bool isReport}){
    //If non register user

    if(CommonMethods().isStaticUser()){
      CommonMethods().goToSignUpFlow();
      return ;
    }
    var screen = SupportScreen(
      isReport: isReport,
      // No target store reference since this is general support from home
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.currentSelectedTabContext, route);
  }
//endregion


  //region On Tap reward screen
  void onTapReward(){
    //If non register user
    
    if(CommonMethods().isStaticUser()){
      CommonMethods().goToSignUpFlow();
      return ;
    }
    var screen = const UserRewardAndInviteesScreen(selectedTab: 0,);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

  //region On Tap invitees screen
  void onTapInvitees(BuildContext context) {
    //If non register user
    if(CommonMethods().isStaticUser()){
      CommonMethods().goToSignUpFlow();
      return;
    }
    var screen = const UserRewardAndInviteesScreen(selectedTab: 1,);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

  //region Go to Seller Accounts Screen
  void goToSellerAccountScreen({required String userReference}) async{
    // if static user then open login screen
    if(CommonMethods().isStaticUser()){
      CommonMethods().goToSignUpFlow();
      return ;
    }
    var screen = SellerAccountsScreen(userReference: userReference);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

  //endregion


  //region On Tap Favourite store
  onTapFavouriteStore() {
    //If non register user
    
    if(CommonMethods().isStaticUser()){
      CommonMethods().goToSignUpFlow();
      return ;
    }
    var screen = const SupportedStoresScreen();
    // var screen = const MyOrdersScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {});
  }

//endregion

  //region On Tap recently visited store
  onTapRecentlyVisitedStore() {
    //RecentlyVisitedStoreScreen
    //If non register user
    
    if(CommonMethods().isStaticUser()){
      CommonMethods().goToSignUpFlow();
      return ;
    }
    var screen = const RecentlyVisitedStoreScreen();
    // var screen = const MyOrdersScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {});
  }

//endregion

  //region Go to My order screen
  void goToMyOrderScreen() {
    if(CommonMethods().isStaticUser()){
      CommonMethods().goToSignUpFlow();
      return ;
    }
    var screen = const BuyerMyOrderScreen();
    // var screen = const MyOrdersScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {});
  }
//endregion

  //region On Tap Find your friends
  goToFindYourCustomer() async{
    //If web view
    if(kIsWeb){
      return  await CommonMethods.appDownloadDialog();
    }
    //Else if static user then open login screen
    else if(CommonMethods().isStaticUser()){
      return CommonMethods().goToSignUpFlow();
    }
    var screen =  FindYourCustomersScreen(visibleNext: false, title: AppStrings.findYourFriendsOnSwadesic,);
    // var screen = const MyOrdersScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {});
  }

//endregion



  //region Go to affiliate program
  void goToAffiliateProgram(){
    var  screen =  const AffiliateProgramWithAppBar();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion




  //region On tap Create
  Future<void> onTapCreateStore(){
    return CommonMethods.appMinimumBottomSheets(
      context: context,
      screen: CreateStoreOptions(),
    );
  }
  //endregion

    //region Open Web View
  void _openWebView(String url, bool showAppBar) {
    if (url.isNotEmpty) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => AppWebView(url: url, showAppBar: showAppBar),
        ),
      );
    }
  }
  //endregion

  //region On tap Swadesic Blog
  onTapGoToSwadesicBlog(){
    const blogUrl = 'https://swadesic.sociallyx.com/blog';
    if (blogUrl.isNotEmpty) {
      _openWebView(blogUrl, false);
    }
    
  }
  //endregion

//region Dispose
  void dispose() {
    imageCache.clear();
    refreshCtrl.close();
  }
//endregion

}
