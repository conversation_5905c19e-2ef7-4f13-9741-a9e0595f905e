import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/components/common_components/needResolution/need_resolution.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/model/order_response/sub_order.dart';

class BuyerReturnBloc {
  // region Common Variables
  BuildContext context;
  final Order order;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  late BuyerMyOrderServices buyerMyOrderServices;
  final List<SubOrder> subOrderList;

  // endregion

  //region Controller
  final bottomSheetRefresh = StreamController<bool>.broadcast();
  //endregion

  //region Text Controller

  //endregion

  // region | Constructor |
  BuyerReturnBloc(
      this.context, this.order, this.buyerSubOrderBloc, this.subOrderList);
  // endregion

  // region Init
  void init() {
    buyerMyOrderServices = BuyerMyOrderServices();
  }
// endregion

  //region Cancel return request
  cancelReturnRequest({required SubOrder subOrder}) async {
    //region Try
    try {
      await buyerMyOrderServices
          .cancelReturnRequest(subOrderNumbers: [subOrder.suborderNumber!]);
      //Message
      CommonMethods.toastMessage("Return cancelled", context);

      //Get sub orders
      buyerSubOrderBloc.getSubOrders();
    }
    //endregion
    on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);

      return;
    }
  }
//endregion

// region On Tap need help
  Future onTapNeedHelp({required List<SubOrder> selectedSuborderList}) {
    return CommonMethods.appBottomSheet(
      context: context,
      screen: Container(
        margin: const EdgeInsets.symmetric(horizontal: 10),
        child: NeedResolution(
          subOrderList: selectedSuborderList,
          buyerSubOrderBloc: buyerSubOrderBloc,
          order: order,
          escalationReason: AppStrings.buyerRefundOnHoldEscalationReason,
          packageReference: null,
          title: AppStrings.ifYouFeelAfter,
          subTitle: AppStrings.additionalNotesOnThis,
          buttonText: AppStrings.issueWithRefundAmount,
        ),
      ),
      bottomSheetName: AppStrings.refundAmountIsOnHold,
    ).then((value) {});
  }
// endregion
}
