import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/recommended_store_and_user/recommended_store_and_user_bloc.dart';
import 'package:swadesic/features/buyers/supported_stores/supported_stores_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/follower_and_supportes/stores_and_people/stores_and_people_bloc.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/data_model/recommended_store_and_user_data_model/recommended_sore_and_user_data_model.dart';
import 'package:swadesic/features/post/feed/feed_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/recommended_store_and_user/recommended_store_and_user.dart';
import 'package:swadesic/services/followers_and_supporters_service/followers_and_supporters_service.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/recommended_store_and_user_service/recommended_store_and_user_service.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class SupportedStoresPagination {
  //region Context
  late BuildContext context;
  late SupportedStoresBloc supportedStoresBloc;

//region Constructor
  SupportedStoresPagination(this.context, this.supportedStoresBloc) {
    //Scroll listener
    supportedStoresBloc.scrollController.addListener(() {
      //If pagination ended and value notifier is true then return
      if (supportedStoresBloc.isPaginationEnded ||
          supportedStoresBloc.paginationIsLoadingValueNotifier.value == true) {
        return;
      }
      scrollListener();
    });
  }

//endregion

  //region Scroll listener
  void scrollListener() async {
    if (supportedStoresBloc.scrollController.position.pixels >=
        supportedStoresBloc.scrollController.position.maxScrollExtent) {
      //Increase offset
      supportedStoresBloc.offset =
          supportedStoresBloc.limit + supportedStoresBloc.offset;
      // Fetch more feed posts when list is scrolled to the bottom
      // await getPaginationFeeds();
    }
  }

  //endregion

//   //region Get pagination feeds
//   Future<void> getPaginationFeeds() async {
//     try {
//
//       //Pagination loading
//       supportedStoresBloc.paginationIsLoadingValueNotifier.value = StoreAndPeoplePaginationState.Loading;
//       // await Future.delayed(Duration(seconds: 2));
//       //Api call
//       List<RecommendedStoreAndUser> paginationData = await FollowersAndSupportersService().followersAndSupporters(reference: supportedStoresBloc.reference,entityType: supportedStoresBloc.entityType,followEnum: supportedStoresBloc.requiredList, limit: supportedStoresBloc.limit, offset: supportedStoresBloc.offset);
//
//       // List<RecommendedStoreAndUser> paginationData = await RecommendedStoreAndUserServices().getRecommendedStoreAndUser(isRecommendedStore: true, limit:supportedStoresBloc.limit, offset: supportedStoresBloc.offset);
//       supportedStoresBloc.storeList.addAll(paginationData);
//       //If paginationData list is empty
//       if (paginationData.isEmpty) {
//         //Pagination ended
//         supportedStoresBloc.isPaginationEnded = true;
//         //Pagination Empty
//         supportedStoresBloc.paginationIsLoadingValueNotifier.value = StoreAndPeoplePaginationState.End;
//
//       }
//       //Pagination Stop loading
//       supportedStoresBloc.paginationIsLoadingValueNotifier.value = StoreAndPeoplePaginationState.Success;
//       //Success
//       supportedStoresBloc.storeAndPeopleCtrl.sink.add(StoreAndPeopleState.Success);
//
//     } on ApiErrorResponseMessage catch (error) {
//       // isLoadingPaginationData = false;
//       context.mounted ? CommonMethods.toastMessage(error.message.toString(), context) : null;
//       //Pagination End
//       supportedStoresBloc.paginationIsLoadingValueNotifier.value = StoreAndPeoplePaginationState.End;
//     } catch (error) {
//       // isLoadingPaginationData = false;
//       context.mounted ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context, commonErrorMessage: true) : null;
//       //Pagination End
//       supportedStoresBloc.paginationIsLoadingValueNotifier.value = StoreAndPeoplePaginationState.End;
//     }
//   }
// //endregion
}
