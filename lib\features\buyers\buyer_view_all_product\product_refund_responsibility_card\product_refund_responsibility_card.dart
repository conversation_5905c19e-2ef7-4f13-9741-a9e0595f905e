import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/product_refund_responsibility_card/product_refund_responsibility_card_bloc.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class ProductRefundResponsibilityCard extends StatefulWidget {
  final List<RefundResponsibility> refundResponsibilityList;
  const ProductRefundResponsibilityCard(
      {super.key, required this.refundResponsibilityList});

  @override
  State<ProductRefundResponsibilityCard> createState() =>
      _ProductRefundResponsibilityCardState();
}

class _ProductRefundResponsibilityCardState
    extends State<ProductRefundResponsibilityCard> {
  //region Bloc
  late ProductRefundResponsibilityCardBloc productRefundResponsibilityCardBloc;

  //endregion

  //region Init
  @override
  void initState() {
    productRefundResponsibilityCardBloc =
        ProductRefundResponsibilityCardBloc(context);
    super.initState();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return StreamBuilder<bool>(
        stream: productRefundResponsibilityCardBloc.dropDownRefreshCtrl.stream,
        initialData: false,
        builder: (context, snapshot) {
          return Container(
            margin: const EdgeInsets.only(bottom: 20, left: 5),
            width: double.infinity,
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: AppColors.lightestGrey2,
            ),
            child: InkWell(
              onTap: () {
                productRefundResponsibilityCardBloc.onTapDropDown(
                    value: snapshot.data!);
              },
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    // margin: const EdgeInsets.only(bottom: 10),
                    padding: const EdgeInsets.symmetric(vertical: 3),
                    // Header with title and arrow
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Row(
                          children: [
                            Text(
                              AppStrings.buyerRefundPolicy,
                              style: AppTextStyle.heading3Medium(
                                  textColor: AppColors.appBlack),
                            )
                          ],
                        ),
                        RotatedBox(
                          quarterTurns: snapshot.data! ? 2 : 0,
                          child: Icon(Icons.keyboard_arrow_down,
                              color: AppColors.appBlack),
                        ),
                      ],
                    ),
                  ),
                  // verticalSizedBox(12),
                  // Subtitle
                  Text(
                    AppStrings.buyerFullRefundBasedOnConditions,
                    style: AppTextStyle.contentText0(
                        textColor: AppColors.appBlack),
                  ),
                  // Expanded content
                  Visibility(
                    visible: snapshot.data!,
                    child: _buildExpandedContent(),
                  ),
                ],
              ),
            ),
          );
        });
  }
//endregion

//region Expand detail
  Widget expandDetail(
      {required List<RefundResponsibility> refundResponsibilityList}) {
    List<RefundResponsibility> dataList = [];
    dataList.addAll(refundResponsibilityList);
    // dataList.removeAt(0);

    return Container(
      margin: EdgeInsets.only(top: 10),
      child: ListView.builder(
        itemCount: dataList.length - 1, // Skip the first element
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemBuilder: (context, index) {
          return Container(
            margin: EdgeInsets.only(bottom: 10),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Heading
                Text(
                  "${dataList[index + 1].itemHeading}", // Start from second element
                  maxLines: 5,
                  style: AppTextStyle.heading3Medium(
                      textColor: AppColors.appBlack),
                ),
                // Title
                Visibility(
                  visible: dataList[index + 1].itemText.isNotEmpty,
                  child: Text(
                    "${dataList[index + 1].itemText}", // Start from second element
                    maxLines: 5,
                    style: AppTextStyle.contentText0(
                        textColor: AppColors.appBlack),
                  ),
                ),
                // Sub text
                Visibility(
                  visible: dataList[index + 1].itemSubtext.isNotEmpty,
                  child: Text(
                    "${dataList[index + 1].itemSubtext}", // Start from second element
                    maxLines: 5,
                    style:
                        AppTextStyle.smallText(textColor: AppColors.appBlack),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

//region Build Expanded Content
  Widget _buildExpandedContent() {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // If seller cancels or auto-cancels
          _buildSection(
            title: AppStrings.buyerIfSellerCancelsOrAutoCancels,
            content: AppStrings.buyerFullRefund,
          ),
          verticalSizedBox(16),

          // If buyer cancels
          _buildSection(
            title: AppStrings.buyerIfBuyerCancels,
            content: AppStrings.buyerPartialRefund,
            bulletPoints: [
              AppStrings.buyerTransactionFeeTDSDeducted,
              AppStrings.buyerDeliveryFeeRefundedOnlyIfShippingNotStarted,
            ],
          ),
          verticalSizedBox(16),

          // If buyer return the product
          _buildSection(
            title: AppStrings.buyerIfBuyerReturnTheProduct,
            content: AppStrings.buyerPartialRefund,
            bulletPoints: [
              AppStrings.buyerDeliveryFeeNotRefundedSinceAlreadyShipped,
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required String content,
    List<String>? bulletPoints,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTextStyle.heading3Medium(textColor: AppColors.appBlack),
        ),
        verticalSizedBox(8),
        Text(
          content,
          style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
        ),
        if (bulletPoints != null && bulletPoints.isNotEmpty) ...[
          verticalSizedBox(8),
          ...bulletPoints
              .map((point) => Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Text(
                      '• $point',
                      style: AppTextStyle.contentText0(
                          textColor: AppColors.appBlack),
                    ),
                  ))
              .toList(),
        ],
      ],
    );
  }
//endregion
}
