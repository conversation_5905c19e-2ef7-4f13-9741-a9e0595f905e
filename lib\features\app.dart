// import 'package:device_preview/device_preview.dart';
import 'package:app_links/app_links.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/base_url.dart';
import 'package:swadesic/env.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/add_user_profile_picture/add_user_profile_picture.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/buyer_on_boarding/buyer_on_boarding_screen.dart';
import 'package:swadesic/services/app_link_services/handle_url.dart';
import 'package:swadesic/features/buyers/user_reward_and_invitees/user_reward_and_invitees_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/app_web_view/app_web_view.dart';
import 'package:swadesic/features/common_buyer_seller_screen/common_referral_page/common_referral_page.dart';
import 'package:swadesic/features/common_buyer_seller_screen/messaging_and_requests/messaging_and_requests_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/super_link/super_link_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/user_referrer/user_referrer.dart';
import 'package:swadesic/features/data_model/app_config_data_model/app_config_data_model.dart';
import 'package:swadesic/features/data_model/faq_data_model/faq_data_model.dart';
import 'package:swadesic/features/data_model/liked_user_or_store_data_model/liked_user_or_store_data.dart';
import 'package:swadesic/features/data_model/message_chat_data_model/message_chat_data_model.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/all_store_notification.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/user_or_store_notification.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/data_model/recommended_store_and_user_data_model/recommended_sore_and_user_data_model.dart';
import 'package:swadesic/features/data_model/saved_bank_data_model/saved_bank_data_model.dart';
import 'package:swadesic/features/data_model/search_result_count_data_model/search_result_count_data_model.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_config_data_model.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/data_model/user_created_stores/user_created_stores.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/all_store_notification.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/user_or_store_notification.dart';
import 'package:swadesic/features/mandatory_update/mandatory_update.dart';
import 'package:swadesic/features/mobile_number_otp/intro_slider/intro_slider_screen.dart';
import 'package:swadesic/features/mobile_number_otp/mobile_number_otp_screen.dart';
import 'package:swadesic/features/post/new.dart';
import 'package:swadesic/features/providers/store_info_provider/store_info_provider.dart';
import 'package:swadesic/features/reffer_data.dart';
import 'package:swadesic/providers/theme_provider.dart';

// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:fluttertoast/fluttertoast.dart';
import 'package:swadesic/features/splash/splash_screen.dart';
import 'package:swadesic/features/web_app_navigation.dart';
import 'package:swadesic/features/widgets/subscription_card/subscription_card.dart';
import 'package:swadesic/model/intro_slider/intro_slider.dart';
import 'package:swadesic/services/app_analytics/app_analytics.dart';
import 'package:swadesic/services/app_link_services/web_app_capture_url.dart';
import 'package:swadesic/services/app_remote_config/app_remote_config_service.dart';
import 'package:swadesic/services/flutter_web_navigation_service/flutter_web_navigation_service.dart';
import 'package:swadesic/services/user_device_detail_services/user_device_detail_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_themes.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/features/data_model/shopping_cart_quantity_data_model/shopping_cart_quantity_data_model.dart';
import 'package:universal_html/html.dart' as html;
import 'package:url_strategy/url_strategy.dart';
import 'package:freerasp/freerasp.dart';

class App extends StatefulWidget {
  const App({Key? key}) : super(key: key);

  @override
  _AppState createState() => _AppState();
}

class _AppState extends State<App> {
  //region Init
  @override
  void initState() {
    //App remote config
    AppRemoteConfigService().init();
    AppConstants.appContext = context;
    AppAnalytics();
    super.initState();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    imageCache.clear();
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
            create: (context) => SellerOwnStoreInfoDataModel()),
        ChangeNotifierProvider(
            create: (context) => ShoppingCartQuantityDataModel()),
        ChangeNotifierProvider(create: (context) => StoreDashboardDataModel()),
        ChangeNotifierProvider(
            create: (context) => LoggedInUserInfoDataModel()),
        ChangeNotifierProvider(
            create: (context) => UserOrStoreNotificationDataModel()),
        ChangeNotifierProvider(
            create: (context) => AllStoreNotificationDataModel()),
        ChangeNotifierProvider(
            create: (context) => UserCreatedStoresDataModel()),
        ChangeNotifierProvider(create: (context) => PostDataModel()),
        ChangeNotifierProvider(
            create: (context) => RecommendedStoreAndUserDataModel()),
        ChangeNotifierProvider(create: (context) => StoreInfoProvider()),
        ChangeNotifierProvider(create: (context) => ProductDataModel()),
        ChangeNotifierProvider(create: (context) => StoreConfigDataModel()),
        ChangeNotifierProvider(
            create: (context) => LikedUserOrStoreDataModel()),
        ChangeNotifierProvider(
            create: (context) => SearchResultCountDataModel()),
        ChangeNotifierProvider(create: (context) => AppConfigDataModel()),
        ChangeNotifierProvider(create: (context) => SavedBankDataModel()),
        ChangeNotifierProvider(create: (context) => MessageChatDataModel()),
        ChangeNotifierProvider(create: (context) => FaqDataModel()),
        ChangeNotifierProvider(create: (context) => ThemeProvider()),
      ],
      child: AppInitializer(),
    );
  }
}

class AppInitializer extends StatefulWidget {
  @override
  _AppInitializerState createState() => _AppInitializerState();
}

class _AppInitializerState extends State<AppInitializer> {
  ThemeProvider? _themeProvider;
  VoidCallback? _themeListener;

  @override
  void initState() {
    super.initState();
    // Initialize theme after providers are available
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // Set global context for AppColors
      AppColors.setGlobalContext(context);
      // Initialize theme provider
      _themeProvider = Provider.of<ThemeProvider>(context, listen: false);
      await _themeProvider!.initializeTheme();
      
      // Set initial system UI overlay style
      _updateSystemUIOverlayStyle(_themeProvider!.isDarkMode(context));
      
      // Initialize deep linking after theme is set
      _initializeDeepLinking();
      
      // Listen to theme changes and force a rebuild of the entire subtree
      _themeListener = () {
        if (mounted) {
          _updateSystemUIOverlayStyle(_themeProvider!.isDarkMode(context));
          setState(() {});
        }
      };
      _themeProvider!.addListener(_themeListener!);
    });
  }
  
  // Initialize deep linking after app is fully loaded
  Future<void> _initializeDeepLinking() async {
    try {
      // Small delay to ensure the app is fully initialized
      await Future.delayed(const Duration(seconds: 1));
      
      // Handle any initial deep link
      if (!kIsWeb) {
        await HandleUrl.handleInitialNavigation();
      }
    } catch (e) {
      debugPrint('Error initializing deep linking: $e');
    }
  }

  @override
  void dispose() {
    // Remove theme listener to avoid memory leaks
    if (_themeProvider != null && _themeListener != null) {
      _themeProvider!.removeListener(_themeListener!);
    }
    super.dispose();
  }

  // Update system UI overlay style based on theme
  void _updateSystemUIOverlayStyle(bool isDark) {
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
        statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
        systemNavigationBarColor:
            isDark ? AppColors.appWhite : AppColors.appWhite,
        systemNavigationBarIconBrightness:
            isDark ? Brightness.light : Brightness.dark,
        systemNavigationBarDividerColor: Colors.transparent,
        systemNavigationBarContrastEnforced: true,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Update global context on every build to ensure it's current
    AppColors.setGlobalContext(context);

    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        double paddingInBothSide =
            CommonMethods.calculateWebPadding(context: context);
        return Container(
          color: AppColors.appWhite, // Now automatically theme-aware
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: paddingInBothSide),
            child: Consumer<ThemeProvider>(
              builder: (context, themeProvider, child) {
                return MaterialApp(
                  navigatorObservers: <NavigatorObserver>[
                    AppAnalytics.observer
                  ],
                  title: "Swadesic",
                  debugShowCheckedModeBanner:
                      AppEnvironment.environment == Environment.dev
                          ? true
                          : false,
                  navigatorKey: AppConstants.globalNavigator,
                  theme: AppThemes.lightTheme,
                  darkTheme: AppThemes.darkTheme,
                  themeMode: themeProvider.getThemeMode(context),
                  // If environment is local then open BaseUrlChangeScreen else open SplashScreen
                  home:
                      (AppConstants.appCurrentEnvironment == Environment.local)
                          ? const BaseUrlChangeScreen()
                          : kIsWeb
                              ? WebAppNavigation()
                              : const SplashScreen(),
                );
              },
            ),
          ),
        );
      },
    );
  }
}

//region Over scroll blue color remove
class MyCustomScrollBehavior extends MaterialScrollBehavior {
  @override
  Widget buildOverscrollIndicator(
      BuildContext context, Widget child, ScrollableDetails details) {
    return child;
  }
}
//endregion