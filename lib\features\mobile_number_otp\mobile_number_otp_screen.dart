import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/navigation/navigation_router.dart';
import 'package:swadesic/features/mobile_number_otp/discovery_and_support/discovery_and_support.dart';
import 'package:swadesic/features/mobile_number_otp/mobile_number_otp_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

class MobileNumberOtpScreen extends StatefulWidget {
  final bool isFromBottomNavigation;
  const MobileNumberOtpScreen({Key? key, this.isFromBottomNavigation = false})
      : super(key: key);

  @override
  State<MobileNumberOtpScreen> createState() => MobileNumberOtpScreenState();
}

class MobileNumberOtpScreenState extends State<MobileNumberOtpScreen> {
  //region Bloc
  late MobileNumberOtpBloc mobileNumberOtpBloc;
  double width = 0.0;
  bool showAllOptions = false;
  final StreamController<bool> showAllOptionsCtrl =
      StreamController<bool>.broadcast();
  final ScrollController scrollController = ScrollController();

  //endregion

  //region Init
  @override
  void initState() {
    //If from bottom navigation then mark as sign in flow opening from static user view
    if (widget.isFromBottomNavigation) {
      AppConstants.isSignInScreenOpenedForStatisUser = true;
    }
    mobileNumberOtpBloc = MobileNumberOtpBloc(context);
    mobileNumberOtpBloc.init();

    // Initialize show all options state
    showAllOptions = false;
    showAllOptionsCtrl.sink.add(showAllOptions);

    // Add keyboard visibility listener with simpler, more reliable scrolling logic
    KeyboardVisibilityController().onChange.listen((bool visible) {
      if (visible && mobileNumberOtpBloc.isEmailFieldVisible) {
        // When keyboard becomes visible and email field is shown
        // Wait for keyboard to fully appear and layout to stabilize
        Future.delayed(const Duration(milliseconds: 100), () {
          if (scrollController.hasClients) {
            // Use a fixed position that ensures the email field is visible
            // This is a more reliable approach than percentage-based scrolling
            scrollController.animateTo(
              200, // Fixed position that should show the email field
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          }
        });
      }
    });

    super.initState();
  }

  //endregion

  //region Dispose
  @override
  void dispose() {
    AppConstants.isSignInScreenOpenedForStatisUser = false;
    AppConstants.isSignInScreenOpenedFromProfileTab = false;
    showAllOptionsCtrl.close();
    scrollController.dispose();
    mobileNumberOtpBloc.dispose();
    super.dispose();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvoked: (didPop) {
        //print("Hello can pop $didPop");
      },
      child: LayoutBuilder(
        builder: (context, boxConstrain) {
          width = boxConstrain.maxWidth;
          return GestureDetector(
            onTap: () {
              CommonMethods.closeKeyboard(context);
            },
            child: Scaffold(
              resizeToAvoidBottomInset: true,
              backgroundColor: AppColors.appWhite,
              body: Stack(
                fit: StackFit.expand,
                alignment: Alignment.center,
                children: [
                  // Image.asset(
                  //   AppImages.onboardingBackground,
                  //   fit: BoxFit.fitWidth,
                  // ),
                  SafeArea(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: body(),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  //region Body
  Widget body() {
    return kIsWeb ? forWebView() : forMobile();
  }

//endregion

  ///Web
  //region For web view
  Widget forWebView() {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          //Flag and text
          AppCommonWidgets.flagAndText(
              text: AppStrings.createShopDiscuss, context: context),
          DiscoveryAndSupport(mobileNumberOtpBloc: mobileNumberOtpBloc),
          emailAddress(),
          const SizedBox(
            height: 30,
          ),
          SizedBox(
            height: MediaQuery.of(context).size.height / 4,
          ),
          //Sign up and sign in button
          signInAndSignUpButton()
        ],
      ),
    );
  }
  //endregion

  ///Mobile
  //region For mobile
  Widget forMobile() {
    return Stack(
      children: [
        SizedBox(
          height: MediaQuery.of(context).size.height,
          child: SingleChildScrollView(
            controller: scrollController,
            physics: const AlwaysScrollableScrollPhysics(),
            child: Padding(
              padding: const EdgeInsets.only(bottom: 20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  //Flag and text
                  AppCommonWidgets.flagAndText(
                      text: AppStrings.createShopDiscuss, context: context),
                  DiscoveryAndSupport(mobileNumberOtpBloc: mobileNumberOtpBloc),

                  // Email address section
                  emailAddress(),

                  // Add extra space at the bottom to ensure scrolling works properly
                  // when the keyboard is open - use a fixed height that works on most devices
                  const SizedBox(
                    height: 300,
                  ),
                ],
              ),
            ),
          ),
        ),
        //Sign up and sign in button
        Align(alignment: Alignment.bottomCenter, child: signInAndSignUpButton())
      ],
    );
  }
  //endregion

//region Mobile Number Text
  Widget mobileNumber() {
    return Container(
      margin: const EdgeInsets.only(top: 40),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppStrings.shareUsYourMobileNumber,
            textAlign: TextAlign.start,
            style: AppTextStyle.access0(textColor: AppColors.writingBlack0),
          ),
          const SizedBox(
            height: 10,
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 13),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(
                  CommonMethods.calculateWebWidth(context: context) * 0.03),
              color: AppColors.textFieldFill1,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                //Flag
                Container(
                    margin: const EdgeInsets.only(right: 5),
                    child: SvgPicture.asset(
                      AppImages.indiaFlag,
                      height: 25,
                    )),

                //+91
                Text(
                  "+91 - ",
                  style: AppTextStyle.access1(textColor: AppColors.appBlack)
                      .copyWith(height: 1),
                ),

                Expanded(
                  child: TextFormField(
                    key: const Key("login_phone_number"),
                    controller: mobileNumberOtpBloc.mobileNumberTextCtrl,
                    onChanged: (value) {
                      //Start number is +91
                      // if (!mobileNumberOtpBloc.mobileNumberTextCtrl.text.startsWith("+91 ")) {
                      //   mobileNumberOtpBloc.mobileNumberTextCtrl.text = "+91 ";
                      //   mobileNumberOtpBloc.mobileNumberTextCtrl.selection = TextSelection.collapsed(offset: mobileNumberOtpBloc.mobileNumberTextCtrl.text.length);
                      // }
                      //If Length is 10 then close keyboard
                      if (mobileNumberOtpBloc
                              .mobileNumberTextCtrl.text.length ==
                          10) {
                        CommonMethods.closeKeyboard(context);
                      }
                      //Hide email field and hide button access
                      else {
                        //Hide below buttons
                        mobileNumberOtpBloc.signInOptionsState =
                            SignInOptionState.Failed;
                        mobileNumberOtpBloc.signInOptionsStateCtrl.sink
                            .add(mobileNumberOtpBloc.signInOptionsState);
                        //Hide email field
                        mobileNumberOtpBloc.isEmailFieldVisible = false;
                        mobileNumberOtpBloc.visibleEmailAddressOptions.sink
                            .add(mobileNumberOtpBloc.isEmailFieldVisible);
                      }
                      //Check int or not
                      // if (int.tryParse(mobileNumberOtpBloc.mobileNumberTextCtrl.text) != null && !mobileNumberOtpBloc.mobileNumberTextCtrl.text.toString().contains("+")) {
                      //   mobileNumberOtpBloc.mobileNumberTextCtrl.text = int.parse(mobileNumberOtpBloc.mobileNumberTextCtrl.text).toString();
                      //   mobileNumberOtpBloc.mobileNumberTextCtrl.selection = TextSelection.fromPosition(
                      //       TextPosition(offset: mobileNumberOtpBloc.mobileNumberTextCtrl.text.length));
                      // }
                    },
                    onTap: () {
                      mobileNumberOtpBloc.isEmailFieldVisible = false;
                      mobileNumberOtpBloc.visibleEmailAddressOptions.sink
                          .add(mobileNumberOtpBloc.isEmailFieldVisible);

                      // mobileNumberOtpBloc.otpFieldVisibleCtrl.sink.add(false);
                    },
                    style: AppTextStyle.access1(textColor: AppColors.appBlack)
                        .copyWith(height: 1),
                    textAlign: TextAlign.start,
                    keyboardType: TextInputType.number,
                    inputFormatters: <TextInputFormatter>[
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(10)
                    ],
                    decoration: const InputDecoration(
                      hintText: '',
                      border: InputBorder.none, // Removes underline
                      contentPadding: EdgeInsets.zero, // Removes padding
                      isDense: true, // Reduces height of the text form field
                    ),
                    // decoration: InputDecoration(
                    //   // prefix: const Text("+91",style:TextStyle(
                    //   //     fontSize: 18, fontFamily: AppConstants.rRegular, fontWeight: FontWeight.w700, color: AppColors.appBlack, letterSpacing: 4) ,),
                    //   prefixIcon: Container(
                    //       width: CommonMethods.textWidth(
                    //           context: context,
                    //           textStyle: AppTextStyle.usernameHeading(textColor: AppColors.writingBlack0)),
                    //       alignment: Alignment.center,
                    //       child:  Text(
                    //         "+91",
                    //         style: AppTextStyle.usernameHeading(textColor: AppColors.writingBlack0),
                    //       )),
                    //   enabledBorder: const UnderlineInputBorder(
                    //     borderSide: BorderSide(color: AppColors.appBlack),
                    //   ),
                    //   focusedBorder: const UnderlineInputBorder(
                    //     borderSide: BorderSide(color: AppColors.appBlack),
                    //   ),
                    //   border: const UnderlineInputBorder(
                    //     borderSide: BorderSide(color: AppColors.appBlack),
                    //   ),
                    // ),
                  ),
                ),
              ],
            ),
          ),
          //messaeg
          Container(
              margin: const EdgeInsets.only(top: 5, left: 5),
              child: Text(
                'The app is available only to Indian mobile users at the moment.',
                style: AppTextStyle.smallText(textColor: AppColors.brandBlack)
                    .copyWith(),
              ))
        ],
      ),
    );
  }

//endregion

  //region Email address
  Widget emailAddress() {
    return StreamBuilder<bool>(
      stream: mobileNumberOtpBloc.visibleEmailAddressOptions.stream,
      initialData: mobileNumberOtpBloc.isEmailFieldVisible,
      builder: (context, snapshot) {
        // If email field should be shown
        if (snapshot.data!) {
          return Container(
            margin: const EdgeInsets.only(top: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppStrings.shareUsYourEmail,
                  textAlign: TextAlign.start,
                  style:
                      AppTextStyle.access0(textColor: AppColors.writingBlack0),
                ),
                const SizedBox(
                  height: 10,
                ),
                Form(
                  key: mobileNumberOtpBloc.emailFormKey,
                  child: TextFormField(
                    key: const Key("login_email"),
                    controller: mobileNumberOtpBloc.emailAddressTextCtrl,
                    onChanged: (value) {
                      // Check if the email is complete (contains @ and .)
                      if (value.contains('@') &&
                          value.contains('.') &&
                          value.indexOf('.') > value.indexOf('@') &&
                          value.lastIndexOf('.') < value.length - 1) {
                        // If email looks complete, validate it
                        bool isValid = mobileNumberOtpBloc
                            .emailFormKey.currentState!
                            .validate();
                        // If valid, close the keyboard after a short delay
                        // This ensures the user has finished typing
                        // if (isValid) {
                        //   // Add a small delay to ensure typing is complete
                        //   Future.delayed(const Duration(milliseconds: 3000), () {
                        //     if (context.mounted) {
                        //       CommonMethods.closeKeyboard(context);
                        //     }
                        //   }
                        //   );
                        // }
                      }
                    },
                    onTap: () {
                      // Ensure the field maintains focus when tapped
                      FocusScope.of(context).requestFocus();

                      // Add a slight delay to ensure keyboard is fully shown
                      Future.delayed(const Duration(milliseconds: 100), () {
                        // Scroll to ensure the email field is visible
                        if (scrollController.hasClients) {
                          // Use a fixed position that ensures the email field is visible
                          // This is a more reliable approach than percentage-based scrolling
                          scrollController.animateTo(
                            200, // Fixed position that should show the email field
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.easeOut,
                          );
                        }
                      });
                    },
                    // Close keyboard when user explicitly submits
                    onEditingComplete: () {
                      // Validate the form when editing is complete
                      bool isValid = mobileNumberOtpBloc
                          .emailFormKey.currentState!
                          .validate();
                      // Close keyboard if validation passes, with a small delay
                      if (isValid) {
                        Future.delayed(const Duration(milliseconds: 300), () {
                          if (context.mounted) {
                            CommonMethods.closeKeyboard(context);
                          }
                        });
                      }
                    },
                    // Use done action to submit the field
                    textInputAction: TextInputAction.done,
                    autofocus: true,
                    style: AppTextStyle.access1(
                        textColor: AppColors.writingBlack0),
                    textAlign: TextAlign.start,
                    keyboardType: TextInputType.emailAddress,
                    decoration: InputDecoration(
                      filled: true, // Enables background fill
                      fillColor: AppColors.textFieldFill1, // Background color
                      contentPadding: const EdgeInsets.symmetric(
                          vertical: 10.0,
                          horizontal: 16.0), // Padding inside the field
                      border: OutlineInputBorder(
                        borderRadius:
                            BorderRadius.circular(10), // Rounded corners
                        borderSide: BorderSide.none, // No visible border
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                        borderSide: BorderSide.none, // No border when focused
                      ),
                    ),
                    validator: (value) {
                      if (value!.isEmpty) {
                        return 'Please enter an email';
                      }
                      if (!CommonMethods.validateEmail(value)) {
                        return 'Please enter a valid email';
                      }
                      return null; // Return null if the input is valid
                    },
                  ),
                ),
                const SizedBox(height: 20),
                // Send OTP Button
                SizedBox(
                  width: double.infinity,
                  child: StatefulBuilder(
                    builder: (context, setState) {
                      return CupertinoButton(
                        padding: const EdgeInsets.symmetric(vertical: 13),
                        color: AppColors.brandBlack,
                        borderRadius: BorderRadius.circular(10),
                        onPressed: mobileNumberOtpBloc.isLoading
                            ? null
                            : () async {
                                // First validate the form
                                if (mobileNumberOtpBloc
                                    .emailFormKey.currentState!
                                    .validate()) {
                                  try {
                                    // Set loading state
                                    mobileNumberOtpBloc.setLoading(true);
                                    setState(() {});

                                    // Close keyboard after validation passes and before sending OTP
                                    CommonMethods.closeKeyboard(context);

                                    // Send OTP and go to OTP verification screen
                                    await mobileNumberOtpBloc
                                        .goToLogInOtpScreen(
                                      email: mobileNumberOtpBloc
                                          .emailAddressTextCtrl.text,
                                      isGoogleLogin: false,
                                      isEmailOtp: true,
                                      isPhoneOtp: false,
                                    );
                                  } finally {
                                    if (mounted) {
                                      mobileNumberOtpBloc.setLoading(false);
                                      setState(() {});
                                    }
                                  }
                                }
                              },
                        child: mobileNumberOtpBloc.isLoading
                            ? AppCommonWidgets.inlineCircularProgress(size: 24)
                            : Text(
                                "Send OTP",
                                style: AppTextStyle.access0(
                                    textColor: AppColors.appWhite),
                              ),
                      );
                    },
                  ),
                ),

                // Go back button
                Align(
                  alignment: Alignment.centerLeft,
                  child: TextButton.icon(
                    onPressed: () {
                      // First reset the email text field
                      mobileNumberOtpBloc.emailAddressTextCtrl.clear();

                      // Hide email field
                      mobileNumberOtpBloc.isEmailFieldVisible = false;
                      mobileNumberOtpBloc.visibleEmailAddressOptions.sink
                          .add(false);

                      // Force UI refresh by rebuilding the widget
                      setState(() {});

                      // Ensure options are visible
                      Future.microtask(() {
                        // Make sure we're showing the sign-in options
                        if (!showAllOptions) {
                          // If all options weren't showing before, show just the Google button
                          showAllOptions = false;
                        }
                        // Trigger UI update
                        showAllOptionsCtrl.sink.add(showAllOptions);
                      });
                    },
                    icon: Icon(
                      Icons.arrow_back,
                      size: 16,
                      color: AppColors.appBlack,
                    ),
                    label: Text("Go back",
                        style: AppTextStyle.smallText(
                            textColor: AppColors.appBlack)),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      minimumSize: Size.zero,
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                  ),
                ),

                // Add extra padding at the bottom to ensure buttons are visible above keyboard
                const SizedBox(
                  height: 150, // Fixed height that works well on most devices
                ),
              ],
            ),
          );
        } else {
          // Return empty container when email field should not be shown
          return const SizedBox();
        }
      },
    );
  }

//endregion

//region Sign in and sign up button
  Widget signInAndSignUpButton() {
    return KeyboardVisibilityBuilder(
      builder: (context, isKeyBoardOpen) {
        // Don't show buttons when keyboard is open
        if (isKeyBoardOpen) {
          return const SizedBox();
        }

        // Check if email field is shown
        if (mobileNumberOtpBloc.isEmailFieldVisible) {
          return const SizedBox(); // Don't show buttons when email field is visible
        }

        // Show authentication options
        return StreamBuilder<bool>(
            stream: showAllOptionsCtrl.stream,
            initialData: showAllOptions,
            builder: (context, snapshot) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Connect with Google button with loading state
                    StreamBuilder<bool>(
                      stream: mobileNumberOtpBloc.googleSignInLoading,
                      initialData: false,
                      builder: (context, loadingSnapshot) {
                        bool isLoading = loadingSnapshot.data ?? false;

                        return Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: isLoading
                                ? AppColors.lightGreen2.withOpacity(0.7)
                                : AppColors.lightGreen2,
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(
                                color: isLoading
                                    ? AppColors.brandGreen.withOpacity(0.7)
                                    : AppColors.brandGreen,
                                width: 2),
                          ),
                          child: CupertinoButton(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            color: Colors.transparent,
                            borderRadius: BorderRadius.circular(10),
                            onPressed: isLoading
                                ? null
                                : () {
                                    // Trigger Google sign-in
                                    mobileNumberOtpBloc.googleSignIn();
                                  },
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                if (isLoading) ...[
                                  AppCommonWidgets.inlineCircularProgress(
                                      size:
                                          24), // Removed extra 'child:' keyword
                                ] else ...[
                                  SvgPicture.asset(
                                    AppImages.google,
                                    height: 24,
                                    width: 24,
                                  ),
                                  const SizedBox(width: 10),
                                  Text(
                                    "Connect with Google",
                                    textAlign: TextAlign.center,
                                    style: AppTextStyle.access0(
                                        textColor: AppColors.appBlack),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        );
                      },
                    ),

                    // Show additional options if showAllOptions is true
                    if (snapshot.data!) ...[
                      const SizedBox(height: 15),

                      // Email and OTP button
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: AppColors.textFieldFill1,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: CupertinoButton(
                          padding: const EdgeInsets.symmetric(vertical: 15),
                          color: AppColors.textFieldFill2,
                          borderRadius: BorderRadius.circular(10),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SvgPicture.asset(
                                AppImages.passwordFill,
                                height: 24,
                                width: 24,
                                color: AppColors.appBlack,
                              ),
                              const SizedBox(width: 10),
                              Text("Continue with Email and OTP",
                                  style: AppTextStyle.access0(
                                      textColor: AppColors.appBlack)),
                            ],
                          ),
                          onPressed: () {
                            // Show email field
                            mobileNumberOtpBloc.isEmailFieldVisible = true;
                            mobileNumberOtpBloc.visibleEmailAddressOptions.sink
                                .add(true);

                            // Add a slight delay to ensure the email field is rendered
                            Future.delayed(const Duration(milliseconds: 100),
                                () {
                              // Scroll to ensure the email field is visible
                              if (scrollController.hasClients) {
                                // Use a fixed position that ensures the email field is visible
                                // This is a more reliable approach than percentage-based scrolling
                                scrollController.animateTo(
                                  200, // Fixed position that should show the email field
                                  duration: const Duration(milliseconds: 300),
                                  curve: Curves.easeOut,
                                );
                              }
                            });
                          },
                        ),
                      ),

                      const SizedBox(height: 15),

                      // Explore as Guest button
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: AppColors.textFieldFill1,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: CupertinoButton(
                          padding: const EdgeInsets.symmetric(vertical: 13),
                          color: AppColors.textFieldFill1,
                          borderRadius: BorderRadius.circular(10),
                          child: Text(
                            "Explore as Guest",
                            style: AppTextStyle.access0(
                                textColor: AppColors.writingBlack1),
                          ),
                          onPressed: () {
                            _navigateAsGuest();
                          },
                        ),
                      ),
                    ] else ...[
                      const SizedBox(height: 10),

                      // See all options button
                      GestureDetector(
                        onTap: () {
                          // Toggle show all options
                          showAllOptions = true;
                          showAllOptionsCtrl.sink.add(showAllOptions);
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: Text(
                            "See all Options",
                            textAlign: TextAlign.center,
                            style: AppTextStyle.access0(
                                textColor: AppColors.writingBlack1),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              );
            });
      },
    );
  }
  //endregion

  // Navigate as guest user
  void _navigateAsGuest() {
    // Set static user flag
    AppConstants.isSignInScreenOpenedForStatisUser = true;

    // Navigate to home screen
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const NavigationRouter()),
      (Route<dynamic> route) => false,
    );
  }

// //region Continue and email button
//   Widget continueAndEmailButton() {
//     return KeyboardVisibilityBuilder(
//       builder: (context, isKeyBoardOpen) {
//         return StreamBuilder<SignInOptionState>(
//             stream: mobileNumberOtpBloc.signInOptionsStateCtrl.stream,
//             initialData: SignInOptionState.Failed,
//             builder: (context, snapshot) {
//
//               //If keyboard open then return a container
//               if(isKeyBoardOpen){
//                 return const SizedBox();
//               }
//               //Success
//               if (snapshot.data! == SignInOptionState.Success) {
//                 return Visibility(
//                   visible: !isKeyBoardOpen,
//                   child: Column(
//                     mainAxisSize: MainAxisSize.min,
//                     children: [
//                       Container(
//                         margin: const EdgeInsets.only(bottom: 30),
//                         width: double.infinity,
//                         child: CupertinoButton(
//                             key: const Key("get_and_verify_otp"),
//                             borderRadius: BorderRadius.circular(120),
//                             padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 15),
//                             color: AppColors.brandGreen,
//                             child:
//                             mobileNumberOtpBloc.emailAddressTextCtrl.text.isNotEmpty?
//                             const Text(
//                               "Continue",
//                               maxLines: 1,
//                               overflow: TextOverflow.ellipsis,
//                               style:
//                               TextStyle(fontFamily: "LatoBold", fontSize: 15, color: AppColors.appWhite, fontWeight: FontWeight.w700),
//                             )
//                             :
//                           mobileNumberOtpBloc.mobileNumberResponse.email == null
//                                 ? Text(
//                                     "Continue with Email",
//                                     maxLines: 1,
//                                     overflow: TextOverflow.ellipsis,
//                                     style:
//                                         const TextStyle(fontFamily: "LatoBold", fontSize: 15, color: AppColors.appWhite, fontWeight: FontWeight.w700),
//                                   )
//                                 : Text(
//                                     "Get OTP on ${CommonMethods().maskEmail(mobileNumberOtpBloc.mobileNumberResponse.email!)}",
//                                     maxLines: 1,
//                                     overflow: TextOverflow.ellipsis,
//                                     style:
//                                         const TextStyle(fontFamily: "LatoBold", fontSize: 15, color: AppColors.appWhite, fontWeight: FontWeight.w700),
//                                   ),
//                             onPressed: () {
//
//                               //If text field is not empty and valid then go to otp screen
//                               if(mobileNumberOtpBloc.emailAddressTextCtrl.text.isNotEmpty &&  mobileNumberOtpBloc.emailFormKey.currentState!.validate()){
//                                 mobileNumberOtpBloc.goToLogInOtpScreen(
//                                     email: mobileNumberOtpBloc.emailAddressTextCtrl.text, isGoogleLogin: false);
//                                 return;
//                               }
//
//                               mobileNumberOtpBloc.mobileNumberResponse.email == null
//                                   ? mobileNumberOtpBloc.visibleEmailAddressOptions.sink.add(true)
//                                   : mobileNumberOtpBloc.goToLogInOtpScreen(
//                                       email: mobileNumberOtpBloc.mobileNumberResponse.email!, isGoogleLogin: false);
//                             }),
//                       ),
//                     ],
//                   ),
//                 );
//               }
//
//               //Loading
//               if (snapshot.data! == SignInOptionState.Loading) {
//                 return Column(
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     Container(
//                         margin: const EdgeInsets.symmetric(vertical: 10),
//                         child: AppCommonWidgets.appCircularProgress()),
//                   ],
//                 );
//               }
//
//               return const SizedBox();
//             });
//       },
//     );
//   }
// //endregion

  ///Sign in button
}
