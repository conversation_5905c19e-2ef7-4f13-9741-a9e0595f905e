import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/shipping_history/shipping_history_response.dart';
import 'package:swadesic/services/shipping_history_services/shipping_history_services.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum ShippingHistoryState { Loading, Success, Failed, Empty }

class ShippingHistoryBloc {
  // region Common Variables
  BuildContext context;
  bool isAddUpdateFieldVisible = false;
  late ShippingHistoryService shippingHistoryService;
  late ShippingHistoryResponse shippingHistoryResponse;
  final String pNumber;
  final String orderNumber;
  int selectedHistoryId = 0;
  int? historyIndex;
  String currentDateTime = "";
  // endregion

  //region Controller
  final shippingHistoryCtrl =
      StreamController<ShippingHistoryState>.broadcast();
  final addButtonLoadingCtrl = StreamController<bool>.broadcast();

  //endregion

  //region Text Controller
  final titleTextCtrl = TextEditingController();
  final descTextCtrl = TextEditingController();

  //endregion

  // region | Constructor |
  ShippingHistoryBloc(this.context, this.pNumber, this.orderNumber);
  // endregion

  // region Init
  void init() {
    //Get current date time
    getCurrentDateTime();
    shippingHistoryService = ShippingHistoryService();
    getHistory(pNumber: pNumber);
  }
// endregion

  //region On tap Options
  void onTapOptions({required History history, required int index}) {
    historyIndex = index;
    shippingHistoryCtrl.sink.add(ShippingHistoryState.Success);
  }
  //endregion

  //region Get history
  getHistory({required String pNumber}) async {
    //region Try
    try {
      //shippingHistoryCtrl.sink.add(ShippingHistoryState.Loading);
      shippingHistoryResponse =
          await shippingHistoryService.getShippingHistory(pNumber: pNumber);
      //Add date time to AM and PM
      for (var data in shippingHistoryResponse.historyList!) {
        List<String> dateInfo = CommonMethods.dateTimeAmPm(date: data.date!);
        data.date = "${dateInfo[1]} ${dateInfo[2]}";
        //print(data.date);
      }
      shippingHistoryCtrl.sink.add(ShippingHistoryState.Success);
    }
    //endregion
    on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    } catch (error) {
      //print(error);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }
//endregion

  //region On tap Add and update
  void onTapAddUpdate() {
    selectedHistoryId = 0;
    isAddUpdateFieldVisible = true;
    shippingHistoryCtrl.sink.add(ShippingHistoryState.Success);
  }
//endregion

//region On tap Add
  void onTapAdd() {
    isAddUpdateFieldVisible = !isAddUpdateFieldVisible;
    addShippingHistory();
  }
//endregion

  //region Add shipping history
  addShippingHistory() async {
    //Check field
    if (titleTextCtrl.text.isEmpty || descTextCtrl.text.isEmpty) {
      return CommonMethods.toastMessage(AppStrings.fieldEmpty, context);
    }
    //region Try
    try {
      //Start loading
      addButtonLoadingCtrl.sink.add(true);

      await shippingHistoryService.addShippingHistory(
          description: descTextCtrl.text,
          orderNumber: orderNumber,
          pNumber: pNumber,
          title: titleTextCtrl.text);

      //Stop loading
      addButtonLoadingCtrl.sink.add(false);

      titleTextCtrl.clear();
      descTextCtrl.clear();
      //Get all history
      getHistory(pNumber: pNumber);
    }
    //endregion
    on ApiErrorResponseMessage {
      //Stop loading
      addButtonLoadingCtrl.sink.add(false);
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
            commonErrorMessage: true);
      }
      return;
    } catch (error) {
      //Stop loading
      addButtonLoadingCtrl.sink.add(false);
      //print(error);
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
            commonErrorMessage: true);
      }
      return;
    }
  }
//endregion

  //region On tap Delete
  deleteHistory({required int historyId}) async {
    //region Try
    try {
      //Close dialog box
      historyIndex = null;
      //Navigator.pop(editDeleteContext);
      await shippingHistoryService.deleteHistory(historyId: historyId);
      //Get all history
      await getHistory(pNumber: pNumber);
    }
    //endregion
    on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    } catch (error) {
      //print(error);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }
  //endregion

//region On tap edit
  void onTapEdit({required History history}) {
    //Hide add and update field
    isAddUpdateFieldVisible = false;
    //Close dialog box
    historyIndex = null;
    //Visible edit text field
    selectedHistoryId = history.shippingUpdateId!;
    //Screen refresh
    shippingHistoryCtrl.sink.add(ShippingHistoryState.Success);
    //Add data to text field
    addDataToTextField(title: history.title!, desc: history.description!);
  }
//endregion

  //region Save
  onTapSave() async {
    //Check field
    if (titleTextCtrl.text.isEmpty || descTextCtrl.text.isEmpty) {
      return CommonMethods.toastMessage(AppStrings.fieldEmpty, context);
    }
    //region Try
    try {
      //Refresh screen
      shippingHistoryCtrl.sink.add(ShippingHistoryState.Success);
      //Api call
      await shippingHistoryService.editHistory(
          historyId: selectedHistoryId,
          pNumber: pNumber,
          title: titleTextCtrl.text,
          description: descTextCtrl.text,
          orderNumber: orderNumber);
      //Clear Text fields
      titleTextCtrl.clear();
      descTextCtrl.clear();
      //Hide text fields
      isAddUpdateFieldVisible = false;
      //Clear selected history id
      selectedHistoryId = 0;
      //Get all history
      await getHistory(pNumber: pNumber);
    }
    //endregion
    on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    } catch (error) {
      //print(error);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }
  //endregion

//region Add data to the text fields
  void addDataToTextField({required String title, required String desc}) {
    titleTextCtrl.text = title;
    descTextCtrl.text = desc;
  }
//endregion

//region Current date time
  void getCurrentDateTime() {
    var now = DateTime.now();

    String formatDate = DateFormat('dd:MM:yyyy hh:mm:ss').format(now);

    List<String> data = CommonMethods.dateTimeAmPm(date: formatDate);

    currentDateTime = "${data[1]} ${data[2]}";

    //print("Current time is $currentDateTime");
  }
//endregion
}
