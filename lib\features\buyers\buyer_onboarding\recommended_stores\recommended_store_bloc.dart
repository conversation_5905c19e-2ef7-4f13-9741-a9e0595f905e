import 'dart:async';

import 'package:flutter/material.dart';
import 'package:swadesic/features/navigation/navigation_router.dart';

class RecommendedStoreBloc {
  // region Common Methods
  BuildContext context;
  List<int> storeId = [1, 2, 332, 333, 222, 3, 22, 32, 3, 22, 3, 22, 43, 34];
  //endregion

  //region Controller
  final selectStoreCtrl = StreamController<int>.broadcast();

  //endregion

  //region Text Controller
  TextEditingController nameTextCtrl = TextEditingController();

  //endregion

  // region | Constructor |
  RecommendedStoreBloc(this.context);
  // endregion

  // region Init
  void init() {}
  // endregion

  //region On Select Store
  void onSelectStore(int storeId) {
    selectStoreCtrl.sink.add(storeId);
  }
//endregion

  // region Go To Buttom navigation
  void goToBottomNavigation() async {
    Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const NavigationRouter()),
        (Route<dynamic> route) => false);
    // var screen =  UserBottomNavigation();
    // var route = MaterialPageRoute(builder: (context) => screen);
    // Navigator.push(context, route);
  }
// endregion
}
