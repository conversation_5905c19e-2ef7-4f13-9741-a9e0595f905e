import 'dart:async';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/app_config_data_model/app_config_data_model.dart';
import 'package:swadesic/features/seller/add_and_edit_image/add_and_edit_image_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/getProduct_image_response/get_seller_product_Image_response.dart';
import 'package:swadesic/model/product_images.dart';
import 'package:swadesic/services/get_product_and_image/get_product_and_image.dart';
import 'package:swadesic/services/seller_hide_delete_service/seller_hide_delete_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class SelectedImagePreviewBloc {
  // region Common Variables
  BuildContext context;
  late ProductAndImageServices productAndImageServices;
  ProductImageResponse? productImageResponse;
  int? productId;

  List<ProductImages> productImages = [];
  late SellerHideDeleteService sellerHideDeleteService;

  //Get app config reference from data model
  late AppConfigDataModel appConfigDataModel;

  // endregion

  //region Text Editing Controller
  TextEditingController brandNameTextCtrl = TextEditingController();

  //endregion

  //region Controller
  final selectedImageCtrl = StreamController<ProductImages>.broadcast();
  final gridViewRefreshCtrl = StreamController<bool>.broadcast();

  //endregion

  // region | Constructor |
  SelectedImagePreviewBloc(this.context, this.productId);

  // endregion

  // region Init
  void init() {
    appConfigDataModel =
        Provider.of<AppConfigDataModel>(context, listen: false);

    if (productId == null) {
      addImages();
    } else {
      productAndImageServices = ProductAndImageServices();
      getProductImages();
      sellerHideDeleteService = SellerHideDeleteService();
    }
  }

// endregion

  //region Add Images to the Model class
  addImages() {
    productImages.clear();
    if (productImageResponse != null) {
      // add all Network Images
      for (var image in productImageResponse!.data!) {
        productImages.add(ProductImages(
            XFile(""), image.productImage, image.productimageid!));
      }
    }

    // add All Local Images
    for (var image in AppConstants.multipleSelectedImage) {
      productImages.add(ProductImages(image, "", 0));
    }
    gridViewRefreshCtrl.sink.add(true);
  }

  //endregion

  //region Get Product Images
  void getProductImages() async {
    try {
      //print(productId);
      productImageResponse =
          await productAndImageServices.getOnlyProductImage("");
      //print(productImageResponse!.data!.length);
      await addImages();
      //editProductDetailCtrl.sink.add(EditProductDetailsState.Success);
    } on ApiErrorResponseMessage catch (error) {
      //editProductDetailCtrl.sink.add(EditProductDetailsState.Failed);
      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    } catch (error) {
      //editProductDetailCtrl.sink.add(EditProductDetailsState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }

  //endregion

//region Go back to Add And Edit Image Screen
  void goBackToAddAndEditImage() {
    int imageLimit = appConfigDataModel.appConfig!.productImageLimit.toInt();

    var screen = const AddAndEditImageScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      // If AppConstants.multipleSelectedImage is not empty
      if (AppConstants.multipleSelectedImage.isNotEmpty) {
        addImages();
      }
    });
  }

//endregion

  // region Delete Image
  void deleteImage(int imageId) async {
    try {
      //print(imageId);

      sellerHideDeleteService.deleteImage(imageId);
      getProductImages();
    } on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message.toString(), context);

      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);

      return;
    }
  }

// endregion

//region Go back to Add Product
//   void goBackToAddProduct(){
//     Navigator.pop(context);
//     Navigator.pop(context);
//   }
//endregion
}
