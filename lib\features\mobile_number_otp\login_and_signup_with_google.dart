import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/env.dart';
import 'package:swadesic/features/navigation/navigation_router.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/buyer_onboarding_new/initial_onboarding_screen.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/mobile_number_otp/login_otp/login_otp.dart';
import 'package:swadesic/features/mobile_number_otp/mobile_number_otp_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/app_data/app_data.dart';
import 'package:swadesic/model/login_response/sign_in_response.dart';
import 'package:swadesic/model/user_details_response/user_details_response.dart';
import 'package:swadesic/services/get_app_data_services/get_app_data_service.dart';
import 'package:swadesic/services/signin_services/signin_services.dart';
import 'package:swadesic/services/user_detail_services/user_detail_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class LoginAndSignupWithGoogle {
  //region Common variable
  late final BuildContext context;
  late GoogleSignIn googleSignIn;
  final String phoneNumber;
  String email = "";
  GoogleSignInAccount? currentUser;
  final MobileNumberOtpBloc mobileNumberOtpBloc;
  final StreamController isButtonLoadingCtrl;
  final bool isRegisterUser;

//endregion

  //region Constructor
  LoginAndSignupWithGoogle(this.context, this.phoneNumber,
      this.mobileNumberOtpBloc, this.isButtonLoadingCtrl, this.isRegisterUser) {
    //Add clint id only if view is web
    googleSignIn = GoogleSignIn(
      clientId: (kIsWeb &&
              AppConstants.appCurrentEnvironment == Environment.dev)
          ? AppConstants.devGoogleClintId
          : (kIsWeb && AppConstants.appCurrentEnvironment == Environment.prod)
              ? AppConstants.prodGoogleClintId
              : null,
      scopes: ['email'],
    );
  }

//endregion

  //region Open google login dialog
  Future<void> openGoogleLoginDialog() async {
    try {
      //Logout
      logOut();
      //Loading
      isButtonLoadingCtrl.sink.add(true);
      //Open google account options
      final GoogleSignInAccount? googleUser = await googleSignIn.signIn();
      //Take out access token
      final auth = await googleUser!.authentication;
      debugPrint("Google account access token is ${auth.accessToken}");
      //Save email from google account
      email = googleUser.email;
      //Google login api call
      await googleLoginApiCall(
          phoneNumber: phoneNumber, accessToken: auth.accessToken!);
      //Stop Loading
      isButtonLoadingCtrl.sink.add(false);
    } catch (error) {
      debugPrint('Sign in failed: $error');
      //Stop Loading
      isButtonLoadingCtrl.sink.add(false);
    }
  }

  //endregion

  //region Log out
  Future<void> logOut() async {
    // await googleSignIn.disconnect();
    await googleSignIn.signOut();
  }

//endregion

  //region Google login api call
  Future<void> googleLoginApiCall(
      {required String phoneNumber, required String accessToken}) async {
    try {
      //Body
      Map<String, dynamic> body = {
        "phonenumber": "$phoneNumber",
        "access_token": accessToken
      };
      //Google login
      SignInResponse googleSignInResponse =
          await SignInServices().googleLoginRequest(body: body);
      //Check login status
      checkGoogleLoginStatus(
          googleSignInResponse: googleSignInResponse, accessToken: accessToken);
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message!, context,
              toastShowTimer: 5)
          : null;
    } catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
    }
  }

//endregion

  //region Check google login status
  void checkGoogleLoginStatus(
      {required SignInResponse googleSignInResponse,
      required String accessToken}) async {
    /// 1
    //If first time user is signing up (If user info is null then push it to OTP screen)
    if (googleSignInResponse.loginUserInfo == null) {
      //Go to otp screen
      goToLogInOtpScreen(
        googleAccessToken: accessToken,
        isPhoneOtp: googleSignInResponse.sentPhoneOtp!,
        isEmailOtp: googleSignInResponse.sentEmailOtp!,
        phoneNumber: phoneNumber,
      );
    }

    /// 2
    //If user info is not null and having data (Then check is user profile is completed or not ,if yes then push to Home else push to complete user onboarding)
    else if (googleSignInResponse.loginUserInfo != null &&
        googleSignInResponse.loginUserInfo!.userReference != null) {
      //
      //Check user profile is completed then go to User Home screen
      if (googleSignInResponse.loginUserInfo!.profileComplete!) {
        //Save access token and all info to share preference and global variable
        await saveUserInfoInGlobalAndSharePref(
            googleSignInResponse: googleSignInResponse);
        //print(AppConstants.appData);
        //Go to buyer bottom navigation
        goToBottomNavigation();
      }
      // If user profile is not completed then go to Buyer onboarding
      else {
        //Save access token and all info to share preference and global variable
        await saveUserInfoInGlobalAndSharePref(
            googleSignInResponse: googleSignInResponse);
        //Go to user onboarding
        goToBuyerOnBoarding(
            userReference: googleSignInResponse.loginUserInfo!.userReference!);
      }
    }
  }

  //endregion

  ///Save user info in global variable and in share preference
  //region Save info in global variable and share preference
  Future<void> saveUserInfoInGlobalAndSharePref(
      {required SignInResponse googleSignInResponse}) async {
    // AppConstants.appData = AppData();
    //Add user id
    AppConstants.appData.userId = googleSignInResponse.loginUserInfo!.userid!;
    //Add user reference
    AppConstants.appData.userReference =
        googleSignInResponse.loginUserInfo!.userReference!;
    //Add access token
    AppConstants.appData.accessToken =
        googleSignInResponse.loginTokenInfo!.access!;
    //Add access token expire
    AppConstants.appData.accessTokenExpire =
        googleSignInResponse.loginTokenInfo!.accessTokenValidity!;
    //Add refresh token
    AppConstants.appData.refreshToken =
        googleSignInResponse.loginTokenInfo!.refresh!;
    //Add refresh token expire
    AppConstants.appData.refreshExpire =
        googleSignInResponse.loginTokenInfo!.refreshTokenValidity!;
    //Mark user view is true
    AppConstants.appData.isUserView = true;
    //Mark store view is false
    AppConstants.appData.isStoreView = false;
    //Pin code
    AppConstants.appData.pinCode = googleSignInResponse.loginUserInfo!.pinCode;
    //Base url
    // AppConstants.appData.baseUrl = AppConstants.appCurrentEnvironment == Environment.dev ?
    // "https://e2e-77-175.ssdcloudindia.net/dev"
    //     : "https://e2e-65-177.ssdcloudindia.net/prod";
    //Add all data to share pref
    await AppDataService().addAppData();
  }

//endregion

  //region Go to login otp screen
  void goToLogInOtpScreen(
      {required String phoneNumber,
      required bool isEmailOtp,
      required bool isPhoneOtp,
      required String googleAccessToken}) {
    var screen = LoginOtpScreen(
      phoneNumber: phoneNumber,
      isEmailOtp: isEmailOtp,
      isPhoneOtp: isPhoneOtp,
      googleAccessToken: googleAccessToken,
      isRegisterUser: isRegisterUser,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

  //region Go to Bottom Navigation
  void goToBottomNavigation() async {
    await getLoggedInUserDetail();
    //If non logged in user flow opened from Static user
    if (AppConstants.isSignInScreenOpenedForStatisUser) {
      Navigator.of(AppConstants.userStoreCommonBottomNavigationContext)
          .popUntil((route) => route.isFirst);
      // If user came from profile tab, redirect to profile
      if (AppConstants.isSignInScreenOpenedFromProfileTab) {
        AppConstants.userPersistentTabController.jumpToTab(4); // Profile tab
        AppConstants.isSignInScreenOpenedFromProfileTab = false; // Reset flag
      }
      // Refresh bottom navigation to update screens
      AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
      // AppConstants.isLoggedIn.value = true;
      // //Make the bottom navigation visible
      // //Update bottom navigation
      // AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
      return;
    }
    Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const NavigationRouter()),
        (Route<dynamic> route) => false);
  }
  //endregion

  //region Get logged in user detail
  Future<void> getLoggedInUserDetail() async {
    late LoggedInUserInfoDataModel loggedInUserInfoDataModel =
        Provider.of<LoggedInUserInfoDataModel>(context, listen: false);
    //Get reference to Product data model
    var productDataModel =
        Provider.of<ProductDataModel>(context, listen: false);
    //region Try
    try {
      //selectedAddressRefreshCtrl.sink.add(SellerReturnWarrantyState.Loading);
      GetUserDetailsResponse userDetailsResponse = await UserDetailsServices()
          .getLoggedInUserDetail(
              userReference: AppConstants.appData.userReference!);

      ///Add user info to logged in user data model
      loggedInUserInfoDataModel.setUserInfoResponse(
          data: userDetailsResponse.userDetail!);
      //Update the buy button to refresh in all loaded product
      for (var product in productDataModel.allProducts) {
        product.isPinCodeChanged = true;
      }
      //Update ui
      productDataModel.updateUi();
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      debugPrint(error.message);
    } catch (error) {
      debugPrint(error.toString());
    }
  }
  //endregion

  //region Go to Buyer on boarding screen
  void goToBuyerOnBoarding({required String userReference}) {
    var screen = InitialOnboardingScreen(userReference: userReference);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion
}
