import 'package:app_links/app_links.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:swadesic/features/faq/faq_navigation.dart';
import 'package:swadesic/services/get_app_data_services/get_app_data_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/universal_link/comment_link.dart';
import 'package:swadesic/util/universal_link/external_review_link.dart';
import 'package:swadesic/util/universal_link/post_link.dart';
import 'package:swadesic/util/universal_link/product_link.dart';
import 'package:swadesic/util/universal_link/store_and_user_link.dart';
import 'package:swadesic/util/universal_link/store_external_review_link.dart';

class HandleUrl {
  //region Constructor
  HandleUrl() {
    //If web
    if (kIsWeb) {
      //If web
      if (AppConstants.webChangedUrl.isNotEmpty &&
          Uri.parse(AppConstants.webChangedUrl).pathSegments.isNotEmpty) {
        debugPrint(
            "In HandleUrl we get the incoming link: ${AppConstants.webChangedUrl}");
        handleAndNavigate(url: Uri.parse(AppConstants.webChangedUrl));
      }
    } else {
      // Initialize URL listener
      urlListener();
    }
  }

  /// Handles initial app link when the app is launched from a cold start
  static Future<void> handleInitialNavigation() async {
    if (!kIsWeb) {
      try {
        final appLinks = AppLinks();
        Uri? initialLink = await appLinks.getInitialAppLink();
        if (initialLink != null && initialLink.pathSegments.isNotEmpty) {
          debugPrint("Handling initial app link: $initialLink");
          // Small delay to ensure the app is fully initialized
          await Future.delayed(const Duration(seconds: 1));
          await HandleUrl().handleAndNavigate(url: initialLink);
        }
      } catch (e) {
        debugPrint("Error handling initial link: $e");
      }
    }
  }
  //endregion

  //region Url listener
  /// Initializes the `AppLinks` instance and listens for app link events.
  ///
  /// This method is responsible for setting up the necessary infrastructure to handle app link navigation.
  /// It creates an instance of `AppLinks` and listens for any incoming app link events using the `allUriLinkStream`.
  /// When an app link event is received, the method calls the `handleAndNavigate()` function to process the link and navigate the user accordingly.
  void urlListener() async {
    final appLinks = AppLinks();

    // Handle initial link (app cold start)
    if (!AppConstants.isAppOpen) {
      try {
        Uri? initialLink = await appLinks.getInitialAppLink();
        if (initialLink != null && initialLink.pathSegments.isNotEmpty) {
          debugPrint("Handling initial app link: $initialLink");
          // Small delay to ensure the app is fully initialized
          await Future.delayed(const Duration(seconds: 1));
          await handleAndNavigate(url: initialLink);
        }
      } catch (e) {
        debugPrint("Error handling initial link: $e");
      }
    }

    // Listen for subsequent links (app in background/foreground)
    appLinks.uriLinkStream.listen((link) async {
      if (link.pathSegments.isNotEmpty) {
        debugPrint("Handling app link while app is open: $link");
        await handleAndNavigate(url: link);
      }
    });

    AppConstants.isAppOpen = true;
  }
  //endregion

  /// Ensures navigation context is available before attempting navigation
  Future<void> _ensureNavigationContextAvailable() async {
    int attempts = 0;
    const maxAttempts = 10;
    const delayBetweenAttempts = Duration(milliseconds: 100);

    while (attempts < maxAttempts) {
      try {
        // Check if currentSelectedTabContext is available
        if (AppConstants.currentSelectedTabContext.mounted) {
          debugPrint("Navigation context is available");
          return;
        }
      } catch (e) {
        // Context not available yet
      }

      // Check if bottom navigation is mounted and try to get context from it
      if (AppConstants.isBottomNavigationMounted.value) {
        try {
          if (AppConstants.userStoreCommonBottomNavigationContext.mounted) {
            // Use the bottom navigation context as fallback
            AppConstants.currentSelectedTabContext = AppConstants.userStoreCommonBottomNavigationContext;
            debugPrint("Using bottom navigation context as fallback");
            return;
          }
        } catch (e) {
          // Bottom navigation context not available yet
        }
      }

      attempts++;
      await Future.delayed(delayBetweenAttempts);
    }

    debugPrint("Warning: Navigation context not available after $maxAttempts attempts");
  }

  /// Safely execute navigation with context, avoiding async gap issues
  void _safeNavigateWithContext(Function(BuildContext) navigationAction) {
    try {
      final context = AppConstants.userStoreCommonBottomNavigationContext;
      if (context.mounted) {
        navigationAction(context);
      } else {
        debugPrint("Navigation context is not mounted");
      }
    } catch (e) {
      debugPrint("Error executing navigation: $e");
    }
  }

  ///Handle web app url
  //region Handle app url
  Future<void> handleAndNavigate({required Uri url}) async {
    Uri completeDecodedUrl = Uri();

    //Decode url path segment if the path segment is encoded and not empty
    if (url.pathSegments.isNotEmpty &&
        CommonMethods.canDecodeBase32(url.pathSegments.first)) {
      completeDecodedUrl = Uri.parse(
          "https://xyz.com/${CommonMethods.decodeBase32(url.pathSegments.first)}");
    } else {
      completeDecodedUrl = url;
    }

    try {
      await Future.delayed(const Duration(seconds: 1));

      // Ensure navigation context is available before proceeding
      await _ensureNavigationContextAvailable();

      ///0. Check is it a web url
      if (url.toString().contains("/web")) {
        //print(url.toString());
        _safeNavigateWithContext((context) {
          CommonMethods.openAppWebView(webUrl: url.toString(), context: context);
        });
        return;
      }

      ///0.1. Check if it's an FAQ URL (check original URL first, not decoded)
      if (url.pathSegments.isNotEmpty && url.pathSegments.first == 'faq') {
        _safeNavigateWithContext((context) {
          FaqNavigation.handleFaqLink(context, url);
        });
        return;
      }

      //Check if the url has 'ic' parameter (both original and decoded URLs)
      if (url.queryParameters.containsKey('ic') &&
          url.queryParameters['ic'] != null) {
        //Save referral code from original URL
        AppDataService()
            .saveReferralCode(referralCode: url.queryParameters['ic']!);
      } else if (completeDecodedUrl.queryParameters.containsKey('ic') &&
          completeDecodedUrl.queryParameters['ic'] != null) {
        //Save referral code from decoded URL
        AppDataService().saveReferralCode(
            referralCode: completeDecodedUrl.queryParameters['ic']!);
      }

      ///1. Handle new URL format: Store handle with product slug
      /// Format: https://lol.swadesic.com/kitten_kreeps/product/kitten-computer
      if (url.pathSegments.length == 3 &&
          url.pathSegments[1] == 'p' &&
          url.pathSegments[2].isNotEmpty) {
        ProductSlugLink(url.pathSegments[0], url.pathSegments[2].split('&')[0]);
        return;
      }

      ///2. Handle new URL format: Store handle with external review request
      /// Format: https://lol.swadesic.com/kitten_kreeps/e-review-request/TOKEN
      if (url.pathSegments.length == 3 &&
          url.pathSegments[1] == 'e-review-request' &&
          url.pathSegments[2].isNotEmpty) {
        String token = url.pathSegments[2];
        // For external review links, we need to decode the token to get the parameters
        try {
          String decodedToken = CommonMethods.decodeBase32(token);
          Uri tokenUri = Uri.parse("https://xyz.com$decodedToken");

          // Check for product external review request (has 'pr' parameter)
          if (tokenUri.queryParameters.containsKey('t') &&
              tokenUri.queryParameters.containsKey('pr') &&
              tokenUri.queryParameters.containsKey('ur')) {
            String actualToken = tokenUri.queryParameters['t']!;
            String productReference = tokenUri.queryParameters['pr']!;
            String userReference = tokenUri.queryParameters['ur']!;

            ExternalReviewLink(actualToken,
                productReference: productReference,
                userReference: userReference);
            return;
          }
          // Check for store external review request (has 'sr' parameter)
          else if (tokenUri.queryParameters.containsKey('t') &&
              tokenUri.queryParameters.containsKey('sr') &&
              tokenUri.queryParameters.containsKey('ur')) {
            String actualToken = tokenUri.queryParameters['t']!;
            String storeReference = tokenUri.queryParameters['sr']!;
            String userReference = tokenUri.queryParameters['ur']!;

            // Handle store external review with dedicated class
            StoreExternalReviewLink(actualToken,
                storeReference: storeReference, userReference: userReference);
            return;
          }
        } catch (e) {
          // If decoding fails, treat as invalid link
          debugPrint("Failed to decode external review token: $e");
        }
      }

      ///3. Handle Base32 encoded URLs (Post, Comment, Reply)
      /// Format: https://lol.swadesic.com/BASE32_ENCODED_STRING
      if (url.pathSegments.length == 1 &&
          CommonMethods.canDecodeBase32(url.pathSegments.first)) {
        String decodedData = CommonMethods.decodeBase32(url.pathSegments.first);
        debugPrint("Decoded data: $decodedData");

        try {
          // Parse the decoded data as a query string to extract parameters
          // The decoded data format is: "d?r=REFERENCE&ic=INVITE_CODE"
          Uri decodedUri = Uri.parse("https://temp.com/$decodedData");

          // Check if it contains the 'r' parameter (reference)
          if (decodedUri.queryParameters.containsKey('r')) {
            String reference = decodedUri.queryParameters['r']!;
            debugPrint("Extracted reference: $reference");

            // Check if it's a post reference (starts with PO)
            if (reference.startsWith('PO')) {
              PostLink(reference);
              return;
            }
            // Check if it's a comment or reply reference (starts with CO)
            // Both comments and replies use CO prefix and are handled by the same screen
            else if (reference.startsWith('CO')) {
              PostLink(
                  reference); // Comments and replies are handled by PostLink/SinglePostViewScreen
              return;
            }
            // Check if it's a product reference (starts with P but not PO)
            else if (reference.startsWith('P') && !reference.startsWith('PO')) {
              ProductLink(reference);
              return;
            }
            // If it's any other type of reference, try to handle it as a post/comment
            else {
              PostLink(reference);
              return;
            }
          }
        } catch (e) {
          debugPrint("Error parsing decoded data as query string: $e");
          // Fallback to old logic for backward compatibility
        }

        // Fallback: Check if decoded data directly starts with reference (old format)
        if (decodedData.startsWith('PO')) {
          PostLink(decodedData);
          return;
        }
        // Check if it's a comment or reply reference (starts with CO)
        else if (decodedData.startsWith('CO')) {
          PostLink(decodedData);
          return;
        }
        // Check if it's a product reference (starts with P but not PO)
        else if (decodedData.startsWith('P') && !decodedData.startsWith('PO')) {
          ProductLink(decodedData);
          return;
        }
        // If it's any other type of reference, try to handle it as a post/comment
        else {
          PostLink(decodedData);
          return;
        }
      }

      ///4. Handle legacy format: Post with query parameter
      if (completeDecodedUrl.queryParameters.containsKey('r') &&
          completeDecodedUrl.queryParameters['r'] != null &&
          completeDecodedUrl.queryParameters['r']!.isNotEmpty &&
          completeDecodedUrl.queryParameters['r']!.startsWith('PO')) {
        PostLink(completeDecodedUrl.queryParameters['r']!);
        return;
      }

      ///5. Handle legacy format: Product with query parameter
      else if (completeDecodedUrl.queryParameters.containsKey('r') &&
          completeDecodedUrl.queryParameters['r'] != null &&
          completeDecodedUrl.queryParameters['r']!.isNotEmpty &&
          completeDecodedUrl.queryParameters['r']!.startsWith('P')) {
        ProductLink(completeDecodedUrl.queryParameters['r']!);
        return;
      }

      ///6. Handle legacy format: External Review (Firebase deep link)
      // Product external review
      else if (completeDecodedUrl.queryParameters.containsKey('pr') &&
          completeDecodedUrl.queryParameters.containsKey('t') &&
          (completeDecodedUrl.queryParameters.containsKey('ur'))) {
        // Get the token from the URL
        String token = completeDecodedUrl.queryParameters['t']!;
        String productReference = completeDecodedUrl.queryParameters['pr']!;
        String userReference = completeDecodedUrl.queryParameters['ur']!;

        // Handle product external review link with the token
        ExternalReviewLink(token,
            productReference: productReference, userReference: userReference);
        return;
      }
      // Store external review
      else if (completeDecodedUrl.queryParameters.containsKey('sr') &&
          completeDecodedUrl.queryParameters.containsKey('t') &&
          (completeDecodedUrl.queryParameters.containsKey('ur'))) {
        // Get the token from the URL
        String token = completeDecodedUrl.queryParameters['t']!;
        String storeReference = completeDecodedUrl.queryParameters['sr']!;
        String userReference = completeDecodedUrl.queryParameters['ur']!;

        // Handle store external review link with the token
        StoreExternalReviewLink(token,
            storeReference: storeReference, userReference: userReference);
        return;
      }

      ///7. Handle new URL format: Store or User handle (with or without query parameters)
      /// Format: https://lol.swadesic.com/kitten_kreeps/?ic=SWSKITTCC
      /// Format: https://lol.swadesic.com/manoj_subramanyam/?ic=SWSKITTCC
      else if (url.pathSegments.isNotEmpty) {
        String handleName = url.pathSegments.first;
        bool isSuperLink =
            url.path.contains('super_link') || url.path.contains('super_link/');

        StoreAndUserLink(
            handleAndUserName: handleName, isSuperLink: isSuperLink);
        return;
      }

      ///8. Fallback: Handle decoded URL store or user
      else {
        StoreAndUserLink(
            handleAndUserName: completeDecodedUrl.pathSegments.first,
            isSuperLink: (completeDecodedUrl.path.contains('super_link') ||
                completeDecodedUrl.path.contains('super_link/')));
      }
    } catch (error) {
      debugPrint("Error handling URL: $error");
    }
  }
//endregion
}
