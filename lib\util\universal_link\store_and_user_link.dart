import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/invalid/invalid_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/super_link/super_link_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/services/store_and_user_reference_services/store_and_user_reference_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class StoreAndUserLink {
  String storeAndUserReference = "";
  String handleAndUserName = "";
  bool isSuperLink;

  StoreAndUserLink({required this.handleAndUserName, this.isSuperLink = false}) {
    getStoreAndUserReference();
  }

  //region Get Store and user reference
  Future<void> getStoreAndUserReference() async {
    try {
      String reference = await StoreAndUserReferenceServices().getStoreAndUserReferences(handleAndUserName: handleAndUserName);

      //If empty reference
      if (reference.isEmpty) {
        invalidScreen();
      } else {
        storeAndUserReference = reference;
        action();
      }
    } catch (error) {
      invalidScreen();
    }
  }

//endregion

  //region Push to Invalid screen
  void invalidScreen() {
    StatefulWidget screen;
    screen = const InvalidScreen(isAppBarVisible: true);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.currentSelectedTabContext, route);
  }

  //endregion

  //region Action
  void action() async {
    // Switch to appropriate tab before navigating
    _switchToAppropriateTab();

    //Push to the store or user screen
    StatefulWidget screen;

    //If super link
    if (storeAndUserReference.startsWith("S") && isSuperLink) {
      screen = SuperLink(handle: handleAndUserName);
      var route = MaterialPageRoute(builder: (context) => screen);
      Navigator.push(AppConstants.currentSelectedTabContext, route);
    }
    else {
      screen = storeAndUserReference.startsWith("S")
          ? BuyerViewStoreScreen(storeReference: storeAndUserReference, initialTapIndex: 1)
          : UserProfileScreen(userReference: storeAndUserReference);
      var route = MaterialPageRoute(builder: (context) => screen);
      Navigator.push(AppConstants.currentSelectedTabContext, route);
    }
  }

  /// Switch to the appropriate tab for store/user content
  void _switchToAppropriateTab() {
    try {
      if (storeAndUserReference.startsWith("S")) {
        // Store content - should open in appropriate tab based on view
        if (AppConstants.appData.isUserView ?? true) {
          // User view: switch to Home tab (index 0) for store viewing
          AppConstants.userPersistentTabController.jumpToTab(0);
        } else if (AppConstants.appData.isStoreView ?? false) {
          // Store view: switch to Profile tab (index 5) for store management
          AppConstants.storePersistentTabController.jumpToTab(5);
        }
      } else {
        // User profile content - should open in Profile tab for both views
        if (AppConstants.appData.isUserView ?? true) {
          // User view: switch to Profile tab (index 4)
          AppConstants.userPersistentTabController.jumpToTab(4);
        } else if (AppConstants.appData.isStoreView ?? false) {
          // Store view: switch to Profile tab (index 5)
          AppConstants.storePersistentTabController.jumpToTab(5);
        }
      }
    } catch (e) {
      debugPrint("Error switching to appropriate tab for store/user: $e");
    }
  }
//endregion
}
