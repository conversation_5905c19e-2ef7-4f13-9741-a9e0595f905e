import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/invalid/invalid_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/super_link/super_link_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/services/store_and_user_reference_services/store_and_user_reference_services.dart';
import 'package:swadesic/util/app_constants.dart';

class StoreAndUserLink {
  String storeAndUserReference = "";
  String handleAndUserName = "";
  bool isSuperLink;

  StoreAndUserLink({required this.handleAndUserName, this.isSuperLink = false}) {
    getStoreAndUserReference();
  }

  //region Get Store and user reference
  Future<void> getStoreAndUserReference() async {
    try {
      String reference = await StoreAndUserReferenceServices().getStoreAndUserReferences(handleAndUserName: handleAndUserName);

      //If empty reference
      if (reference.isEmpty) {
        invalidScreen();
      } else {
        storeAndUserReference = reference;
        action();
      }
    } catch (error) {
      invalidScreen();
    }
  }

//endregion

  //region Push to Invalid screen
  void invalidScreen() {
    StatefulWidget screen;
    screen = const InvalidScreen(isAppBarVisible: true);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.currentSelectedTabContext, route);
  }

  //endregion

  //region Action
  void action() async {
    //Push to the store or user screen in current tab for immediate visibility
    StatefulWidget screen;

    //If super link
    if (storeAndUserReference.startsWith("S") && isSuperLink) {
      screen = SuperLink(handle: handleAndUserName);
      var route = MaterialPageRoute(builder: (context) => screen);
      Navigator.push(AppConstants.currentSelectedTabContext, route);
    }
    else {
      screen = storeAndUserReference.startsWith("S")
          ? BuyerViewStoreScreen(storeReference: storeAndUserReference, initialTapIndex: 1)
          : UserProfileScreen(userReference: storeAndUserReference);
      var route = MaterialPageRoute(builder: (context) => screen);
      Navigator.push(AppConstants.currentSelectedTabContext, route);
    }
  }
//endregion
}
