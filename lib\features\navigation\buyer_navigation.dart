import 'package:flutter/material.dart';
import 'package:swadesic/features/navigation/core/base_navigation.dart';
import 'package:swadesic/features/navigation/widgets/custom_bottom_nav.dart';
import 'package:swadesic/features/navigation/config/navigation_actions.dart';
import 'package:swadesic/features/navigation/config/navigation_config.dart';
import 'package:swadesic/features/post/add_post/add_post_screen.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/auto_hide_navigation_service.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/user_or_store_notification.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/all_store_notification.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/model/user_details_response/user_details_response.dart';
import 'package:swadesic/util/common_methods.dart';

/// Buyer navigation implementation with Home, Search, Notifications, Add + Profile tabs
class BuyerNavigation extends BaseNavigation {
  final bool isFromOnboardingFlow;

  const BuyerNavigation({
    super.key,
    this.isFromOnboardingFlow = false,
  });

  @override
  State<BuyerNavigation> createState() => _BuyerNavigationState();
}

class _BuyerNavigationState extends BaseNavigationState<BuyerNavigation> {
  // Cache provider data to avoid repeated lookups
  UserOrStoreNotificationDataModel? _cachedUserNotificationModel;
  AllStoreNotificationDataModel? _cachedStoreNotificationModel;

  @override
  void initState() {
    super.initState();
    // Set up app constants for buyer navigation
    AppConstants.userStoreCommonBottomNavigationContext = context;
    AppConstants.isBottomNavigationMounted.value = true;
    AppConstants.userPersistentTabController.index = 0;

    // Set the internal currentIndex to Home tab (0) for buyer navigation
    final initialIndex = _getInitialTabIndex();
    currentIndex = initialIndex;
    debugPrint('BuyerNavigation: Setting initial tab index to $initialIndex');

    // Initialize navigation actions with context and navigator keys
    NavigationActions.initialize(context, navigatorKeys);

    // Cache provider data once to avoid repeated lookups
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _cacheProviderData();
      }
    });
  }

  /// Get the initial tab index for buyer navigation
  /// User accounts should start on Home tab (index 0)
  int _getInitialTabIndex() {
    // Check if we have a stored tab index from account switching
    final storedIndex = AppConstants.userPersistentTabController.index;

    // If the stored index is 0 (Home), use it, otherwise default to Home
    if (storedIndex == 0) {
      return 0;
    }

    // For user accounts, default to Home tab
    return 0; // Home tab index
  }

  void _cacheProviderData() {
    try {
      _cachedUserNotificationModel =
          Provider.of<UserOrStoreNotificationDataModel>(context, listen: false);
      _cachedStoreNotificationModel =
          Provider.of<AllStoreNotificationDataModel>(context, listen: false);
    } catch (e) {
      // Handle provider not found gracefully
      debugPrint('Provider data not available: $e');
    }
  }

  @override
  List<Widget> get tabPages {
    // Get tab screens from centralized configuration
    final configScreens = NavigationConfig.getTabScreens(NavigationType.buyer);
    // Add profile screen
    configScreens.add(NavigationActions.buildBuyerProfileScreen());
    return configScreens;
  }

  @override
  Widget buildCustomBottomNav() {
    return CustomBottomNav(
      currentIndex: currentIndex,
      onTabSelected: (index) => _handleTabSelected(index),
      onDoubleTap: (index) => _handleDoubleTap(index),
      onHorizontalSwipe: onHorizontalSwipe,
      onLongPress: (index) => _handleLongPress(index),
      onIconSwipe: (index) => _handleIconSwipe(index),
      tabIcons: NavigationConfig.getTabIcons(NavigationType.buyer),
      tabIconsFilled:
          NavigationConfig.getTabIcons(NavigationType.buyer, filled: true),
      tabNames: NavigationConfig.getTabNames(NavigationType.buyer),
      profileWidget: Consumer<LoggedInUserInfoDataModel>(
        builder: (context, userModel, _) => ProfileAvatar(
          isSelected: currentIndex == 4,
          isStore: false,
          imageUrl: userModel.userDetail?.icon,
          key: ValueKey<String>(
              'profile_avatar_${userModel.userDetail?.userid}'), // Force rebuild on user change
        ),
      ),
    );
  }

  // Gesture handlers using centralized configuration
  void _handleTabSelected(int index) {
    if (index == 3) {
      if (CommonMethods().isStaticUser()) {
        return CommonMethods().goToSignUpFlow();
      }
      // Add button tapped - push AddPostScreen with current tab's context
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const AddPostScreen(),
          fullscreenDialog: true,
        ),
      );
      // Don't change the tab when adding a post
      return;
    } else if (index < 4) {
      // Update the persistent tab controller
      AppConstants.userPersistentTabController.index = index;

      NavigationConfig.executeGesture(
          NavigationType.buyer, index, GestureType.tap);
    }
    onTabSelected(index);
  }

  void _handleDoubleTap(int index) {
    if (index < 4) {
      NavigationConfig.executeGesture(
          NavigationType.buyer, index, GestureType.doubleTap);
    } else {
      // Profile tab double tap
      NavigationActions.doubleTapAction(
          NavigationActions.buildBuyerProfileScreen(), 4);
    }
    // resetTab(index);
  }

  void _handleLongPress(int index) {
    if (index < 4) {
      NavigationConfig.executeGesture(
          NavigationType.buyer, index, GestureType.longPress);
    } else {
      // Profile tab long press
      NavigationActions.buyerProfileLongPress();
    }
  }

  void _handleIconSwipe(int index) {
    if (index < 4) {
      NavigationConfig.executeGesture(
          NavigationType.buyer, index, GestureType.swipe);
    }
    onIconSwipe(index);
  }

  @override
  void onProfileTabSelected() {
    // After Profile switching behavior: show Home tab displaying Feed page
    // This means when user taps profile and then comes back, they see Home tab
    // The profile tab itself will be shown, but this sets up the return behavior
  }

  @override
  void dispose() {
    AppConstants.isBottomNavigationMounted.value = false;
    super.dispose();
  }
}

/// Wrapper widget to handle auto-hide navigation for buyer screens
class BuyerNavigationWrapper extends StatefulWidget {
  final Widget child;
  final ScrollController? scrollController;

  const BuyerNavigationWrapper({
    super.key,
    required this.child,
    this.scrollController,
  });

  @override
  State<BuyerNavigationWrapper> createState() => _BuyerNavigationWrapperState();
}

class _BuyerNavigationWrapperState extends State<BuyerNavigationWrapper> {
  final AutoHideNavigationService _autoHideService =
      AutoHideNavigationService();

  @override
  void initState() {
    super.initState();
    _autoHideService.enableAutoHide();
    if (widget.scrollController != null) {
      _autoHideService.attachToScrollController(widget.scrollController!);
    }
  }

  @override
  void dispose() {
    _autoHideService.disableAutoHide();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
