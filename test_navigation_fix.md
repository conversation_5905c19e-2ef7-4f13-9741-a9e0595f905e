# Navigation Context Fix Test Plan

## Issue Summary
- **Problem**: Deep links and redirections fail in user view but work in store view
- **Root Cause**: Multiple issues:
  1. `AppConstants.currentSelectedTabContext` not properly initialized in user view
  2. Web URL capture was using non-functional `WebAppCaptureUrl()` instead of working `PageUrlService.initialize()`
  3. Web deep link initialization was missing from app startup
- **Solution**: Fixed navigation context initialization AND web URL capture mechanism

## Changes Made

### 1. BuyerNavigation (lib/features/navigation/buyer_navigation.dart)
- Added `_initializeCurrentTabContext()` method
- Called during `initState()` with `WidgetsBinding.instance.addPostFrameCallback`
- Ensures `AppConstants.currentSelectedTabContext` is set for the initial tab (Home tab, index 0)

### 2. SellerNavigation (lib/features/navigation/seller_navigation.dart)
- Added `_initializeCurrentTabContext()` method
- Called during `initState()` with `WidgetsBinding.instance.addPostFrameCallback`
- Ensures `AppConstants.currentSelectedTabContext` is set for the initial tab (Profile tab, index 5)

### 3. HandleUrl (lib/services/app_link_services/handle_url.dart)
- Added `_ensureNavigationContextAvailable()` method to wait for context availability
- Added `_safeNavigateWithContext()` method to avoid async gap issues
- Enhanced error handling and fallback mechanisms

### 4. Web URL Capture Fix (main.dart, main_dev.dart, main_prod.dart)
- **CRITICAL FIX**: Replaced non-functional `WebAppCaptureUrl()` with working `PageUrlService.initialize()`
- This ensures `AppConstants.webChangedUrl` is properly set when URLs are pasted in browser
- Without this, web deep links would never be processed

### 5. App Initialization (lib/features/app.dart)
- Added web deep link initialization in `_initializeDeepLinking()`
- Now calls `HandleUrl()` constructor for web platforms to process captured URLs
- Previously only handled mobile deep links

## Test Scenarios

### User View Testing
1. **Cold Start with Deep Link**
   - Open app in user view with a deep link
   - Verify redirection works to the correct screen
   - Check that home tab context is properly initialized

2. **Warm Start with Deep Link**
   - App already running in user view
   - Tap on a deep link from external source
   - Verify navigation works correctly

3. **Internal Navigation**
   - Navigate within the app in user view
   - Test post links, product links, store links
   - Verify all navigation types work

### Store View Testing
1. **Cold Start with Deep Link**
   - Open app in store view with a deep link
   - Verify redirection still works (should not be affected)
   - Check that profile tab context is properly initialized

2. **Cross-Context Testing**
   - Switch between user and store views
   - Test deep links in both contexts
   - Verify context switching works properly

## Expected Results
- Deep links should work in both user view and store view
- Web URLs pasted in browser address bar should trigger navigation
- No more "context not available" errors
- Consistent navigation behavior across all view types
- Proper fallback mechanisms in place

## Debug Information
The fix includes debug prints to help track the process:
- `BuyerNavigation: Initialized currentSelectedTabContext for tab X`
- `SellerNavigation: Initialized currentSelectedTabContext for tab X`
- `In HandleUrl we get the incoming link: [URL]` (for web)
- `Navigation context is available`
- `Using bottom navigation context as fallback`

## Verification Steps for Web Testing

### Cold Start Testing (App Completely Closed)
1. Run the app with `flutter run -d chrome`
2. Wait for the app to fully load in user view
3. Copy a deep link URL and modify the domain to match your local server (e.g., `http://localhost:port/`)
4. Paste the modified URL in the Chrome address bar and press Enter
5. Check debug console for:
   - **Single URL capture**: `In HandleUrl we get the incoming link: [URL]` (should appear only ONCE)
   - Context initialization: `BuyerNavigation: Initialized currentSelectedTabContext for tab 0`
   - Navigation processing messages
6. Verify the app:
   - Navigates to the correct screen (not staying on home)
   - Automatically switches to the appropriate tab
   - Shows the opened content immediately

### Warm Start Testing (App Already Running)
1. With app running, navigate to a different tab (e.g., Search tab)
2. Paste a deep link URL in the browser address bar
3. Verify the app:
   - Automatically switches to the correct tab for the content type
   - Opens the target page in that tab
   - User can immediately see the opened content (no silent background opening)

### Test Different Content Types
- **Posts**: Should open in Home tab for both user and store views
- **Products**: Should open in Home tab for both user and store views
- **Store profiles**: Should open in Home tab (user view) or Profile tab (store view)
- **User profiles**: Should open in Profile tab for both views

## Key Fixes

### 1. Multiple Navigation Triggers (Fixed)
**Problem**: `HandleUrl()` was being called 3 times during cold start:
- App.dart: `_initializeDeepLinking()`
- UserBottomNavigationBloc: `init()`
- StoreBottomNavigationBloc: `init()`

**Solution**: Removed redundant calls from bottom navigation blocs, keeping only the centralized call in App.dart.

### 2. Tab Context Mismatch (Fixed)
**Problem**: Link classes navigated within current tab instead of switching to appropriate tab first.

**Solution**: Added `_switchToAppropriateTab()` method to all link classes:
- **PostLink**: Switches to Home tab (index 0) for both user and store views
- **ProductLink**: Switches to Home tab (index 0) for both user and store views
- **StoreAndUserLink**:
  - Store content: Home tab (index 0) for user view, Profile tab (index 5) for store view
  - User profiles: Profile tab (index 4) for user view, Profile tab (index 5) for store view

### 3. Web URL Capture (Previously Fixed)
The critical issue was that `WebAppCaptureUrl()` was commented out internally, so `AppConstants.webChangedUrl` was never set. This meant the `HandleUrl` constructor never processed web URLs. By switching to `PageUrlService.initialize()`, web URLs are now properly captured and processed.
