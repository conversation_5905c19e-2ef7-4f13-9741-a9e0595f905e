// import 'dart:async';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:swadesic/features/buyers/buyer_my_orders/buyer_my_orders_bloc.dart';
// import 'package:swadesic/model/api_response_message.dart';
// import 'package:swadesic/model/order_response/get_order_response.dart';
// import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';
// import 'package:swadesic/util/common_methods.dart';
//
//
// class ReturnBloc {
//   // region Common Variables
//   BuildContext context;
//   late BuyerMyOrderServices buyerMyOrderServices;
//   final Order store;
//   // endregion
//
//
//   //region Controller
//   final bottomSheetRefresh = StreamController<bool>.broadcast();
//   //endregion
//
//   //region Text Controller
//   final notesToSeller = TextEditingController();
//   //endregion
//
//   // region | Constructor |
//   ReturnBloc(this.context,this.store);
//   // endregion
//
//   // region Init
//   void init() {
//     buyerMyOrderServices = BuyerMyOrderServices();
//   }
// // endregion
//
//
//   //region Return product
//   returnProduct({required String subOrderNumber,required BuyerMyOrdersBloc buyerMyOrdersBloc}) async {
//     //region Try
//     try {
//       await buyerMyOrderServices.returnOrder(subOrderNumber:subOrderNumber, reason:notesToSeller.text);
//       CommonMethods.toastMessage("order return requested", context);
//       CommonMethods.buyerCloseBottomSheetAndGetAllOrders(context: context, store: store, buyerMyOrdersBloc: buyerMyOrdersBloc);
//     }
//     //endregion
//      on ApiErrorResponseMessage catch (error) {
//       CommonMethods.toastMessage(AppStrings.commonErrorMessage, context, commonErrorMessage: true);
//       return;
//       var snackBar = SnackBar(content: Text(error.message.toString()));
//       ScaffoldMessenger.of(context).showSnackBar(snackBar);
//       return;
//     } catch (error) {
//       //print(error);
//       var snackBar = SnackBar(content: Text(error.toString()));
//       ScaffoldMessenger.of(context).showSnackBar(snackBar);
//       return;
//     }
//   }
// //endregion
//
//
//
//
//
//
// }
