import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/bottom_navigations/store_bottom_navigation/store_bottom_navigation.dart';
import 'package:swadesic/features/bottom_navigations/user_bottom_navigation/user_bottom_navigation.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/buyer_payment/buyer_payment_screen.dart';
import 'package:swadesic/features/buyers/buyer_payment/razor_pay_payment/razor_pay_payment.dart';
import 'package:swadesic/features/buyers/shopping_cart/cart_delivery_address/cart_delivery_address_screen.dart';
import 'package:swadesic/features/buyers/shopping_cart/colsed_and_deleted_store_product.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_bloc.dart';
import 'package:swadesic/features/mobile_number_otp/intro_slider/intro_slider_screen.dart';
import 'package:swadesic/features/mobile_number_otp/mobile_number_otp_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/buyer_payment_options_responses/create_order_and_initiate_payment_response.dart';
import 'package:swadesic/model/shopping_cart_address_response/shopping_cart_address_response.dart';
import 'package:swadesic/model/shopping_cart_responses/cart_details_response.dart';
import 'package:swadesic/model/shopping_cart_responses/order_create_response.dart';
import 'package:swadesic/model/shopping_cart_responses/payment_create_response.dart';
import 'package:swadesic/services/cache_storage/cache_storage_service.dart';
import 'package:swadesic/services/get_app_data_services/get_app_data_service.dart';
import 'package:swadesic/services/shopping_cart_service/shopping_cart_service.dart';
import 'package:swadesic/services/user_address_services/user_address_service.dart';
import 'package:swadesic/services/user_detail_services/user_detail_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/no_internet_handle/no_internet_handle.dart';
import 'package:swadesic/util/universal_link/universal_link.dart';

import '../../../../services/cache_storage/storage_keys.dart';

enum SecureCheckoutState { Loading, Success, Failed, Empty }

class SecureCheckoutBloc {
  // region Common Methods
  BuildContext context;
  final ShoppingCartBloc shoppingCartBloc;
  late ShoppingCartAddressResponse shoppingCartAddressResponse;
  late ShoppingCartAddress selectedAddress = ShoppingCartAddress();

  ///Order Create response
  // late OrderCreateResponse createOrderAndInitiatePaymentResponse;
  late CreateOrderAndInitiatePaymentResponse
      createOrderAndInitiatePaymentResponse;

  ///Payment Create response
  late PaymentCreateResponse paymentCreateResponse;

  // endregion

  //region Controller
  final refreshCtrl = StreamController<bool>.broadcast();
  final secureCheckOutStateCtrl =
      StreamController<SecureCheckoutState>.broadcast();
  final startSecureCheckOutCtrl = StreamController<bool>.broadcast();

  //endregion

  //region Text ctrl

  //endregion

  // region | Constructor |
  SecureCheckoutBloc(this.context, this.shoppingCartBloc);

  // endregion

  // region Init
  init() async {
    // Make sure user details are loaded before proceeding
    if (BuyerHomeBloc.userDetailsResponse.userDetail == null) {
      // Initialize UserDetailsServices
      UserDetailsServices userDetailsServices = UserDetailsServices();

      try {
        // Get user details using the app data user reference
        if (AppConstants.appData.userReference != null) {
          BuyerHomeBloc.userDetailsResponse =
              await userDetailsServices.getLoggedInUserDetail(
            userReference: AppConstants.appData.userReference!,
          );
        }
      } catch (e) {
        // Handle error silently - we'll use null checks in the UI
        print('Error loading user details: $e');
      }
    }

    // Continue with address loading
    getAddress();
  }

  // endregion

  //region Go to cart address
  void goToCartAddress() {
    var screen = CartDeliveryAddressScreen(secureCheckoutBloc: this);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      //Success
      secureCheckOutStateCtrl.sink.add(SecureCheckoutState.Success);
    });
  }

  //endregion

  //region Get Addresses
  getAddress() async {
    //region Try
    try {
      // selectedAddressRefreshCtrl.sink.add(ShoppingAddressState.Loading);
      shoppingCartAddressResponse = await UserAddressService().getUserAddress();

      //If address is not null
      if (shoppingCartAddressResponse.addressList!.isNotEmpty) {
        //Check if cached address id is in address list then add that address to selected address
        String id = await CacheStorageService()
            .getString(StorageKeys.shoppingCartAddress);

        for (var data in shoppingCartAddressResponse.addressList!) {
          if (data.useraddressid.toString() == id) {
            selectedAddress = data;
          }
        }
      }

      //Success
      secureCheckOutStateCtrl.sink.add(SecureCheckoutState.Success);
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message!, context);
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
    }
  }

  //endregion

  //region Order Creation
  // orderCreate() async {
  //   //region Try
  //   try {
  //     ///Check if no address is selected
  //     if (selectedAddress.useraddressid == null) {
  //       CommonMethods.toastMessage(AppStrings.pleaseSelectDelivery, context);
  //       return;
  //     }
  //
  //     ///Collect seller notes
  //     var sellerNotes = collectSellerNotes();
  //
  //     ///Cart item list
  //     List<String> cartItemList = [];
  //     for (var id in shoppingCartBloc.getCartItemResponses.cartItemId!) {
  //       cartItemList.add(id.toString());
  //     }
  //
  //     ///If grand total unable to load
  //     if (shoppingCartBloc.getCartPriceResponse.cartFees == null) {
  //       return CommonMethods.toastMessage(AppStrings.unableToLoadGrandTotal, context);
  //     }
  //
  //     //Loading
  //     startSecureCheckOutCtrl.sink.add(true);
  //     createOrderAndInitiatePaymentResponse = await ShoppingCartServices().orderCreate(
  //         cartItemId: cartItemList,
  //         addressId: selectedAddress.useraddressid!,
  //         mobileNumber: selectedAddress.phoneNumber!,
  //         deliveryNotes: shoppingCartBloc.cartDetailsResponse.deliveryNotes,
  //         sellerNotes: sellerNotes);
  //
  //     //If some of the product can not deliver to the address
  //     if (createOrderAndInitiatePaymentResponse.notDeliverable!.isNotEmpty) {
  //       //Loading stop
  //       startSecureCheckOutCtrl.sink.add(false);
  //       return markProductNotDeliverable(productReferences: createOrderAndInitiatePaymentResponse.notDeliverable!);
  //     }
  //
  //     //If store is closed or store is deleted or products are deleted
  //     if (createOrderAndInitiatePaymentResponse.closedStore!.isNotEmpty ||
  //         createOrderAndInitiatePaymentResponse.deletedStore!.isNotEmpty ||
  //         createOrderAndInitiatePaymentResponse.deletedProducts!.isNotEmpty) {
  //       //Loading false
  //       startSecureCheckOutCtrl.sink.add(false);
  //       return ClosedAndDeletedStoreProduct(
  //           shoppingCartBloc: shoppingCartBloc, closedStoreList: storeIdToCartStore(), deletedCartProductList: deletedProduct());
  //     }
  //     //If store is deleted
  //     // if (createOrderAndInitiatePaymentResponse.deletedStore!.isNotEmpty) {
  //     //   // return ClosedAndDeletedStoreProduct(this,createOrderAndInitiatePaymentResponse.deletedStore!);
  //     // }
  //
  //     //Payment create
  //     ///Un comment
  //     // paymentCreate(createOrderAndInitiatePaymentResponse.orderRequestNumber!);
  //     //Go to buyer payment screen
  //     // goToBuyerPaymentScreen();
  //     //Initiate payment
  //     initiatePayment();
  //   }
  //   //endregion
  //   on ApiErrorResponseMessage catch (error) {
  //     //Loading false
  //     startSecureCheckOutCtrl.sink.add(false);
  //     context.mounted ? CommonMethods.toastMessage(error.message!, context) : null;
  //   } catch (error) {
  //     //Loading false
  //     startSecureCheckOutCtrl.sink.add(false);
  //     //print(error);
  //     CommonMethods.toastMessage(AppStrings.commonErrorMessage, context, commonErrorMessage: true);
  //
  //     return;
  //   }
  // }

  //endregion

  //region Create order and initiate payment
  createOrderAndInitiatePayment() async {
    //region Try
    try {
      ///Check if no address is selected
      if (selectedAddress.useraddressid == null) {
        CommonMethods.toastMessage(AppStrings.pleaseSelectDelivery, context);
        return;
      }

      ///Collect seller notes
      var sellerNotes = collectSellerNotes();

      ///Cart item list
      List<String> cartItemList = [];
      for (var id in shoppingCartBloc.getCartItemResponses.cartItemId!) {
        cartItemList.add(id.toString());
      }

      ///If grand total unable to load
      if (shoppingCartBloc.getCartPriceResponse.cartFees == null) {
        return CommonMethods.toastMessage(
            AppStrings.unableToLoadGrandTotal, context);
      }

      //Loading
      startSecureCheckOutCtrl.sink.add(true);
      createOrderAndInitiatePaymentResponse = await ShoppingCartServices()
          .createOrderAndInitiatePayment(
              cartItemId: cartItemList,
              addressId: selectedAddress.useraddressid!,
              mobileNumber: selectedAddress.phoneNumber!,
              deliveryNotes: shoppingCartBloc.cartDetailsResponse.deliveryNotes,
              sellerNotes: sellerNotes);

      //If some of the product can not deliver to the address
      if (createOrderAndInitiatePaymentResponse.notDeliverable!.isNotEmpty) {
        //Loading stop
        startSecureCheckOutCtrl.sink.add(false);
        return markProductNotDeliverable(
            productReferences:
                createOrderAndInitiatePaymentResponse.notDeliverable!);
      }

      //If some of the product can not deliver to the address
      if (createOrderAndInitiatePaymentResponse
          .onlyPickupProducts!.isNotEmpty) {
        //Loading stop
        startSecureCheckOutCtrl.sink.add(false);
        return markProductNotDeliverable(
            productReferences:
                createOrderAndInitiatePaymentResponse.onlyPickupProducts!);
      }

      //If store is closed or store is deleted or products are deleted
      if (createOrderAndInitiatePaymentResponse.closedStore!.isNotEmpty ||
          createOrderAndInitiatePaymentResponse.deletedStore!.isNotEmpty ||
          createOrderAndInitiatePaymentResponse.deletedProducts!.isNotEmpty) {
        //Loading false
        startSecureCheckOutCtrl.sink.add(false);
        return ClosedAndDeletedStoreProduct(
            shoppingCartBloc: shoppingCartBloc,
            closedStoreList: storeIdToCartStore(),
            deletedCartProductList: deletedProduct());
      }

      //Initiate payment
      initiatePayment();
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      //Loading false
      startSecureCheckOutCtrl.sink.add(false);
      context.mounted
          ? CommonMethods.toastMessage(error.message!, context,
              toastShowTimer: 5)
          : null;
    } catch (error) {
      //Loading false
      startSecureCheckOutCtrl.sink.add(false);
      //print(error);
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;

      return;
    }
  }

  //endregion

  //region Initiate payment
  void initiatePayment() {
    RazorPayPayment(
        context: context,
        createOrderAndInitiatePaymentResponse:
            createOrderAndInitiatePaymentResponse);
  }

  //endregion

  //region Payment Create
  // paymentCreate(String orderId) async {
  //   //region Try
  //   try {
  //     paymentCreateResponse = await ShoppingCartServices().paymentCreate(
  //       orderNumber: orderId,
  //       deliveryFee: shoppingCartBloc.getCartPriceResponse.cartFees!
  //           .firstWhere((element) => element.orderBreakupItemText == "Delivery fees")
  //           .orderBreakupItemValue
  //           .toString(),
  //       cartTotal: shoppingCartBloc.getCartPriceResponse.cartFees!
  //           .firstWhere((element) => element.orderBreakupItemText == "Product total")
  //           .orderBreakupItemValue
  //           .toString(),
  //       totalAmount: shoppingCartBloc.getCartPriceResponse.cartFees!
  //           .firstWhere((element) => element.orderBreakupItemText == "Grand total")
  //           .orderBreakupItemValue
  //           .toString(),
  //     );
  //     //print("Txn token is ${paymentCreateResponse.txnToken}");
  //
  //     //Go to Buyer Payment Screen
  //     goToBuyerPaymentScreen();
  //
  //     //Get bank List Api Call
  //     // getBankList(paymentCreateResponse.orderNumber!,paymentCreateResponse.txnToken!);
  //   }
  //   //endregion
  //   on ApiErrorResponseMessage catch (error) {
  //     CommonMethods.toastMessage(AppStrings.commonErrorMessage, context, commonErrorMessage: true);
  //     return;
  //     return;
  //     CommonMethods.toastMessage(AppStrings.commonErrorMessage, context, commonErrorMessage: true);
  //     return;
  //   } catch (error) {
  //     //print(error);
  //     CommonMethods.toastMessage(AppStrings.commonErrorMessage, context, commonErrorMessage: true);
  //
  //     return;
  //   }
  // }

  //endregion

  //region Go To Buyer Payment Screen
  // void goToBuyerPaymentScreen() {
  //   //Add order number and TXN Token to the globalvariable
  //   // AppConstants.orderNumber = paymentCreateResponse.orderNumber!;
  //   // AppConstants.txnToken = paymentCreateResponse.txnToken!;
  //
  //   //Available payment type list
  //   // List<String> paymentList = [];
  //   // for (var data in paymentCreateResponse.fetchPaymentOptionApiResponse!.body!.merchantPayOption!.paymentModes!) {
  //   //   paymentList.add(data.displayName!);
  //   // }
  //   //Pop Current screen
  //   Navigator.pop(context);
  //   var screen = BuyerPaymentScreen(
  //     getCartPriceResponse: shoppingCartBloc.getCartPriceResponse,
  //     orderNumber: createOrderAndInitiatePaymentResponse.orderRequestNumber!,
  //   );
  //   var route = MaterialPageRoute(builder: (context) => screen);
  //   Navigator.pushReplacement(context, route).then((value) {
  //     init();
  //     // Navigator.pushReplacement(context, route).then((value) {
  //   });
  //   // Navigator.pushReplacement(context, route).then((value) {
  //   // });
  // }

  //endregion

  //region Take out deleted products
  List<CartProduct> deletedProduct() {
    List<CartStore> deletedStore = [];
    List<CartProduct> deletedCartProductList = [];

    //Deleted store by finding deleted store id
    for (var storeId in createOrderAndInitiatePaymentResponse.deletedStore!) {
      // deletedStore = cartDetailsResponse.cartStoreList!.where((element) => element.storeid == storeId).toList();
      deletedStore.addAll(shoppingCartBloc.cartDetailsResponse.cartStoreList!
          .where((element) => element.storeid == storeId));
    }

    //Deleted product from deleted store
    for (var store in deletedStore) {
      deletedCartProductList.addAll(store.cartProductList!
          .where((element) => element.isDeleted!)
          .toList());
    }

    //Deleted product from open store
    for (var store in shoppingCartBloc.cartDetailsResponse.cartStoreList!) {
      //print(store.storeName);
      deletedCartProductList.addAll(store.cartProductList!.where((element) =>
          createOrderAndInitiatePaymentResponse.deletedProducts!
              .contains(element.productReference)));
      // deletedCartProductList = store.cartProductList!.where((element) => createOrderAndInitiatePaymentResponse.deletedProducts!.contains(element.productReference)).toList();
    }
    return deletedCartProductList;
  }

  //endregion

  //region From store id to CartStore
  List<CartStore> storeIdToCartStore() {
    List<CartStore> cartStoreList = [];
    //Store id loop
    for (var storeId in createOrderAndInitiatePaymentResponse.closedStore!) {
      cartStoreList.addAll(shoppingCartBloc.cartDetailsResponse.cartStoreList!
          .where((element) => element.storeid == storeId));
    }
    return cartStoreList;
  }

  //endregion

  //region Collect notes
  List<Map> collectSellerNotes() {
    List<Map> data = [];

    for (var uni in shoppingCartBloc.cartDetailsResponse.cartStoreList!) {
      data.add({"storeid": uni.storeid, "note": uni.sellerNote});
    }
    data.removeWhere((element) => element["note"] == "");

    //print(data);
    return data;
  }

  //endregion

  //region Sub order as not deliverable
  markProductNotDeliverable({required List<String> productReferences}) {
    //Message
    CommonMethods.toastMessage(AppStrings.someOfTheProductAreNotAble, context,
        toastShowTimer: 5);
    //Store loop
    for (var store in shoppingCartBloc.cartDetailsResponse.cartStoreList!) {
      //Product loop
      for (var product in store.cartProductList!) {
        //If product reference contains then mark as un available as true
        if (productReferences.contains(product.productReference!)) {
          product.deliverability = false;
        }
      }
    }
    //Close screen
    Navigator.pop(context);
  }

  //endregion

//region Dispose
  void dispose() {
    refreshCtrl.close();
    startSecureCheckOutCtrl.close();
  }
//endregion
}
