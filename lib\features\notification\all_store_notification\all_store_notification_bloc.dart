import 'dart:async';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_comment/buyer_product_comment_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/all_store_notification.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/user_or_store_notification.dart';
import 'package:swadesic/features/data_model/user_created_stores/user_created_stores.dart';
import 'package:swadesic/features/post/single_post_view/single_post_view_screen.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_sub_order/seller_sub_order_screen.dart';
import 'package:swadesic/features/support/issue_suggestion_dialog/issue_suggestion_dialog.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/buyer_search_response/store_list_response.dart';
import 'package:swadesic/model/notification_response/notification_response.dart';
import 'package:swadesic/services/notification_services/notification_service.dart';
import 'package:swadesic/services/seller_home_service/seller_home_service.dart';
import 'package:swadesic/services/store_and_user_reference_services/store_and_user_reference_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/store_user_product_navigation/store_user_product_navigation.dart';
import 'package:encrypt/encrypt.dart' as encrypt;

enum AllStoreNotificationState { Loading, Success, Failed, Empty }

class AllStoreNotificationBloc {
  // region Common Variables
  BuildContext context;
  late GetNotificationResponse allStoreNotificationResponse =
      GetNotificationResponse();
  late AllStoreNotificationDataModel allStoreNotificationDataModel;
  final Function(int) unSeenCount;

  ///Get Store List
  late SellerHomeService sellerHomeService;
  static late StoreListResponse storeListResponse;

  // endregion

  //region Controller
  final allStoreNotificationCtrl =
      StreamController<AllStoreNotificationState>.broadcast();
  final tabRefreshCtrl = StreamController<bool>.broadcast();

  // region | Constructor |
  AllStoreNotificationBloc(this.context, this.unSeenCount);

  // endregion

  // region Init
  init() {
    //Notification data to data model initialize
    allStoreNotificationDataModel =
        Provider.of<AllStoreNotificationDataModel>(context, listen: false);

    //Get  store list create dby user
    getStoreListCreatedByUser();
    //For ground
    forGroundNotification();
    //Get all store notification
    getAllStoreNotification();
  }

// endregion

  ///1
  //region For ground notification
  void forGroundNotification() {
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      //If not mounted
      if (context.mounted) {
        //Get all store notification
        getAllStoreNotification();
      }
    });
  }

  //endregion

  ///3
  //region Get All store notification
  Future<void> getAllStoreNotification() async {
    //region Try
    try {
      //Api call
      allStoreNotificationResponse =
          await NotificationService().getAllStoreNotification();
      //Add all data in data all store notification data model
      allStoreNotificationDataModel.addAllStoreNotification(
          notificationData: allStoreNotificationResponse);
      //Empty
      if (allStoreNotificationResponse.notifications!.isEmpty) {
        allStoreNotificationCtrl.sink.add(AllStoreNotificationState.Empty);
        return;
      }
      //On seen count call back
      unSeenCount(allStoreNotificationResponse.notSeenCount!);
      //Success
      context.mounted
          ? allStoreNotificationCtrl.sink.add(AllStoreNotificationState.Success)
          : null;
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      //Failed
      allStoreNotificationCtrl.sink.add(AllStoreNotificationState.Failed);
      CommonMethods.toastMessage(error.message!, context);
      //CommonMethods.snackBar(error.message!, context);
    } catch (error) {
      //print(error);
      //Failed
      allStoreNotificationCtrl.sink.add(AllStoreNotificationState.Failed);
    }
  }

  //endregion

  //region Get StoreList created by user
  void getStoreListCreatedByUser() async {
    try {
      storeListResponse = await SellerHomeService().getSellerStore();
    } on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }

//endregion

  //region Update Notification
  updateNotification({required NotificationDetail notificationDetail}) async {
    //region Try
    try {
      //If product is seen then return
      if (notificationDetail.notificationStatus == "NOT_SEEN") {
        //Update ui is seen
        allStoreNotificationDataModel.markNotificationAsSeen(
            notificationDetail: notificationDetail);
        //Api call
        // await NotificationService().updateNotification(notificationReference: [notificationDetail.notificationReference!], notifiedUserOrStore:notificationDetail.notifiedUser!);
        NotificationService().updateNotification(body: {
          "notification_list": [notificationDetail.notificationReference!],
          "notified_user": [notificationDetail.notifiedUser!],
          "is_all_notifications": false
        });
      }
      //If notification contains a url
      navigateToScreen(notificationDetail: notificationDetail);

      // if(notificationDetail.externalUrl != null){
      //   context.mounted?CommonMethods.opeAppWebView(webUrl: notificationDetail.externalUrl!, context: context):null;
      //   return;
      // }else{
      //   //Navigate
      //   navigateToScreen(notificationDetail: notificationDetail);
      // }
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message!, context);
      //CommonMethods.snackBar(error.message!, context);
    } catch (error) {
      //print(error);
      // var snackBar =  SnackBar(content: Text(error.toString()));
      // ScaffoldMessenger.of(context).showSnackBar(snackBar);
      // return;
    }
  }

  //endregion

  //region Navigation
  navigateToScreen({required NotificationDetail notificationDetail}) async {
    debugPrint(
        'navigateToScreen: Starting navigation for notification: ${notificationDetail.notificationType}');
    debugPrint(
        'Current store: ${AppConstants.appData.storeReference}, Target store: ${notificationDetail.notifiedUser}');

    // If already on the correct store, navigate directly
    if (notificationDetail.notifiedUser ==
        AppConstants.appData.storeReference) {
      debugPrint('Already on correct store, navigating directly');
      _navigateAfterSwitch(notificationDetail);
      return;
    }

    // If we need to switch to a different store
    try {
      // Find the target store
      final targetStore = storeListResponse.storeList!.firstWhere((element) =>
          element.storeReference == notificationDetail.notifiedUser);

      // Show switching message
      if (context.mounted) {
        CommonMethods.toastMessage(
            "Switching to ${targetStore.storehandle}", context,
            toastShowTimer: 3);
      }

      // Store the notification details before switching accounts
      final notificationToNavigate = notificationDetail;

      // Switch to the seller account
      await CommonMethods.switchToSeller(
          storeReference: notificationToNavigate.notifiedUser!,
          storeId: targetStore.storeid!,
          context: context);

      // Add a delay to ensure the navigation is complete
      await Future.delayed(const Duration(milliseconds: 1500));

      // Use a timer to ensure we have a valid context after navigation
      int retryCount = 0;
      const maxRetries = 3;

      Future<void> attemptNavigation() async {
        try {
          debugPrint(
              'Attempting to navigate (attempt ${retryCount + 1} of $maxRetries)');
          if (AppConstants.currentSelectedTabContext.mounted) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _navigateAfterSwitch(notificationToNavigate);
            });
          } else {
            throw Exception('Context not mounted yet');
          }
        } catch (e) {
          retryCount++;
          if (retryCount < maxRetries) {
            debugPrint('Navigation attempt $retryCount failed, retrying...');
            await Future.delayed(const Duration(milliseconds: 500));
            await attemptNavigation();
          } else {
            debugPrint('Max navigation retries reached');
            if (context.mounted) {
              CommonMethods.toastMessage(
                  "Navigation failed, please try again", context);
            }
          }
        }
      }

      await attemptNavigation();
    } catch (e) {
      debugPrint('Error switching to store: $e');
      if (context.mounted) {
        CommonMethods.toastMessage(
            "Failed to switch store: ${e.toString()}", context);
      }
    }
  }

  // Helper method to handle navigation after account switch
  void _navigateAfterSwitch(NotificationDetail notificationDetail) {
    debugPrint(
        '_navigateAfterSwitch: Processing notification type: ${notificationDetail.notificationType}');
    debugPrint(
        'Action page: ${notificationDetail.actionPage}, Notification about: ${notificationDetail.notificationAbout}');

    try {
      // First try using actionPage if available
      if (notificationDetail.actionPage != null) {
        debugPrint(
            'Navigating using actionPage: ${notificationDetail.actionPage}');
        StoreUserProductNavigation()
            .goToScreen(actionPage: notificationDetail.actionPage!);
      }
      // Then try using notificationAbout if available
      else if (notificationDetail.notificationAbout != null) {
        debugPrint(
            'Navigating using notificationAbout: ${notificationDetail.notificationAbout}');
        StoreUserProductNavigation().navigateToStoreProductAndStore(
            references: notificationDetail.notificationAbout!);
      }
      // If neither is available, try to determine navigation based on notification type
      else {
        debugPrint('No direct navigation target, checking notification type');
        _handleNotificationByType(notificationDetail);
      }
    } catch (e) {
      debugPrint('Error in _navigateAfterSwitch: $e');

      // Try again after a short delay if the first attempt fails
      Future.delayed(const Duration(milliseconds: 500), () {
        try {
          if (notificationDetail.actionPage != null) {
            StoreUserProductNavigation()
                .goToScreen(actionPage: notificationDetail.actionPage!);
          } else if (notificationDetail.notificationAbout != null) {
            StoreUserProductNavigation().navigateToStoreProductAndStore(
                references: notificationDetail.notificationAbout!);
          } else {
            _handleNotificationByType(notificationDetail);
          }
        } catch (retryError) {
          debugPrint('Retry navigation failed: $retryError');
        }
      });
    }
  }

  // Handle navigation based on notification type
  void _handleNotificationByType(NotificationDetail notificationDetail) {
    final type = notificationDetail.notificationType?.toLowerCase() ?? '';
    debugPrint('Handling notification by type: $type');

    // Handle different notification types
    if (type.contains('comment')) {
      // For comments, try to extract the post reference from the message
      final matches = RegExp(r'PO\d+')
          .stringMatch(notificationDetail.notificationMessage ?? '');
      if (matches != null) {
        StoreUserProductNavigation()
            .navigateToStoreProductAndStore(references: matches);
      }
    } else if (type.contains('like') || type.contains('repost')) {
      // For likes/reposts, try to find the referenced content
      if (notificationDetail.notificationReference != null) {
        // Try to extract reference from the notification reference
        final ref = notificationDetail.notificationReference!;
        if (ref.startsWith('PO') ||
            ref.startsWith('P') ||
            ref.startsWith('S')) {
          StoreUserProductNavigation()
              .navigateToStoreProductAndStore(references: ref);
        }
      }
    } else if (type.contains('reward')) {
      // For rewards, navigate to the rewards screen
      StoreUserProductNavigation().goToScreen(actionPage: 'REWARDS');
    } else {
      debugPrint('No navigation handler for notification type: $type');
      // As a fallback, try to navigate to the store's profile
      if (notificationDetail.notifiedUser != null) {
        StoreUserProductNavigation().navigateToStoreProductAndStore(
            references: notificationDetail.notifiedUser!);
      }
    }
  }
}

//endregion

//region Go to comment
void goToComment(
    {required String commentOrPostReference,
    required String notificationEnumns}) async {
  var screen = SinglePostViewScreen(
    postReference: commentOrPostReference,
  );
  var route = MaterialPageRoute(builder: (context) => screen);
  Navigator.push(AppConstants.currentSelectedTabContext, route);
}

//endregion

void encryptData(String data, String keyString) {
  final key = encrypt.Key.fromUtf8(keyString);
  final iv = encrypt.IV.fromLength(16);
  final encrypter = encrypt.Encrypter(encrypt.AES(key));
  final encrypted = encrypter.encrypt(data, iv: iv);
  //print("Encrypted data is ${encrypted.base64}");
  decryptData(encrypted.base64, keyString);
}

void decryptData(String encryptedData, String keyString) {
  final key = encrypt.Key.fromUtf8(keyString);
  final iv = encrypt.IV.fromLength(16);
  final encrypter = encrypt.Encrypter(encrypt.AES(key));
  final encrypted = encrypt.Encrypted.fromBase64(encryptedData);
  final decrypted = encrypter.decrypt(encrypted, iv: iv);
  //print("Decrypted data is $decrypted");
}

//region Dispose
void dispose() {}
//endregion

