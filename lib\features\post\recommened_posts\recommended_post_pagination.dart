import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/post/feed/feed_bloc.dart';
import 'package:swadesic/features/post/recommened_posts/recommended_post_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum RecommendedPostPaginationState { Loading, Done, Empty }

class RecommendedPostPagination {
  //region Context
  late BuildContext context;
  late RecommendedPostBloc recommendedPostBloc;
  RecommendedPostPaginationState currentApiCallStatus =
      RecommendedPostPaginationState.Done;

  int limit = 10;
  // bool isLoadingPaginationData = false;
  RecommendedPostPaginationState currentPaginationState =
      RecommendedPostPaginationState.Done;
  //endregion

  //region Controller
  final feedPaginationStateCtrl =
      StreamController<RecommendedPostPaginationState>.broadcast();
  //endregion

  //region Constructor
  RecommendedPostPagination(this.context, this.recommendedPostBloc) {
    //Add initial state in pagination state
    feedPaginationStateCtrl.sink.add(RecommendedPostPaginationState.Done);
    //Scroll listener
    // recommendedPostBloc.scrollController.addListener(() {
    //   scrollListener();
    // });
    //Pagination controller listener
    feedPaginationStateCtrl.stream
        .listen((RecommendedPostPaginationState state) {
      feedPaginationControllerListener(state: state);
    });
  }
  //endregion

  //region Feed page Controller listener
  void feedPaginationControllerListener(
      {required RecommendedPostPaginationState state}) {
    currentPaginationState = state;
    //print("Status of pagination${state.name}");
  }
  //endregion

  //region Scroll listener
  void scrollListener() async {
    //If Page state is empty then return
    if (currentPaginationState == RecommendedPostPaginationState.Empty) {
      return;
    }

    if (
        // currentPaginationState != FeedPaginationState.Loading &&
        recommendedPostBloc.scrollController.offset >=
                recommendedPostBloc.scrollController.position.maxScrollExtent &&
            !recommendedPostBloc.scrollController.position.outOfRange &&
            recommendedPostBloc.recommendedPostList.length >= 10
        // recommendedPostBloc.scrollController.position.pixels >= recommendedPostBloc.scrollController.position.maxScrollExtent

        ) {
      //Increase offset
      // offset = limit + offset;
      // Fetch more feed posts when list is scrolled to the bottom
      await getPaginationRecommendedPosts();
    }
  }
  //endregion

  //region Get pagination recommended posts
  Future<void> getPaginationRecommendedPosts() async {
    // Get reference to the PostDataModel
    var postDataModel = Provider.of<PostDataModel>(context, listen: false);
    //Get reference to Product data model
    var productDataModel =
        Provider.of<ProductDataModel>(context, listen: false);

    try {
      //If api call status is Loading then return
      if (currentApiCallStatus == RecommendedPostPaginationState.Loading) {
        return;
      }
      //Loading
      feedPaginationStateCtrl.sink.add(RecommendedPostPaginationState.Loading);
      //Current api call status is Loading
      currentApiCallStatus = RecommendedPostPaginationState.Loading;

      //Api call
      List<dynamic> responseList = await PostService().getRecommendedPosts(
          context: context,
          limit: limit,
          offset: recommendedPostBloc.recommendedPostList.length);
      //Add feed to the recommended list variable
      recommendedPostBloc.recommendedPostList.addAll(responseList);
      //Filter post and product
      for (var postAndProduct in responseList) {
        if (postAndProduct is PostDetail) {
          //Add data in post data model feed
          postDataModel.addPostIntoList(postList: [postAndProduct]);
          // //print('Post: ${postAndProduct.postOrCommentReference}');
        } else if (postAndProduct is Product) {
          //Add data to product data model
          productDataModel.addProductIntoList(products: [postAndProduct]);
          // //print('Product: ${postAndProduct.productReference}');
        }
      }
      //If response is empty
      if (responseList.isEmpty) {
        // isLoadingPaginationData = false;
        //Current state
        currentPaginationState = RecommendedPostPaginationState.Empty;
        //Message
        context.mounted
            ? CommonMethods.toastMessage(AppStrings.noMorePostToLoad, context)
            : null;
        return feedPaginationStateCtrl.sink
            .add(RecommendedPostPaginationState.Empty);
      }
      // isLoadingPaginationData = false;
      //Done
      feedPaginationStateCtrl.sink.add(RecommendedPostPaginationState.Done);
      //Current api call status is Done
      currentApiCallStatus = RecommendedPostPaginationState.Done;
    } on ApiErrorResponseMessage catch (error) {
      // isLoadingPaginationData = false;
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      //Done
      feedPaginationStateCtrl.sink.add(RecommendedPostPaginationState.Done);
    } catch (error) {
      // isLoadingPaginationData = false;
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
      //Done
      feedPaginationStateCtrl.sink.add(RecommendedPostPaginationState.Done);
    }
  }
//endregion
}
