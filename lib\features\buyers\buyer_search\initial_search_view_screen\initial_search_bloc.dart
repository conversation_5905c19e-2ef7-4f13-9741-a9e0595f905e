import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_bloc.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_screen.dart';
import 'package:swadesic/services/buyer_search_services/buyer_search_services.dart';

class InitialSearchBloc {
  // region Common Variables
  BuildContext context;
  late BuyerSearchServices buyerSearchServices;
  // static late BuyerSearchHistoryResponse buyerSearchHistoryResponse = BuyerSearchHistoryResponse();
  // endregion

  //region Controller
  final refreshCtrl = StreamController<bool>.broadcast();
  // region | Constructor |
  InitialSearchBloc(this.context);

  // endregion

  // region Init
  void init() {
    buyerSearchServices = BuyerSearchServices();
  }

// endregion

  //region Get History
  // getHistory() async {
  //   try {
  //     // buyerSearchHistoryCtrl.sink.add(BuyerSearchHistoryState.Loading);
  //     buyerSearchHistoryResponse = await buyerSearchServices.getSearchedHistory();
  //     if(buyerSearchHistoryResponse.recentSearches!.isEmpty && buyerSearchHistoryResponse.store!.isEmpty &&
  //         buyerSearchHistoryResponse.product!.isEmpty && buyerSearchHistoryResponse.user!.isEmpty
  //     ){
  //       // return buyerSearchHistoryCtrl.sink.add(BuyerSearchHistoryState.Empty);
  //     }
  //     // buyerSearchHistoryCtrl.sink.add(BuyerSearchHistoryState.Success);
  //   }
  //    on ApiErrorResponseMessage catch (error) {
  //     CommonMethods.toastMessage(AppStrings.commonErrorMessage, context, commonErrorMessage: true);
  //     return;
  //     // buyerSearchHistoryCtrl.sink.add(BuyerSearchHistoryState.Failed);
  //     CommonMethods.snackBar(AppStrings.error, context);
  //     return;
  //   }
  //   catch (error) {
  //     // buyerSearchHistoryCtrl.sink.add(BuyerSearchHistoryState.Failed);
  //     CommonMethods.snackBar(AppStrings.error, context);
  //     return;
  //   }
  // }
  //endregion

  //region Go to search screen
  goToSearchScreen({required int index}) {
    //Apply global search filter
    BuyerSearchBloc.searchScreenSelectedTab = index;

    ///If api data is null then return
    // if(buyerSearchHistoryResponse.message == null){
    //   return;
    // }
    var screen = const BuyerSearchScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

//endregion

//region Dispose
  void dispose() {
    imageCache.clear();
    refreshCtrl.close();
  }
//endregion
}
