import 'dart:async';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/common_buyer_seller_screen/logout/logout.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/shopping_cart_quantity_data_model/shopping_cart_quantity_data_model.dart';
import 'package:swadesic/features/mobile_number_otp/intro_slider/intro_slider_screen.dart';
import 'package:swadesic/features/mobile_number_otp/mobile_number_otp_screen.dart';
import 'package:swadesic/features/seller/deactivate_and_delete_store/exit_survey/exit_survey_screen.dart';
import 'package:swadesic/features/splash/splash_screen.dart';
import 'package:swadesic/features/user_profile/user_settings/app_and_security/app_font_change/app_font_change.dart';
import 'package:swadesic/model/app_data/app_data.dart';
import 'package:swadesic/services/cache_storage/cache_storage_service.dart';
import 'package:swadesic/services/cache_storage/storage_keys.dart';
import 'package:swadesic/services/user_device_detail_services/user_device_detail_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum AppAndSecurityState { Loading, Success }

class AppAndSecurityBloc {
  //region Common variable
  late BuildContext context;
  late CacheStorageService cacheStorageService;
  late PackageInfo packageInfo;
  bool availableUpdate = false;
  late ShoppingCartQuantityDataModel shoppingCartQuantityDataModel;
  late LoggedInUserInfoDataModel loggedInUserInfoDataModel;

  final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

  //endregion

//region Text Editing Controller
//endregion

//region Controller
  final refreshCtrl = StreamController<AppAndSecurityState>.broadcast();

//endregion
  //region Constructor
  AppAndSecurityBloc(this.context);
  //endregion
//region Init
  init() {
    shoppingCartQuantityDataModel =
        Provider.of<ShoppingCartQuantityDataModel>(context, listen: false);
    loggedInUserInfoDataModel =
        Provider.of<LoggedInUserInfoDataModel>(context, listen: false);
    cacheStorageService = CacheStorageService();
    getAppVersion();
    checkUpdate();
    _getDeviceId();
  }
//endregion

//region Initialize package info

//endregion

  //region Get device id
  Future<String> _getDeviceId() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    String deviceId = '';

    if (kIsWeb) {
      WebBrowserInfo webBrowserInfo = await deviceInfo.webBrowserInfo;
      deviceId = webBrowserInfo.userAgent ?? '';
    } else if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      deviceId = androidInfo.id ?? '';
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      deviceId = iosInfo.identifierForVendor ?? '';
    }
    return deviceId;
  }

  //endregion

//region Logout
  void logOut() async {
    //Delete FCM token if user is on mobile only
    !CommonMethods.isWeb()
        ? await UserDeviceDetailService().deleteDeviceAndFcmDetail(
            deviceId: await CommonMethods.getDeviceId())
        : null;
    //Clear share preference
    cacheStorageService.removeItem(StorageKeys.userId);
    cacheStorageService.removeItem(StorageKeys.userReference);
    cacheStorageService.removeItem(StorageKeys.shoppingCartAddress);
    cacheStorageService.removeItem(StorageKeys.shoppingCartMessage);
    cacheStorageService.removeItem(StorageKeys.appdataKey);
    cacheStorageService.removeItem(StorageKeys.orderOtpStatus);
    //Clear cart
    shoppingCartQuantityDataModel.productReferenceList.clear();
    //Clear user data in global
    AppConstants.appData = AppData(baseUrl: AppConstants.baseUrl);
    //Switch user bottom navigation to the 1st

    //Test check data
    var data = await cacheStorageService.getString(StorageKeys.appdataKey);

    //print(data);

    // AppConstants.userPersistentTabController.jumpToTab(0);
    AppConstants.userPersistentTabController.jumpToTab(0);
    //Clear user logged in user info
    loggedInUserInfoDataModel.switchToStaticUser();
    //Push and pop all screen behind
    Navigator.of(AppConstants.userStoreCommonBottomNavigationContext,
            rootNavigator: true)
        .pushAndRemoveUntil(
            MaterialPageRoute(
                builder: (context) => const MobileNumberOtpScreen()),
            (Route<dynamic> route) => false);
    // MobileNumberOtpScreen()), (Route<dynamic> route) => false);
    // Navigator.of(context,rootNavigator: true).pus();
  }
//endregion

//region Open dialog
  Future openDialog() {
    return CommonMethods.appDialogBox(
        context: context,
        widget: OkayAndCancelDialogScreen(
          onTapSecondButton: () {
            logOut();
          },
          previousScreenContext: context,
          isMessageVisible: true,
          message: AppStrings.areYouSureWantsToLogout,
          firstButtonName: "Cancel",
          secondButtonName: "Logout",
        ));
  }
//endregion

  //region Get app version
  getAppVersion() async {
    //Loading
    refreshCtrl.sink.add(AppAndSecurityState.Loading);
    //Take out app version
    packageInfo = await PackageInfo.fromPlatform();
    //Success
    refreshCtrl.sink.add(AppAndSecurityState.Success);
  }
  //endregion

  //region Check update
  Future<void> checkUpdate() async {
    //Loading
    refreshCtrl.sink.add(AppAndSecurityState.Loading);
    await InAppUpdate.checkForUpdate().then((updateInfo) {
      if (updateInfo.updateAvailability == UpdateAvailability.updateAvailable) {
        //Change the value to true
        availableUpdate = true;
      }
    });
    //Success
    refreshCtrl.sink.add(AppAndSecurityState.Success);
  }
  //endregion

  //region Go to app font change
  void goToAppFontScreen() {
    var screen = const AppFontChangeScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route);
  }
  //endregion

// region On tap delete account
  void onTapDeleteAccount() {
    var screen = ExitSurveyScreen(
      reference: AppConstants.appData.userReference!,
      leadingTitle: AppStrings.deleteAccount,
    );
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

//region Dispose
  void dispose() {
    refreshCtrl.close();
  }
//endregion
}
