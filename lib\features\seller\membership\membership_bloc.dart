import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
// import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/referral_code/referral_code_bloc.dart';
import 'package:swadesic/features/seller/membership/already_joined_bottom_sheets.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/membership_responses/create_invitation_response.dart';
import 'package:swadesic/model/membership_responses/friends_invites_response.dart';
import 'package:swadesic/model/membership_responses/membersip_type_model.dart';
import 'package:swadesic/model/membership_responses/sent_request_response.dart';
import 'package:swadesic/services/membership_services/membership_service.dart';
import 'package:swadesic/services/user_detail_services/user_detail_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum MembershipState { Loading, Success, Failed, Empty, NoPermission }

class MembershipBloc {
  // region Common Methods
  BuildContext context;
  int selectedTab = 0;
  bool isBottomSheetVisible = false;
  // List<Contact>phoneContacts = [];
  List<String> mobileNumberList = [];
  int selectedFriendRank = 0;
  String sendAndRequest = "";
  String selectedFriendMobileNumber = "";
  String selectedFriendName = "";
  bool isFindFriendVisibleGreen = false;
  bool isFilterVisible = false;
  bool isAlreadyJoinedVisible = false;

  ///Filter
  bool sellerFilter = false;
  bool memberFilter = false;
  bool nonMemberFilter = false;
  bool contactOnlyFilter = false;
  List<String> selectType = ["seller", "member", "non-member", "contact only"];

  ///Friends and invites
  late MembershipService membershipService;
  late FriendsInvitesResponse friendsInvitesResponse;
  late FriendsInvitesResponse numberAccessResponse;
  late List<FriendsInfo> friendsInviteSearch = [];

  ///Create invite
  late CreateInvitationResponse createInvitationResponse;

  ///Sent request response
  late SentRequestResponse sentRequestResponse;
  late List<MemberSeller> sentMemberInvites = [];
  late List<MemberSeller> sentSellerInvites = [];

  ///Get user details
  late UserDetailsServices userDetailsServices;
  // late GetUserDetailsResponse userDetailsResponse;

  // endregion

  //region Focus node
  final FocusNode referralFocusNode = FocusNode();

  //endregion

  //region Controller
  final tabCtrl = StreamController<bool>.broadcast();
  final inviteCtrl = StreamController<bool>.broadcast();
  // final isFindFriendVisibleCtrl = StreamController<bool>.broadcast();
  final isVisiblePhoneSheetCtrl = StreamController<bool>.broadcast();
  final friendsInviteCtrl = StreamController<MembershipState>.broadcast();
  final sentCtrl = StreamController<MembershipState>.broadcast();
  final mobileNumberSheetCtrl = StreamController<MembershipState>.broadcast();
  final filterCtrl = StreamController<bool>.broadcast();
  final alreadyJoinedCtrl = StreamController<bool>.broadcast();
  final membershipFieldCtrl = StreamController<bool>.broadcast();

  //endregion

  //region Text Ctrl
  final referralCodeTextCtrl = TextEditingController();
  final mobileNumberTextCtrl = TextEditingController();
  final nameTextCtrl = TextEditingController();
  final friendsInviteSearchTextCtrl = TextEditingController();
  final sentSearchTextCtrl = TextEditingController();
  TextInputType membershipKeyboardType = TextInputType.text;

  //endregion

  // region | Constructor |
  MembershipBloc(this.context);

  // endregion

  // region Init
  void init() {
    //sentRequestResponse = SentRequestResponse();
    //Initialize membership service
    membershipService = MembershipService();
    friendsInvitesResponse = FriendsInvitesResponse();
    userDetailsServices = UserDetailsServices();
    //Get already send invites api
    getSentRequestApi();
    // referralFocusNode.addListener(() {
    //   //print("Focus to membershio text is ${referralFocusNode.hasFocus}");
    //   if(referralFocusNode.hasFocus){
    //     membershipKeyboardType = TextInputType.text;
    //   }
    //   if(!referralFocusNode.hasFocus){
    //     membershipKeyboardType = TextInputType.text;
    //   }
    // });
  }
  // endregion

  //region Visible and hide Already joined
  alreadyJoinedVisible(FriendsInfo friendsInfo, membershipBloc) {
    showModalBottomSheet<void>(
      backgroundColor: Colors.white,
      barrierColor: Colors.transparent,
      context: context,
      builder: (BuildContext context) {
        return AlreadyJoinedBottomSheet(
          friendsInfo: friendsInfo,
          membershipBloc: membershipBloc,
        );
      },
    );
  }
  //endregion

  //region On tap drawer
  void onTapDrawer() {
    //print(isFilterVisible);
    isFilterVisible = !isFilterVisible;
    filterCtrl.sink.add(true);
  }
  //endregion

  //region On Select filter
  void onSelectFilter() {
    filterCtrl.sink.add(true);

    ///Clear friends invite Search
    friendsInviteSearch.clear();

    //Seller
    if (sellerFilter) {
      //Add Friends to friend and invite Search
      for (var userInfo in friendsInvitesResponse.friendsInfoList!) {
        //friendsInviteSearch.addAll(friendsInvitesResponse.friendsInfoList!.where((element) => element.rank==3));
        if (userInfo.rank == 3) {
          friendsInviteSearch.add(userInfo);
          //print("seller");
        }
      }
      friendsInviteCtrl.sink.add(MembershipState.Success);
    }

    //Member
    if (memberFilter) {
      //Add Friends to friend and invite Search
      for (var userInfo in friendsInvitesResponse.friendsInfoList!) {
        //friendsInviteSearch.addAll(friendsInvitesResponse.friendsInfoList!.where((element) => element.rank==3));
        if (userInfo.rank == 2) {
          friendsInviteSearch.add(userInfo);
        }
      }
      friendsInviteCtrl.sink.add(MembershipState.Success);
    }
    //Non-Member
    if (nonMemberFilter) {
      //Add Friends to friend and invite Search
      for (var userInfo in friendsInvitesResponse.friendsInfoList!) {
        //friendsInviteSearch.addAll(friendsInvitesResponse.friendsInfoList!.where((element) => element.rank==3));
        if (userInfo.membershipType == "NON_MEMBER") {
          friendsInviteSearch.add(userInfo);
        }
      }
      friendsInviteCtrl.sink.add(MembershipState.Success);
    }
    //Contact only
    if (contactOnlyFilter) {
      //Add Friends to friend and invite Search
      for (var userInfo in friendsInvitesResponse.friendsInfoList!) {
        //friendsInviteSearch.addAll(friendsInvitesResponse.friendsInfoList!.where((element) => element.rank==3));
        if (userInfo.membershipType!.isEmpty) {
          friendsInviteSearch.add(userInfo);
        }
      }
      friendsInviteCtrl.sink.add(MembershipState.Success);
    }

    //Else
    if (!sellerFilter &&
        !memberFilter &&
        !nonMemberFilter &&
        !contactOnlyFilter) {
      ///Clear friends invite Search
      friendsInviteSearch.clear();

      ///Add friend info to friends info search

      // for(var data in friendsInvitesResponse.friendsInfoList!){
      //   if(data.membershipType == null){
      //     friendsInviteSearch.add(data);
      //   }
      // }
      friendsInviteSearch.addAll(friendsInvitesResponse.friendsInfoList!);
      friendsInviteCtrl.sink.add(MembershipState.Success);
    }
  }
  //endregion

  //region On select Tab
  void onSelectTab(int tab) {
    selectedTab = tab;
    if (selectedTab == 1) {
      getLoggedInUserDetail();
      askPermissions();
      getSentRequestApi();
    }
    if (selectedTab == 2) {
      getLoggedInUserDetail();
      getSentRequestApi();
    }
    tabCtrl.sink.add(true);
    //Close bottom sheet
    isBottomSheetVisible = false;
    inviteCtrl.sink.add(true);
    isVisiblePhoneSheetCtrl.sink.add(false);
  }
  //endregion

  //region Open request and send bottom sheet
  void requestSendBottomSheet(
      String sendOrRequest, int friendRank, String phoneNumber, String name) {
    selectedFriendMobileNumber = phoneNumber;
    selectedFriendName = name;
    isVisiblePhoneSheetCtrl.sink.add(false);
    sendAndRequest = sendOrRequest;
    selectedFriendRank = friendRank;
    isBottomSheetVisible = !isBottomSheetVisible;
    inviteCtrl.sink.add(true);
  }
  //endregion

  //region On tap invite using phone
  void onTapInviteUsingPhone() {
    //Clear number field
    mobileNumberTextCtrl.clear();
    //Clear name field
    nameTextCtrl.clear();
    isVisiblePhoneSheetCtrl.sink.add(true);
  }
  //endregion

  //region Ask permission
  Future<void> askPermissions() async {
    // await FlutterContacts.requestPermission();
    //
    // PermissionStatus permissionStatus = await _getContactPermission();
    //
    // ///If permission is granted
    // if (permissionStatus == PermissionStatus.granted) {
    //   isFindFriendVisibleGreen=false;
    //   // Get all contacts (fully fetched)
    //   phoneContacts = await FlutterContacts.getContacts(withProperties: true,sorted: true);
    //   ///If contact is empty
    //   if(phoneContacts.isEmpty){
    //     friendsInviteCtrl.sink.add(MembershipState.Empty);
    //     return;
    //   }
    //   //Fetch all contact number
    //   for(var contactList in phoneContacts){
    //     for(var numberList in contactList.phones){
    //       mobileNumberList.add(numberList.normalizedNumber);
    //     }
    //   }
    //   ///Call friends and invite function
    //   friendsInvitesApi(mobileNumberList);
    // }
    // ///If permission is denied
    // if (permissionStatus == PermissionStatus.denied) {
    //   isFindFriendVisibleGreen=true;
    //   //isFindFriendVisibleCtrl.sink.add(true);
    //   friendsInviteCtrl.sink.add(MembershipState.NoPermission);
    // }
    // ///If permission is denied permanently
    // if (permissionStatus == PermissionStatus.permanentlyDenied) {
    //   isFindFriendVisibleGreen=true;
    //   // isFindFriendVisibleCtrl.sink.add(true);
    //   friendsInviteCtrl.sink.add(MembershipState.NoPermission);
    // }
  }
  //endregion

  //region Get Contact Permission
  Future<PermissionStatus> _getContactPermission() async {
    PermissionStatus permission = await Permission.contacts.status;

    if (permission != PermissionStatus.granted &&
        permission != PermissionStatus.permanentlyDenied) {
      PermissionStatus permissionStatus = await Permission.contacts.request();
      return permissionStatus;
    } else {
      return permission;
    }
  }
  //endregion

  //region Get friend invites
  friendsInvitesApi(List<String> numberList) async {
    try {
      ///All filter to false
      sellerFilter = false;
      memberFilter = false;
      nonMemberFilter = false;
      filterCtrl.sink.add(true);

      ///Loading
      friendsInviteCtrl.sink.add(MembershipState.Loading);

      ///Clear friends invite Search
      friendsInviteSearch.clear();

      ///User detail api call
      getLoggedInUserDetail();

      friendsInvitesResponse =
          await membershipService.getFriendsInvites(numberList);

      ///Get sent request
      getSentRequestApi();

      ///Add friend info to friends info search
      friendsInviteSearch.addAll(friendsInvitesResponse.friendsInfoList!);

      ///Empty
      if (friendsInviteSearch.isEmpty) {
        friendsInviteCtrl.sink.add(MembershipState.Empty);
      }
      //Add membership rank
      for (var numbers in friendsInvitesResponse.friendsInfoList!) {
        //Add empty string if invite type is NULL
        numbers.membershipType == null ? numbers.membershipType = "" : null;
        int rank = membershipRank(numbers.membershipType!);
        //Add rank to the model
        numbers.rank = rank;
      }
      //Add name to the model
      for (var numbers in friendsInvitesResponse.friendsInfoList!) {
        //print(addNameToModel(numbers.phonenumber!));
        //Add name
        //Add name(If available in local) to the pair of number
        numbers.name = addNameToModel(numbers.phonenumber!);
        //print(numbers.name);
      }

      // //print(friendsInvitesResponse.data![2].rank);
      friendsInviteCtrl.sink.add(MembershipState.Success);
      selectedFriendName = nameTextCtrl.text;
    } on ApiErrorResponseMessage catch (error) {
      friendsInviteCtrl.sink.add(MembershipState.Failed);
      CommonMethods.toastMessage(error.message.toString(), context);

      return;
    } catch (error) {
      friendsInviteCtrl.sink.add(MembershipState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);

      return;
    }
  }
  //endregion

  //region Get Friend and invite Api call
  getFriendsInviteApiCall(List<String> numberList) async {
    try {
      CommonMethods.closeKeyboard(context);
      //membershipCtrl.sink.add(MembershipState.Loading);
      numberAccessResponse =
          await membershipService.getFriendsInvites(numberList);
      //print(numberAccessResponse.friendsInfoList!.first.membershipType);

      // //Add membership rank
      for (var numbers in numberAccessResponse.friendsInfoList!) {
        //Add empty string if invite type is NULL
        numbers.membershipType == null ? numbers.membershipType = "" : null;
        int rank = membershipRank(numbers.membershipType!);
        //Add rank to the model
        numbers.rank = rank;
      }
      //print(numberAccessResponse.friendsInfoList![0].rank);
      mobileNumberSheetCtrl.sink.add(MembershipState.Success);
    } on ApiErrorResponseMessage catch (error) {
      mobileNumberSheetCtrl.sink.add(MembershipState.Success);
      CommonMethods.toastMessage(error.message.toString(), context);

      return;
    } catch (error) {
      mobileNumberSheetCtrl.sink.add(MembershipState.Success);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);

      return;
    }
  }
  //endregion

  //region Get Sent requests
  getSentRequestApi() async {
    try {
      //membershipCtrl.sink.add(MembershipState.Loading);
      sentRequestResponse = await membershipService.getSentInvitation();
      //region Add membership rank for member
      for (var numbers in sentRequestResponse.memberSellerInvite!.member!) {
        //Add empty string if invite type is NULL
        numbers.membershipType == null ? numbers.membershipType = "" : null;
        int rank = membershipRank(numbers.membershipType!);
        //Add rank to the model
        numbers.rank = rank;
        //print(numbers.rank);
      }
      //endregion

      //region Add membership rank for seller
      for (var numbers in sentRequestResponse.memberSellerInvite!.seller!) {
        //Add empty string if invite type is NULL
        numbers.membershipType == null ? numbers.membershipType = "" : null;
        int rank = membershipRank(numbers.membershipType!);
        //Add rank to the model
        numbers.rank = rank;
        //print(numbers.rank);
      }
      //endregion

      //region add joined status for member
      for (var number in sentRequestResponse.memberSellerInvite!.member!) {
        number.joinedStatus = joinedStatus(number);
        //print( number.joinedStatus);
      }

      //endregion

      //region Add joined status for seller
      for (var number in sentRequestResponse.memberSellerInvite!.seller!) {
        number.joinedStatus = joinedStatus(number);
        //print( number.joinedStatus);
      }

      //endregion

      ///Clear member and seller searched
      sentMemberInvites.clear();
      sentSellerInvites.clear();

      ///Add all member and seller object to searched send result
      sentMemberInvites.addAll(sentRequestResponse.memberSellerInvite!.member!);
      sentSellerInvites.addAll(sentRequestResponse.memberSellerInvite!.seller!);

      ///Sent number List
      //sentNumberList();

      sentCtrl.sink.add(MembershipState.Success);
    } on ApiErrorResponseMessage {
      //print(error.message);
      friendsInviteCtrl.sink.add(MembershipState.Success);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);

      return;
    } catch (error) {
      //print(error);
      friendsInviteCtrl.sink.add(MembershipState.Success);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);

      return;
    }
  }
  //endregion

  //region Membership ranks
  int membershipRank(String? inviteType) {
    //print(inviteType);
    if (inviteType == MembershipTypeModel.noUser.type) {
      return MembershipTypeModel.noUser.priority;
    }
    if (inviteType == MembershipTypeModel.noneMember.type) {
      return MembershipTypeModel.noneMember.priority;
    }
    if (inviteType == MembershipTypeModel.member.type) {
      return MembershipTypeModel.member.priority;
    }
    if (inviteType == MembershipTypeModel.corporateMember.type) {
      return MembershipTypeModel.corporateMember.priority;
    }
    if (inviteType == MembershipTypeModel.seller.type) {
      return MembershipTypeModel.seller.priority;
    }
    if (inviteType == MembershipTypeModel.corporateSeller.type) {
      return MembershipTypeModel.corporateSeller.priority;
    }
    return 0;
  }
//endregion

/*
  1. Remove -> "is_onboarded": false, "invited_by_this_user": false

  2. Joined -> "invited_by_this_user": true

  3. Already joined ->   "is_onboarded": true, "invited_by_this_user": false

 */

  //region

  /*

  Already joined ->

   */

  //region Add joined status
  String joinedStatus(MemberSeller memberSeller) {
    if (!memberSeller.isOnboarded! && !memberSeller.onBoardedByThisUser!) {
      return "remove";
    }
    if (memberSeller.isOnboarded! && memberSeller.onBoardedByThisUser!) {
      return "joined";
    }
    return "already joined";
  }
  //endregion

  //region Add name in model class
  String addNameToModel(String userContactNumber) {
    String name = "";
    //Capture the name
    // for( var pContact in phoneContacts){
    //   name = "${pContact.name.first} ${pContact.name.last}" ;
    //   //print(name);
    //   //Check the number
    // for(var phone in pContact.phones){
    //   if(phone.normalizedNumber== userContactNumber){
    //     return name;
    //     //print("$name  ${phone.normalizedNumber}");
    //   }
    // }
    // }
    return "data";
  }
  //endregion

  ///Not in use
  //region Send Number List
  // void sentNumberList(){
  //   //Clear sent mobile number list
  //   sentMobileNumberList.clear();
  //
  //   for(var data in sentRequestResponse.memberSellerInvite!.member! ){
  //     sentMobileNumberList.add(data.phoneNumber!);
  //   }
  //
  //   for(var data in sentRequestResponse.memberSellerInvite!.seller! ){
  //     sentMobileNumberList.add(data.phoneNumber!);
  //   }
  //   friendsInviteCtrl.sink.add(MembershipState.Success);
  //
  //   //print(sentMobileNumberList.toString());
  //
  //
  //
  // }
  //endregion

  //region Create invite code
  createInviteCode(String mobileNumber, String inviteType, String name) async {
    ///Check if no invite left
    if (inviteType == "MEMBER" &&
        BuyerHomeBloc.userDetailsResponse.userDetail!.memberInviteBalance ==
            0) {
      //Hide send/request options
      isBottomSheetVisible = false;
      inviteCtrl.sink.add(true);
      //Hide Send invite using phone number option
      isVisiblePhoneSheetCtrl.sink.add(false);
      CommonMethods.toastMessage(AppStrings.noMoreMemberInviteLeft, context);
      return;
    }
    if (inviteType == "SELLER" &&
        BuyerHomeBloc.userDetailsResponse.userDetail!.sellerInviteBalance ==
            0) {
      //Hide send/request options
      isBottomSheetVisible = false;
      inviteCtrl.sink.add(true);
      //Hide Send invite using phone number option
      isVisiblePhoneSheetCtrl.sink.add(false);
      CommonMethods.toastMessage(AppStrings.noMoreSellerInviteLeft, context);
      return;
    }

    var message = "";
    try {
      // if(friendsInvitesResponse.data!.isEmpty){
      //  membershipCtrl.sink.add(MembershipState.Loading);
      // }
      //print(mobileNumber);
      //print(inviteType);
      //print(name);
      createInvitationResponse = await membershipService.getInvitationCode(
          mobileNumber,
          inviteType,
          nameTextCtrl.text.isEmpty ? name : nameTextCtrl.text);
      //Hide send/request options
      isBottomSheetVisible = false;
      inviteCtrl.sink.add(true);
      //Hide Send invite using phone number option
      isVisiblePhoneSheetCtrl.sink.add(false);

      ///Types of invite message with code
      //Membership
      if (inviteType == "MEMBER") {
        message =
            "Hey $name.. I have a Membership invite to Swadesic and I want you to join 🙂 "
            "I added you with this number $mobileNumber, "
            "so make sure you sign up using this. Here is your Membership link with invite code- ${createInvitationResponse.data!.inviteCode!}";
      } else {
        message =
            "Hey $name.. I have a Seller Membership invite to Swadesic and I want you to join 🙂 "
            "I added you with this number $mobileNumber, "
            "so make sure you sign up using this. Here is your Seller Membership link with invite code- ${createInvitationResponse.data!.inviteCode!}";
      }

      CommonMethods.share(message);
      //Clear all text field
      mobileNumberTextCtrl.clear();
      nameTextCtrl.clear();

      //Get user detail
      getLoggedInUserDetail();
      //Get sent requests
      getSentRequestApi();
      //askPermissions
      askPermissions();
    } on ApiErrorResponseMessage catch (error) {
      friendsInviteCtrl.sink.add(MembershipState.Success);
      CommonMethods.toastMessage(error.message.toString(), context);

      return;
    } catch (error) {
      friendsInviteCtrl.sink.add(MembershipState.Success);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);

      return;
    }
  }
  //endregion

  //region Delete invite code
  deleteInviteCode(String inviteCode) async {
    try {
      createInvitationResponse =
          await membershipService.deleteInviteCode(inviteCode);
      CommonMethods.toastMessage("Invite code deleted", context);
      getSentRequestApi();

      ///User detail api call
      getLoggedInUserDetail();
    } on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    } catch (error) {
      CommonMethods.toastMessage("Invite code deleted", context);
      getSentRequestApi();
      // CommonMethods.snackBar(AppStrings.error, context);
      return;
    }
  }
  //endregion

  //region Get logged in user
  getLoggedInUserDetail() async {
    try {
      BuyerHomeBloc.userDetailsResponse =
          await userDetailsServices.getLoggedInUserDetail(
              userReference: AppConstants.appData.userReference!);
      // CommonMethods.saveAllData(userDetailsResponse);
      // friendsInviteCtrl.sink.add(MembershipState.Success);
    } on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message.toString(), context);

      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }
  //endregion

  ///Search functionality

  //region On change sent search text
  void onChangeSentSearchText(String value) {
    //Clear member and seller searched

    sentMemberInvites.clear();
    sentSellerInvites.clear();

    //Add Member
    for (var member in sentRequestResponse.memberSellerInvite!.member!) {
      if (member.name!.toLowerCase().contains(value.toLowerCase()) ||
          member.phoneNumber!.toLowerCase().contains(value.toLowerCase())) {
        sentMemberInvites.add(member);
      }
    }
    //print("User ${sentMemberInvites.length}");
    //Add Seller
    for (var seller in sentRequestResponse.memberSellerInvite!.seller!) {
      if (seller.name!.toLowerCase().contains(value.toLowerCase()) ||
          seller.phoneNumber!.toLowerCase().contains(value.toLowerCase())) {
        sentSellerInvites.add(seller);
      }
    }
    //print("Seller ${sentSellerInvites.length}");

    sentCtrl.sink.add(MembershipState.Success);
  }
  //endregion

  //region On change friend and invite search text
  void onChangeFriendInvitesSearchText(String value) {
    ///Clear friends invite Search
    friendsInviteSearch.clear();
    //Add Friends to friend and invite Search
    for (var userInfo in friendsInvitesResponse.friendsInfoList!) {
      if (userInfo.name.toLowerCase().contains(value.toLowerCase()) ||
          userInfo.phonenumber!.toLowerCase().contains(value.toLowerCase())) {
        friendsInviteSearch.add(userInfo);
      }
    }
    //print("User info ${friendsInviteSearch.length}");
    //onSelectFilter();

    friendsInviteCtrl.sink.add(MembershipState.Success);
  }
  //endregion

  //region Apply code
  applyCode({required String referralCode}) async {
    try {
      CommonMethods.closeKeyboard(context);
      //membershipCtrl.sink.add(MembershipState.Loading);
      // String response =await membershipService.applyInviteCode(code: referralCodeTextCtrl.text.replaceAll(" ", ""));
      String response =
          await membershipService.applyInviteCode(code: referralCode);
      if (response == "error") {
        return CommonMethods.toastMessage(
            AppStrings.invalidReferralCode, context);
      } else {
        await getLoggedInUserDetail();
        tabCtrl.sink.add(true);
      }
    } on ApiErrorResponseMessage catch (error) {
      mobileNumberSheetCtrl.sink.add(MembershipState.Success);
      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    } catch (error) {
      mobileNumberSheetCtrl.sink.add(MembershipState.Success);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);

      return;
    }
  }
//endregion

  //region Keybord handle
  KeyEventResult handleKeyEvent(FocusNode node, RawKeyEvent event) {
    if (event.logicalKey == LogicalKeyboardKey.close) {
      //print("Close");
      // referralCodeTextCtrl.clear();
    }

    return event.logicalKey == LogicalKeyboardKey.keyQ
        ? KeyEventResult.handled
        : KeyEventResult.ignored;
  }
  //endregion

  //region get referral code
  getReferralCode() {
    // if(ReferralCodeBloc.firstFieldTextCtrl.text.isEmpty || ReferralCodeBloc.firstFieldTextCtrl.text.isEmpty||ReferralCodeBloc.firstFieldTextCtrl.text.isEmpty){
    //   return CommonMethods.snackBar("Enter a valid code", context);
    // }
    String code = ReferralCodeBloc.firstFieldTextCtrl.text +
        ReferralCodeBloc.secondFieldTextCtrl.text +
        ReferralCodeBloc.thirdFieldTextCtrl.text;
    List<String> codeList = code.split('');
    codeList.insert(1, "-");
    codeList.insert(6, "-");

    if (codeList.length != 11) {
      CommonMethods.toastMessage(AppStrings.invalidReferralCode, context);
      return;
    }
    //print(codeList.join(""));
    applyCode(referralCode: codeList.join(""));
  }
//endregion

//region Dispose
  dispose() {
    membershipFieldCtrl.close();
  }
//endregion
}
