import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/recommended_store_and_user/recommended_store_and_user_bloc.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/data_model/recommended_store_and_user_data_model/recommended_sore_and_user_data_model.dart';
import 'package:swadesic/features/post/feed/feed_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/recommended_store_and_user/recommended_store_and_user.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/recommended_store_and_user_service/recommended_store_and_user_service.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum RecommendedStorePaginationState { Loading, Done, Empty }

class RecommendedStoreAndUserPagination {
  //region Context
  late BuildContext context;
  late RecommendedStoreAndUserBloc recommendedStoreAndUserBloc;
  final bool isRecommendedStore;
  RecommendedStorePaginationState currentApiCallStatus =
      RecommendedStorePaginationState.Done;

  // Fetch data instance
  // RecommendedStoreAndUserDataModel recommendedStoreAndUserDataModel = Provider.of<RecommendedStoreAndUserDataModel>(context);
  //
  // // Store or user list
  // List<RecommendedStoreAndUser> storeOrUserList = isRecommendedStore
  //     ? recommendedStoreAndUserDataModel.recommendedStoreAndUserList
  //     .where((element) => element.entityType == EntityType.STORE.name)
  //     .toList()
  //     : recommendedStoreAndUserDataModel.recommendedStoreAndUserList
  //     .where((element) => element.entityType == EntityType.USER.name)
  //     .toList();
  int limit = 30;
  int offset = 30;

  // bool isLoadingPaginationData = false;
  RecommendedStorePaginationState currentPaginationState =
      RecommendedStorePaginationState.Loading;

  //endregion

  //region Controller
  final recommendedStorePaginationStateCtrl =
      StreamController<RecommendedStorePaginationState>.broadcast();

  //endregion

//region Constructor
  RecommendedStoreAndUserPagination(
      this.context, this.recommendedStoreAndUserBloc, this.isRecommendedStore) {
    //Add initial state in pagination state
    recommendedStorePaginationStateCtrl.sink
        .add(RecommendedStorePaginationState.Done);
    //Scroll listener
    recommendedStoreAndUserBloc.scrollController.addListener(() {
      scrollListener();
    });
    //Pagination controller listener
    recommendedStorePaginationStateCtrl.stream
        .listen((RecommendedStorePaginationState state) {
      feedPaginationControllerListener(state: state);
    });
  }

//endregion

  //region Feed page Controller listener
  void feedPaginationControllerListener(
      {required RecommendedStorePaginationState state}) {
    currentPaginationState = state;
    //print("Status of pagination${state.name}");
  }

  //endregion

  //region Scroll listener
  void scrollListener() async {
    // Fetch data instance
    // RecommendedStoreAndUserDataModel recommendedStoreAndUserDataModel = Provider.of<RecommendedStoreAndUserDataModel>(context);

    //If Page state is empty then return
    if (currentPaginationState == RecommendedStorePaginationState.Empty) {
      // //print("Feed pagination is empty");
      return;
    }

    //1. If list of data is not smaller then 10 and m
    //2. Position pixel is 200 less then max scroll
    if (
        // storeOrUserList.length >= 10 &&
        recommendedStoreAndUserBloc.scrollController.offset >=
                recommendedStoreAndUserBloc
                    .scrollController.position.maxScrollExtent &&
            !recommendedStoreAndUserBloc.scrollController.position.outOfRange) {
      //Increase offset
      offset = limit + offset;
      // Fetch more feed posts when list is scrolled to the bottom
      await getPaginationRecommendedStoreAndUser();
    }
  }

  //endregion

  //region Get recommended store and user
  Future<void> getPaginationRecommendedStoreAndUser() async {
    // Get reference to the RecommendedStoreAndUserDataModel
    var recommendedStoreAndUserDataModel =
        Provider.of<RecommendedStoreAndUserDataModel>(context, listen: false);
    try {
      //If api call status is Loading then return
      if (currentApiCallStatus == RecommendedStorePaginationState.Loading) {
        return;
      }
      //Loading
      recommendedStorePaginationStateCtrl.sink
          .add(RecommendedStorePaginationState.Loading);
      //Current api call status is Loading
      currentApiCallStatus = RecommendedStorePaginationState.Loading;
      //Api call
      List<UserAndStoreInfo> recommendedStoreList =
          await RecommendedStoreAndUserServices().getRecommendedStoreAndUser(
              isRecommendedStore: isRecommendedStore,
              limit: 10,
              offset: recommendedStoreAndUserBloc
                  .recommendedStoreAndUserList.length);

      //Add recommended store or user in Recommended store and user list
      recommendedStoreAndUserBloc.recommendedStoreAndUserList
          .addAll(recommendedStoreList);
      //Clear store in data model
      // recommendedStoreAndUserDataModel.clearAllStoreAndUserList();
      //Add recommended store to data model
      recommendedStoreAndUserDataModel.addRecommendedStoreOrUser(
          storeList: recommendedStoreList);

      //If feed list is empty
      if (recommendedStoreList.isEmpty) {
        //Current api call status is Done
        currentApiCallStatus = RecommendedStorePaginationState.Done;
        // recommendedStoreAndUserBloc.showRecommendedStore = true;
        //Current state
        // currentPaginationState = FeedPaginationState.Empty;
        // isLoadingPaginationData = false;
        return recommendedStorePaginationStateCtrl.sink
            .add(RecommendedStorePaginationState.Empty);
      }
      // isLoadingPaginationData = false;
      //Done
      recommendedStorePaginationStateCtrl.sink
          .add(RecommendedStorePaginationState.Done);
      //Current api call status is Done
      currentApiCallStatus = RecommendedStorePaginationState.Done;
    } on ApiErrorResponseMessage catch (error) {
      // isLoadingPaginationData = false;
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      //Done
      recommendedStorePaginationStateCtrl.sink
          .add(RecommendedStorePaginationState.Done);
      //Current api call status is Done
      currentApiCallStatus = RecommendedStorePaginationState.Done;
    } catch (error) {
      // isLoadingPaginationData = false;
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
      //Done
      recommendedStorePaginationStateCtrl.sink
          .add(RecommendedStorePaginationState.Done);
      //Current api call status is Done
      currentApiCallStatus = RecommendedStorePaginationState.Done;
    }
  }
//endregion
}
