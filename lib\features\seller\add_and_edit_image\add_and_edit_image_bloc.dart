import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/app_config_data_model/app_config_data_model.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';


class AddAndEditImageBloc {
  // region Common Variables
  BuildContext context;
  final ImagePicker picker = ImagePicker();
  //Get app config reference from data model
  late AppConfigDataModel appConfigDataModel;
  // var selectedImage;
  // late List<XFile> selectedImage;

  // endregion


  //region Controller
  final blankCtrl = StreamController<String>.broadcast();



  //endregion

  // region | Constructor |
  AddAndEditImageBloc(this.context);

  // endregion

  // region Init
  void init() async{
    appConfigDataModel = Provider.of<AppConfigDataModel>(context, listen: false);


  }
// endregion


//region Open Gallery
  void openGallery() async {
    int maxImageLimit = appConfigDataModel.appConfig!.productImageLimit;
    try {
      List<XFile>? galleryImage = await picker.pickMultiImage(
        requestFullMetadata: true,
      );

      // If galleryImage is not null and contains images
      if (galleryImage != null && galleryImage.isNotEmpty) {
        int currentImageCount = AppConstants.multipleSelectedImage.length;
        int availableSlots = maxImageLimit - currentImageCount;

        // Check if there's room to add more images
        if (availableSlots > 0) {
          // If the number of selected images exceeds the available slots, take only the allowed number
          if (galleryImage.length > availableSlots) {
            // Add only the allowed number of images
            AppConstants.multipleSelectedImage.addAll(galleryImage.sublist(0, availableSlots));

            // Show a toast message informing the user that the limit was reached
            CommonMethods.toastMessage("${AppStrings.youCantAddMoreThen} $maxImageLimit images", context);
          } else {
            // Add all selected images since they fit within the limit
            AppConstants.multipleSelectedImage.addAll(galleryImage);
          }
        }
        // Pop and return the selected images to the previous screen
        Navigator.pop(context, galleryImage);
      }
    } catch (e) {
      //print("Error: $e");
    }
  }
//endregion



//region Open Camera
  void openCamera()async{
    try{
      XFile imageFromCamera = (await picker.pickImage(source: ImageSource.camera, requestFullMetadata: true))!;
      //If imageFromCamera is not null
      if (imageFromCamera.path.isNotEmpty) {
        Navigator.pop(context, [imageFromCamera]);
      }
    }
    catch(e){
      //print(e);
    }

  }

//endregion




  //region Image crop
  Future<XFile>crop({required File file})async{
    File? croppedFile = await CommonMethods.imageCrop(file: File(file.path),);
    // userDetail.icon = files!.path;
    // isChanged = true;
    if(croppedFile != null){
      return XFile(croppedFile.path);
    }
    else{
      return XFile(file.path);
    }
    // isImageSelected = true;
    // refreshCtrl.sink.add(true);
  }
//endregion







//region Go to Add Product

//endregion



}

