import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';

class StoreCommonWidgets {
  //region Button
  static Widget storeButtons({
    double verticalPadding = 10,
    double horizontalPadding = 10,
    Color? buttonColor,
    required Widget textAndIcon,
    onTapButton,
    double? leftOffset,
    double? rightOffset,
    double? topOffset,
    double? bottomOffset,
  }) {
    buttonColor = buttonColor ?? AppColors.textFieldFill1;
    return InkWell(
      onTap: onTapButton,
      child: Container(
          alignment: Alignment.center,
          constraints: const BoxConstraints(
            minHeight: 40, // Ensure minimum height for proper text display
          ),
          decoration: BoxDecoration(
            color: buttonColor,
            borderRadius: BorderRadius.circular(100),
          ),
          padding: EdgeInsets.only(
            left: horizontalPadding + (leftOffset ?? 0),
            right: horizontalPadding + (rightOffset ?? 0),
            top: verticalPadding + (topOffset ?? 0),
            bottom: verticalPadding + (bottomOffset ?? 0),
          ),
          child: textAndIcon),
    );
  }
  //endregion
}
