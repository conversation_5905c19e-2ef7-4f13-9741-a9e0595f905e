import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/model/seller_all_order_response/tracking_detail_response.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum DeliveryAndReturnPersonLogisticState { Loading, Success, Failed, Empty }

class DeliveryAndReturnPersonLogisticBloc {
  //region Common variable
  late BuildContext context;
  bool isDelayDetailVisible = false;
  late BuyerMyOrderServices buyerMyOrderServices;
  late TrackingDetailResponse trackingDetailResponse;
  final List<SubOrder> suborderList;
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;

  //endregion

//region Text Editing Controller
//endregion

//region Controller
  final deliveryAndReturnPersonLogisticStateCtrl =
      StreamController<DeliveryAndReturnPersonLogisticState>.broadcast();

//endregion
  //region Constructor
  DeliveryAndReturnPersonLogisticBloc(
      this.suborderList, this.buyerSubOrderBloc, this.order,
      {required this.context});
  //endregion
//region Init
  void init() {
    buyerMyOrderServices = BuyerMyOrderServices();
    getTrackingDetail();
  }
//endregion

  //region On tap delay button
  void onTapDelayButton() {
    //Make variable to true
    isDelayDetailVisible = true;
    //Refresh ui
    deliveryAndReturnPersonLogisticStateCtrl.sink
        .add(DeliveryAndReturnPersonLogisticState.Success);
  }
  //endregion

  //region Get tracking detail
  getTrackingDetail() async {
    //region Try
    try {
      deliveryAndReturnPersonLogisticStateCtrl.sink
          .add(DeliveryAndReturnPersonLogisticState.Loading);
      trackingDetailResponse = await buyerMyOrderServices.getTrackingDetail(
          packageNumber: suborderList.first.packageNumber!);
      deliveryAndReturnPersonLogisticStateCtrl.sink
          .add(DeliveryAndReturnPersonLogisticState.Success);
    }
    //endregion
    on ApiErrorResponseMessage {
      deliveryAndReturnPersonLogisticStateCtrl.sink
          .add(DeliveryAndReturnPersonLogisticState.Failed);

      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    } catch (error) {
      deliveryAndReturnPersonLogisticStateCtrl.sink
          .add(DeliveryAndReturnPersonLogisticState.Failed);
      return;
    }
  }
//endregion
}
