import 'dart:async';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:swadesic/features/navigation/navigation_router.dart';
import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/find_your_customers_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/util/app_strings.dart';

import '../../../../util/common_methods.dart';

class AddUserProfilePictureBloc {
  // region Common Methods
  BuildContext context;
  final ImagePicker picker = ImagePicker();
  final ImageCropper imageCrop = ImageCropper();
  late File? files;

  final String userReference;

  // endregion

  //region Controller
  final userProfilePictureCtrl = StreamController<String>.broadcast();
  //endregion

  //region Text Controller
  //endregion

  // region | Constructor |
  AddUserProfilePictureBloc(this.context, this.userReference);
  // endregion

  // region Init
  void init() {}
  // endregion

  //region Open Gallery
  void openGallery() async {
    try {
      XFile? galleryImage = await picker.pickImage(
        source: ImageSource.gallery,
      );
      if (galleryImage == null) return;
      //print(galleryImage.toString());
      ///Crop image
      crop(file: File(galleryImage.path));
    } catch (e) {
      //print("Error is $e");
    }
  }
//endregion

  //region Image crop
  crop({required File file}) async {
    files = await CommonMethods.imageCrop(
      file: File(file.path),
    );

    userProfilePictureCtrl.sink.add(files!.path);
  }
  //endregion

  //region Upload profile pic
  uploadProfilePic() async {
    try {
      ///Api call
      await UploadFileService().addUserProfilePic(
          fileNameWithExtension: files.toString(), filePath: files!.path);
      goToFindYourFriend();
    } on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message.toString(), context);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }
//endregion

//region Go to find your friend
  void goToFindYourFriend() {
    Navigator.of(context).push(MaterialPageRoute(
        builder: (context) => FindYourCustomersScreen(
              visibleNext: true,
              title: AppStrings.findYourFriendsOnSwadesic,
              iSFromOnboarding: true,
            )));
  }
//endregion

  //region Go to Bottom Navigation
  void goToBottomNavigation() {
    Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const NavigationRouter()),
        (Route<dynamic> route) => false);
  }
  //endregion

//region Dispose
  void dispose() {
    userProfilePictureCtrl.close();
  }
//endregion
}
