import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/bottom_navigations/store_bottom_navigation/store_bottom_navigation.dart';
import 'package:swadesic/features/bottom_navigations/user_bottom_navigation/user_bottom_navigation.dart';
import 'package:swadesic/features/buyers/shopping_cart/cart_delivery_address/add_edit_cart_address/add_edit_cart_address.dart';
import 'package:swadesic/features/buyers/shopping_cart/secure_checkout/secure_checkout_bloc.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_bloc.dart';
import 'package:swadesic/features/mobile_number_otp/intro_slider/intro_slider_screen.dart';
import 'package:swadesic/features/mobile_number_otp/mobile_number_otp_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/shopping_cart_address_response/shopping_cart_address_response.dart';
import 'package:swadesic/services/cache_storage/cache_storage_service.dart';
import 'package:swadesic/services/cache_storage/storage_keys.dart';
import 'package:swadesic/services/get_app_data_services/get_app_data_service.dart';
import 'package:swadesic/services/user_address_services/user_address_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/no_internet_handle/no_internet_handle.dart';
import 'package:swadesic/util/universal_link/universal_link.dart';

enum CartDeliveryAddressState { Loading, Success, Failed, Empty }

class CartDeliveryAddressBloc {
  // region Common Methods
  BuildContext context;
  final SecureCheckoutBloc secureCheckoutBloc;
  // endregion

  //region Controller
  final cartAddressCtrl =
      StreamController<CartDeliveryAddressState>.broadcast();
  final addEditFieldCtrl = StreamController<bool>.broadcast();
  //endregion

  //region Text ctrl
  final TextEditingController firstNameTextCtrl = TextEditingController();
  final TextEditingController phoneNumberTextCtrl = TextEditingController();
  final TextEditingController addressTextCtrl = TextEditingController();
  final TextEditingController cityTextCtrl = TextEditingController();
  final TextEditingController pinCodeTextCtrl = TextEditingController();
  final TextEditingController stateTextCtrl = TextEditingController();
  //endregion

  // region | Constructor |
  CartDeliveryAddressBloc(this.context, this.secureCheckoutBloc);
  // endregion

  // region Init
  init() {
    getAddress();
  }
  // endregion

  ///Add address
  //region Add Address Api call
  Future<void> addAddressApiCall({
    required String firstName,
    required String phoneNumber,
    required String address,
    required String city,
    required String pinCode,
    required String state,
  }) async {
    //region Try
    try {
      //Field empty
      if (firstName.isEmpty ||
          phoneNumber.isEmpty ||
          address.isEmpty ||
          city.isEmpty ||
          pinCode.isEmpty ||
          state.isEmpty) {
        CommonMethods.toastMessage(AppStrings.fieldEmpty, context);
        return;
      }
      //Mobile number
      if (phoneNumber.length != 10) {
        CommonMethods.toastMessage(AppStrings.enterValidNumber, context);
        return;
      }
      //Pin code check
      if (pinCode.length < 6) {
        CommonMethods.toastMessage(AppStrings.enterValidPinCode, context);
        return;
      }
      //Add address api call
      ShoppingCartAddress newAddress = await UserAddressService().addAddress(
          address: address,
          city: city,
          pinCode: pinCode,
          state: state,
          firstName: firstName,
          defaultAddress: false,
          mobileNumber: phoneNumber);

      //Add new address data to cart block
      secureCheckoutBloc.shoppingCartAddressResponse.addressList!
          .add(newAddress);

      //Close bottom sheet
      context.mounted ? Navigator.pop(context) : null;

      //Message
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.addressAdded, context)
          : null;
      //Success
      cartAddressCtrl.sink.add(CartDeliveryAddressState.Success);
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
    } catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(
              AppStrings.unableToAddNewAddress, context)
          : null;
    }
  }
  //endregion

  ///Edit address
  //region Edit address Api call
  Future<void> editAddressApiCall(
      {required String firstName,
      required String phoneNumber,
      required String address,
      required String city,
      required String pinCode,
      required String state,
      required int addressId}) async {
    //region Try
    try {
      //Field empty
      if (firstName.isEmpty ||
          phoneNumber.isEmpty ||
          address.isEmpty ||
          city.isEmpty ||
          pinCode.isEmpty ||
          state.isEmpty) {
        CommonMethods.toastMessage(AppStrings.fieldEmpty, context);
        return;
      }
      //Mobile number
      if (phoneNumber.length != 10) {
        CommonMethods.toastMessage(AppStrings.enterValidNumber, context);
        return;
      }
      //Pin code check
      if (pinCode.length < 6) {
        CommonMethods.toastMessage(AppStrings.enterValidPinCode, context);
        return;
      }
      //Add address api call
      ShoppingCartAddress newAddress = await UserAddressService().editAddress(
          address: address,
          city: city,
          pinCode: pinCode,
          state: state,
          name: firstName,
          defaultAddress: false,
          mobileNumber: phoneNumber,
          userAddressId: addressId);

      //Update local address
      var selectedAddress = secureCheckoutBloc
          .shoppingCartAddressResponse.addressList!
          .firstWhere((element) => element.useraddressid == addressId);
      //update data
      selectedAddress.name = firstName;
      selectedAddress.state = state;
      selectedAddress.address = address;
      selectedAddress.city = city;
      selectedAddress.pincode = pinCode;
      selectedAddress.phoneNumber = phoneNumber;

      //Close bottom sheet
      context.mounted ? Navigator.pop(context) : null;

      //Message
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.addressUpdated, context)
          : null;
      //Success
      cartAddressCtrl.sink.add(CartDeliveryAddressState.Success);
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
    } catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.unableToEditAddress, context)
          : null;
    }
  }
  //endregion

  ///Get address
  //region Get Addresses
  Future<void> getAddress() async {
    //region Try
    try {
      //Loading
      //Get address
      ShoppingCartAddressResponse userAddressResponse =
          await UserAddressService().getUserAddress();
      //Add address to the address list

      List<ShoppingCartAddress> newAddress = [];
      for (var data in userAddressResponse.addressList!) {
        var newAddress = secureCheckoutBloc
            .shoppingCartAddressResponse.addressList!
            .where((element) => element.useraddressid! != data.useraddressid!);
        //print(newAddress.length);
        newAddress = newAddress;
      }
      secureCheckoutBloc.shoppingCartAddressResponse.addressList!
          .addAll(newAddress);

      //Success
      cartAddressCtrl.sink.add(CartDeliveryAddressState.Success);
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message!, context)
          : null;
    } catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
    }
  }
  //endregion

  ///Delete address
  //region Delete address
  Future<void> deleteAddress(
      {required ShoppingCartAddress shoppingCartAddress}) async {
    //region Try
    try {
      await UserAddressService()
          .deleteAddress(shoppingCartAddress.useraddressid!);
      //If deleted address is selected then remove that
      if (secureCheckoutBloc.selectedAddress.useraddressid ==
          shoppingCartAddress.useraddressid) {
        secureCheckoutBloc.selectedAddress = ShoppingCartAddress();
      }
      //remove local address
      secureCheckoutBloc.shoppingCartAddressResponse.addressList!.removeWhere(
          (element) =>
              element.useraddressid == shoppingCartAddress.useraddressid!);
      //Message
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.addressDeleted, context)
          : null;
      //Success
      cartAddressCtrl.sink.add(CartDeliveryAddressState.Success);
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message!, context)
          : null;
    } catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
    }
  }
  //endregion

  //region On select address
  void onSelectAddress(
      {required ShoppingCartAddress shoppingCartAddress}) async {
    //Select ed address
    secureCheckoutBloc.selectedAddress = shoppingCartAddress;
    //Save address id to cache memory
    await CacheStorageService().saveString(StorageKeys.shoppingCartAddress,
        shoppingCartAddress.useraddressid!.toString());
    //Success
    cartAddressCtrl.sink.add(CartDeliveryAddressState.Success);
  }
  //endregion

  //region On tap address
  void onTapAddAddress({ShoppingCartAddress? shoppingCartAddress}) {
    CommonMethods.appBottomSheet(
        screen: AddEditCartAddress(
          isAddAddress: true,
          cartDeliveryAddressBloc: this,
        ),
        context: context,
        bottomSheetName: "Add new address");
  }
  //endregion

  //region On tap edit address
  void onTapEditAddress({ShoppingCartAddress? shoppingCartAddress}) {
    CommonMethods.appBottomSheet(
        screen: AddEditCartAddress(
            isAddAddress: false,
            cartDeliveryAddressBloc: this,
            shoppingCartAddress: shoppingCartAddress),
        context: context,
        bottomSheetName: "Edit address");
  }
  //endregion

//region Dispose
  void dispose() {
    cartAddressCtrl.close();
    addEditFieldCtrl.close();
  }
//endregion
}
