import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:marquee/marquee.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/buyer_home/home_access/home_access_bloc.dart';
import 'package:swadesic/features/buyers/buyer_home/home_access/support_score_card_overlay.dart';
import 'package:swadesic/features/widgets/level_badge/level_badge.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/features/communication/welcome_bottom_sheet.dart';

//region Home access
class HomeAccess extends StatefulWidget {
  final bool showSupportScore;
  const HomeAccess({Key? key, this.showSupportScore = false}) : super(key: key);

  @override
  State<HomeAccess> createState() => _HomeAccessState();
}
//endregion

class _HomeAccessState extends State<HomeAccess> {
  //region Bloc
  late HomeAccessBloc homeAccessBloc;

  //endregion
  //region Init
  @override
  void initState() {
    homeAccessBloc = HomeAccessBloc(context);
    homeAccessBloc.init();
    super.initState();
  }

  //endregion
  //region Dispose
  @override
  void dispose() {
    homeAccessBloc.dispose();
    super.dispose();
  }

  //endregion
  @override
  Widget build(BuildContext context) {
    return body();
  }

  //region Body
  Widget body() {
    return Column(
      children: [
        ///Support Score Widget
        if (widget.showSupportScore) _buildSupportScoreWidget(),

        ///Find your customers on swadesic
        // AppCommonWidgets.settingOption(
        //     horizontalPadding: 10,
        //     prefixIconPadding: 5,
        //     optionText: AppStrings.findYourFriendsOnSwadesic,
        //     prefixIcon: AppImages.findYourFriendsIcon,
        //     onTap: () {
        //       homeAccessBloc.goToFindYourCustomer();
        //     }),

        ///Supported stores
        AppCommonWidgets.settingOption(
            horizontalPadding: 10,
            prefixIconPadding: 5,
            optionText: AppStrings.supportedStores,
            prefixIcon: AppImages.supportStore,
            onTap: () {
              homeAccessBloc.onTapFavouriteStore();
            }),

        // ///Supported stores
        // AppCommonWidgets.settingOption(
        //     horizontalPadding: 10,
        //     prefixIconPadding: 5,
        //     optionText: AppStrings.supportedStores,
        //     prefixIcon: AppImages.recentlyVisitedStore,
        //     onTap: () {
        //       homeAccessBloc.onTapFavouriteStore();
        //     }),

        ///Recently visited stores
        AppCommonWidgets.settingOption(
            horizontalPadding: 10,
            prefixIconPadding: 5,
            optionText: AppStrings.recentStore,
            prefixIcon: AppImages.recentlyVisitedStore,
            onTap: () {
              homeAccessBloc.onTapRecentlyVisitedStore();
            }),

        ///My  order
        AppCommonWidgets.settingOption(
            horizontalPadding: 10,
            prefixIconPadding: 5,
            optionText: AppStrings.myOrders,
            prefixIcon: AppImages.orders,
            onTap: () {
              homeAccessBloc.goToMyOrderScreen();
            }),

        // ///Affiliate program
        // AppCommonWidgets.settingOption(
        //   subOption: Text(AffiliateProgramDetail().title!,
        //     maxLines: 1,
        //     overflow: TextOverflow.ellipsis,
        //     style: AppTextStyle.smallText(textColor: AppColors.brandGreen),),
        //     horizontalPadding: 10,
        //     prefixIconPadding: 5,
        //     optionText: AppStrings.affiliateProgram,
        //     prefixIcon: AppImages.affiliateIcon,
        //     onTap: () {
        //       homeAccessBloc.goToAffiliateProgram();
        //     }),

        // SizedBox(
        //   height: 100,
        //   child: Row(
        //     children: [
        //       Marquee(
        //         text: "Ram ram",
        //
        //         style: AppTextStyle.smallText(textColor: AppColors.brandGreen),
        //         scrollAxis: Axis.horizontal,
        //         crossAxisAlignment: CrossAxisAlignment.start,
        //         blankSpace: 20.0,
        //         velocity: 50.0,
        //         // pauseAfterRound:  Duration.zero,
        //         startPadding: 50.0,
        //         // accelerationDuration: Duration.zero,
        //         // accelerationCurve: Curves.slowMiddle,
        //         // decelerationDuration: const Duration(seconds: 5),
        //         decelerationCurve: Curves.slowMiddle,
        //       )
        //     ],
        //   ),
        // ),

        ///Invitees
        AppCommonWidgets.settingOption(
            horizontalPadding: 10,
            prefixIconPadding: 5,
            optionText: AppStrings.myInvitees,
            prefixIcon: AppImages.invitees,
            onTap: () {
              homeAccessBloc.onTapInvitees(context);
            }),

        ///Create store
        AppCommonWidgets.settingOption(
            horizontalPadding: 10,
            prefixIconPadding: 5,
            optionText: AppStrings.createStore,
            prefixIcon: AppImages.createStore,
            onTap: () {
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                backgroundColor: Colors.transparent,
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.vertical(
                    top: Radius.circular(20),
                  ),
                ),
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.85,
                ),
                builder: (context) => WelcomeBottomSheet(
                  inviteCode: AppConstants.appData.userReference!,
                  homeAccessBloc: homeAccessBloc,
                ),
              );
            }),

        ///Switch account
        AppCommonWidgets.settingOption(
            horizontalPadding: 10,
            prefixIconPadding: 5,
            optionText: AppStrings.switchAccount,
            prefixIcon: AppImages.switchAccount,
            onTap: () {
              homeAccessBloc.goToSellerAccountScreen(
                  userReference: AppConstants.appData.userReference!);
            }),

        ///Support
        AppCommonWidgets.settingOption(
            horizontalPadding: 10,
            prefixIconPadding: 5,
            optionText: AppStrings.support,
            prefixIcon: AppImages.support,
            onTap: () {
              homeAccessBloc.onTapGoToSupport(isReport: true);
            }),

        ///Suggest an feature
        AppCommonWidgets.settingOption(
            horizontalPadding: 10,
            prefixIconPadding: 5,
            optionText: AppStrings.suggestAnIdea,
            prefixIcon: AppImages.supportIdea,
            onTap: () {
              homeAccessBloc.onTapGoToSupport(isReport: false);
            }),

        ///Swadesic Blog 
        AppCommonWidgets.settingOption(
            horizontalPadding: 10,
            prefixIconPadding: 5,
            optionText: AppStrings.swadesicBlog,
            prefixIcon: AppImages.swadesicBlog,
            onTap: () {
              homeAccessBloc.onTapGoToSwadesicBlog();
            }),
      ],
    );
  }

//endregion

  //region Support Score Widget
  Widget _buildSupportScoreWidget() {
    // Get user details from BuyerHomeBloc
    final userDetail = BuyerHomeBloc.userDetailsResponse.userDetail;

    // Return empty container if user detail is null
    if (userDetail == null) {
      return const SizedBox();
    }

    // Level ranges mapping
    final Map<int, Map<String, double>> levelRanges = {
      1: {'min': 0, 'max': 10000},
      2: {'min': 10000, 'max': 50000},
      3: {'min': 50000, 'max': 100000},
      4: {'min': 100000, 'max': 200000},
      5: {'min': 200000, 'max': 500000},
      6: {'min': 500000, 'max': 700000},
      7: {'min': 700000, 'max': 1000000},
      8: {'min': 1000000, 'max': 1500000},
      9: {'min': 1500000, 'max': 1500000}, // Max level
    };

    // Get support score
    final supportScore = userDetail.userSupportScore ?? 0.0;

    // Get current level from user info instead of calculating from support score
    final currentLevel = int.tryParse(userDetail.userLevel ?? "1") ?? 1;

    // Get current level range
    final currentRange = levelRanges[currentLevel]!;
    final minScore = currentRange['min']!;

    // Get next level minimum score for progress calculation
    final nextLevelMinScore = currentLevel < 9
        ? levelRanges[currentLevel + 1]!['min']!
        : currentRange['max']!;

    // Calculate progress towards next level
    double progress = 0.0;
    if (nextLevelMinScore > minScore) {
      progress = (supportScore - minScore) / (nextLevelMinScore - minScore);
      progress = progress.clamp(0.0, 1.0);
    }

    return GestureDetector(
      onTap: () {
        _openSupportScoreCard();
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        padding: const EdgeInsets.all(15),
        decoration: BoxDecoration(
          color: AppColors.appWhite,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.lightGray2, width: 1),
          boxShadow: [
            BoxShadow(
              color: AppColors.appBlack.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Top row with Support Score and Level Badge
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Support Score : ${supportScore.toInt()}",
                  style:
                      AppTextStyle.pageHeading3(textColor: AppColors.appBlack),
                ),
                // Level badge using the LevelBadge widget
                LevelBadge.createLevelBadge(
                  level: currentLevel.toString(),
                  badgeType: LevelBadgeType.user,
                  width: 28,
                  height: 28,
                  borderWidth: 1.5,
                  fontSize: 16,
                ),
              ],
            ),

            verticalSizedBox(12),

            // Progress bar with circular indicator (same as store valuation widget)
            Stack(
              children: [
                Container(
                  width: double.infinity,
                  height: 12,
                  decoration: BoxDecoration(
                    color: AppColors.textFieldFill2,
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: progress,
                  child: Container(
                    height: 12,
                    decoration: BoxDecoration(
                      color: AppColors.brandBlack,
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                ),
                // Circular indicator at the end of progress
                Positioned(
                  left: (MediaQuery.of(context).size.width - 44) * progress - 8,
                  top: 0,
                  child: Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: AppColors.appWhite,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: AppColors.brandBlack,
                        width: 2,
                      ),
                    ),
                  ),
                ),
              ],
            ),

            verticalSizedBox(8),

            // Points to next level text (aligned to the right)
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  currentLevel < 9
                      ? "${(nextLevelMinScore - supportScore).toInt()} points to Level ${currentLevel + 1}"
                      : "Max Level Reached",
                  style: AppTextStyle.smallText3(textColor: AppColors.appBlack),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  //endregion

  //region Open Support Score Card
  void _openSupportScoreCard() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return const SupportScoreCardOverlay();
      },
    );
  }
  //endregion

//region Common access
  Widget commonAccess(
      {required icon, required String accessName, required onPressed}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CupertinoButton(
          padding: EdgeInsets.zero,
          onPressed: () {
            onPressed();
          },
          child: Container(
            padding: const EdgeInsets.all(10),
            child: Row(
              children: [
                SizedBox(
                    height: 30,
                    width: 30,
                    child: SvgPicture.asset(
                      icon,
                      height: 30,
                      width: 30,
                      color: AppColors.brandBlack,
                    )),
                horizontalSizedBox(20),
                Text(
                  accessName,
                  style:
                      AppTextStyle.access0(textColor: AppColors.writingBlack0),
                )
                // appText(accessName,)
              ],
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: divider(),
        ),
      ],
    );
  }
//endregion
}
