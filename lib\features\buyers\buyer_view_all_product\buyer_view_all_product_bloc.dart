import 'dart:async';

import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

import '../../../model/api_response_message.dart';
import '../../../model/store_product_response/store_product_response.dart';
import '../../../services/store_product_services/store_product_services.dart';
import 'buyer_view_product/buyer_view_product_list/buyer_view_product_screen.dart';

enum BuyerViewAllProductState { Loading, Success, Failed }

class BuyerViewAllProductBloc {
  // region Common Variables
  BuildContext context;

  /// products
  late StoreProductServices storeProductServices;
  //late StoreProductResponse storeProductResponse;
  final String searchString;

  final List<Product> productList;

  // endregion

  //region Controller
  final screenRefreshCtrl = StreamController<bool>.broadcast();

  //endregion

  //region Text Editing Controller
  TextEditingController searchTextEditingCtrl = TextEditingController();
  //endregion

  // region | Constructor |
  BuyerViewAllProductBloc(this.context, this.productList, this.searchString);
  // endregion

  // region Init
  void init() {
    storeProductServices = StoreProductServices();
    //Add searched text into TextField
    searchTextEditingCtrl.text = searchString;

    //storeProductResponse = StoreProductResponse();
  }
// endregion

  //region Save and un-Save Product
  saveUnSaveProduct(String productReference, bool status, int index) async {
    ///Access check
    if (BuyerHomeBloc.userDetailsResponse.userDetail!.saveProduct! != "1") {
      return CommonMethods.toastMessage(AppStrings.noAccess, context);
    }
    try {
      productList[index].saveStatus = !status;
      screenRefreshCtrl.sink.add(true);

      // buyerViewStoreProductCtrl.sink.add(BuyerViewStoreState.Loading);
      await storeProductServices.saveUnSaveProduct(productReference);
      //
    } on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    } catch (error) {
      screenRefreshCtrl.sink.add(true);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }
  //endregion

  //region Go to Product View Screen
  void goToViewProductScreen(int index) {
    var screen = BuyerViewProductScreen(
      openingFrom: SearchScreenEnum.SEARCH_RESULT,
      index: index,
      productList: productList,
      searchedText: searchString,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      screenRefreshCtrl.sink.add(true);
    });
  }
  //endregion
}
