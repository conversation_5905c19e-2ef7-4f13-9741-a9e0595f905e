import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/post/post_screen.dart';
import 'package:swadesic/features/store_external_reviews/create_store_external_review_request_screen.dart';
import 'package:swadesic/model/store_info/store_info.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/features/common_buyer_seller_screen/report/report_screen.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';

class StoreReviewsScreen extends StatefulWidget {
  final StoreInfo storeInfo;

  const StoreReviewsScreen({
    Key? key,
    required this.storeInfo,
  }) : super(key: key);

  @override
  State<StoreReviewsScreen> createState() => _StoreReviewsScreenState();
}

class _StoreReviewsScreenState extends State<StoreReviewsScreen>
    with AutomaticKeepAliveClientMixin<StoreReviewsScreen> {
  // Keep alive
  @override
  bool get wantKeepAlive => true;

  // Controller for the PostScreen
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // Method to handle creating external review link
  void _onTapCreateExternalReviewLink() {
    // Create a StoreInfo object with the available data
    StoreInfo store = widget.storeInfo;

    // Navigate to create store external review request screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreateStoreExternalReviewRequestScreen(
          store: store,
          storeImage: store.icon!,
        ),
      ),
    );
  }

  //region Go to report screen
  void goToReportScreen() async {
    //If non register user
    if (CommonMethods().isStaticUser()) {
      CommonMethods().goToSignUpFlow();
      return;
    }
    await Future.delayed(Duration.zero);
    var screen = ReportScreen(
      reference: widget.storeInfo.storeReference!,
      isStore: true,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Scaffold(
      appBar: AppCommonWidgets.mainAppBar(
        context: context,
        isTitleVisible: true,
        title:
            '${widget.storeInfo.storeName}\'s ${AppStrings.reviewsLowerCase}',
        isLeadingVisible: true,
        isMembershipVisible: false,
        isCartVisible: false,
        isDefaultMenuVisible: false,
        isCustomMenuVisible: true,
        customMenuButton: _buildCustomMenu(),
      ),
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          SliverToBoxAdapter(
            child: _buildOverallRatingsSection(),
          ),
          SliverFillRemaining(
            child: PostScreen(
              storeOrUserReference: widget.storeInfo.storeReference!,
              previousScrollController: _scrollController,
              contentType: PostScreenContentType.storeReviews,
            ),
          ),
        ],
      ),
    );
  }

  // Build overall ratings section
  Widget _buildOverallRatingsSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Overall ratings',
            style: AppTextStyle.sectionHeading(
              textColor: AppColors.appBlack,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildRatingCard(
                  title: 'Average Store Rating',
                  rating: widget.storeInfo.storeAvgRating ?? 0.0,
                  reviewCount: widget.storeInfo.storeReviewCount ?? 0,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildRatingCard(
                  title: 'Average Product Rating',
                  rating: widget.storeInfo.storeProductAvgRating ?? 0.0,
                  reviewCount: widget.storeInfo.storeProductReviewCount ?? 0,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          Text(
            'Reviews',
            style: AppTextStyle.sectionHeading(
              textColor: AppColors.appBlack,
            ),
          ),
        ],
      ),
    );
  }

  // Build individual rating card
  Widget _buildRatingCard({
    required String title,
    required double rating,
    required int reviewCount,
  }) {
    return IntrinsicHeight(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppColors.textFieldFill1,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title with fixed height
            Text(
              title,
              style: AppTextStyle.smallText(
                textColor: AppColors.appBlack,
              ).copyWith(
                  fontSize: 12), // Slightly smaller font size for better fit
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),

            const SizedBox(height: 8),
            // Rating row with fixed height
            Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  rating.toStringAsFixed(1),
                  style: AppTextStyle.access0(
                    textColor: AppColors.appBlack,
                  ),
                ),
                const SizedBox(width: 4),
                SvgPicture.asset(
                  AppImages.star,
                  height: 14,
                  width: 14,
                ),
                const SizedBox(width: 4),
                Flexible(
                  child: Text(
                    '($reviewCount${CommonMethods.singularPluralText(
                      item: reviewCount,
                      isValueReturn: false,
                      singular: AppStrings.reviewLowerCase,
                      plural: AppStrings.reviewsLowerCase,
                    )})',
                    style: AppTextStyle.access0(
                      textColor: AppColors.appBlack,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Custom menu for store reviews screen
  Widget _buildCustomMenu() {
    return PopupMenuButton(
      padding: EdgeInsets.zero,
      icon: SvgPicture.asset(AppImages.drawerIcon),
      itemBuilder: (context) {
        return [
          PopupMenuItem<int>(
            height: 0,
            value: 0,
            onTap: () async {
              goToReportScreen();
            },
            padding: EdgeInsets.zero,
            child: SizedBox(
              width: 150,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                      padding: const EdgeInsets.all(10),
                      child:
                          AppCommonWidgets.menuText(text: AppStrings.report)),
                  divider()
                ],
              ),
            ),
          ),
          // Create Store Review Request Link
          PopupMenuItem<int>(
            height: 0,
            value: 0,
            onTap: () async {
              await Future.delayed(Duration.zero);
              _onTapCreateExternalReviewLink();
            },
            padding: EdgeInsets.zero,
            child: SizedBox(
              width: 200,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    child: AppCommonWidgets.menuText(
                      text: 'Create Store Review Link',
                    ),
                  ),
                  Container(
                    height: 1,
                    color: Colors.grey.withOpacity(0.3),
                  ),
                ],
              ),
            ),
          ),
        ];
      },
    );
  }
}
