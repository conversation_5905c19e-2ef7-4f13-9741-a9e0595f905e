import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_text_style.dart';

class VerificationInProgressCard {
  static Widget buildTitle() {
    return Text(
      "Verification in Progress",
      style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
    );
  }

  static Widget buildContent() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Clock icon
        Icon(
          Icons.access_time,
          color: AppColors.appBlack,
          size: 24,
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            "We're reviewing your documents. In the meantime, add products and request reviews to get ready for orders.",
            style: AppTextStyle.smallTextRegular(textColor: AppColors.appBlack)
                .copyWith(
              fontSize: 12,
            ),
          ),
        ),
      ],
    );
  }
}
