import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:swadesic/features/common_buyer_seller_screen/app_web_view/app_web_view_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';

//region App web view
class AppWebView extends StatefulWidget {
  final String url;
  final bool showAppBar;

  const AppWebView({Key? key, required this.url, this.showAppBar = true})
      : super(key: key);

  @override
  State<AppWebView> createState() => _AppWebViewState();
}
//endregion
class _AppWebViewState extends State<AppWebView> {
  late AppWebViewBloc appWebViewBloc;
  InAppWebViewController? _webViewController;

  @override
  void initState() {
    appWebViewBloc = AppWebViewBloc(context, widget.url);
    appWebViewBloc.init();
    super.initState();
  }

  @override
  void dispose() {
    appWebViewBloc.dispose();
    super.dispose();
  }

  Future<bool> _handleBackNavigation() async {
    if (_webViewController != null && await _webViewController!.canGoBack()) {
      _webViewController!.goBack();
      return false; // Prevent exiting the WebView
    }
    return true; // Exit WebView
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _handleBackNavigation,
      child: Scaffold(
        appBar: widget.showAppBar ? appBar() : null,
        body: SafeArea(child: body()),
      ),
    );
  }

  AppBar appBar() {
    return AppBar(
      leading: InkWell(
        onTap: () async {
          if (_webViewController != null && await _webViewController!.canGoBack()) {
            _webViewController!.goBack();
          } else {
            Navigator.pop(context);
          }
        },
        child: Container(
          padding: const EdgeInsets.all(16),
          child: SvgPicture.asset(
            AppImages.close,
            height: 12,
            width: 12,
            color: AppColors.appBlack,
          ),
        ),
      ),
      elevation: 0,
      backgroundColor: AppColors.appWhite,
      actions: [
        myMenuButton(),
      ],
    );
  }

  // Keep your original myMenuButton implementation
  Widget myMenuButton() {
    return PopupMenuButton<int>(
      padding: EdgeInsets.zero,
      icon: SvgPicture.asset(AppImages.drawerIcon, color: AppColors.appBlack),
      itemBuilder: (context) {
        return [
          PopupMenuItem<int>(
            value: 0,
            onTap: () async {
              CommonMethods.openUrl(url: widget.url);
            },
            child: SizedBox(
              width: 150,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.all(0),
                    child: AppCommonWidgets.menuText(
                      text: AppStrings.openInBrowser,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ];
      },
    );
  }

  Widget body() {
    return Column(
      children: [
        StreamBuilder<int>(
          stream: appWebViewBloc.pageLoadingCtrl.stream,
          initialData: 0,
          builder: (context, snapshot) {
            return Visibility(
              visible: snapshot.data != 100,
              child: LinearPercentIndicator(
                barRadius: const Radius.circular(10),
                padding: EdgeInsets.zero,
                lineHeight: 5.0,
                percent: snapshot.data!.toDouble() / 100,
                backgroundColor: AppColors.tertiaryGreen,
                progressColor: AppColors.activeGreen,
              ),
            );
          },
        ),
        Expanded(
          child: InAppWebView(
            initialUrlRequest: URLRequest(url: Uri.parse(widget.url)),
            onWebViewCreated: (controller) {
              _webViewController = controller;
            },
            onReceivedServerTrustAuthRequest: (controller, challenge) async {
              return ServerTrustAuthResponse(
                action: ServerTrustAuthResponseAction.PROCEED,
              );
            },
            onProgressChanged: (webViewController, progress) {
              appWebViewBloc.pageLoadingCtrl.sink.add(progress);
            },
          ),
        ),
      ],
    );
  }
}
