import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:swadesic/util/app_constants.dart';


class BuyerAddImageBloc {
  // region Common Variables
  BuildContext context;
  final ImagePicker picker = ImagePicker();
  // var selectedImage;
  // late List<XFile> selectedImage;

  // endregion


  //region Controller
  final blankCtrl = StreamController<String>.broadcast();



  //endregion

  // region | Constructor |
  BuyerAddImageBloc(this.context);

  // endregion

  // region Init
  void init() async{


  }
// endregion


//region Open Gallery
  void openGallery()async{
    try{
     List <XFile> galleryImage = (await picker.pickMultiImage(requestFullMetadata: true));

     AppConstants.multipleSelectedImage.addAll(galleryImage);

      if(galleryImage == null) return;
      //print(galleryImage.toString());
      // selectedImage = File(image.path);
      // AppConstants.captureImages.insert(0,XFile(""));

      //print(  AppConstants.multipleSelectedImage.length);
     // var screen =  BuyerSelectedImagePreviewScreen();
     // var route = MaterialPageRoute(builder: (context) => screen);
     // Navigator.push(context, route);
     Navigator.pop(context);
    }
    catch(e){
      //print("Error is $e");
    }

  }

//endregion

//region Open Camera
  void openCamera()async{
    try{
      XFile imageFromCamera = (await picker.pickImage(source: ImageSource.camera,maxWidth:800,maxHeight: 800))!;
      if(imageFromCamera == null) return;
      ///
      File image = File(imageFromCamera.path);
      //print(image.path.toString());
      //print(imageFromCamera.toString());
      // selectedImage = File(image.path);
      AppConstants.multipleSelectedImage.add(imageFromCamera);
     // //print(AppConstants.captureImages.length);
     //  var screen =  SelectedImagePreviewScreen();
     //  var route = MaterialPageRoute(builder: (context) => screen);
     //  Navigator.pushReplacement(context, route);
      // var route = MaterialPageRoute(builder: (context) => screen);
      Navigator.pop(context);
      // Navigator.pushReplacement(context, MaterialPageRoute(builder: (BuildContext context) => SelectedImageScreen(image: selectedImage)));
    }
    catch(e){
      //print(e);
    }

  }

//endregion








//region Go to Add Product

//endregion



}
