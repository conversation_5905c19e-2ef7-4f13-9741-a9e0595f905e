import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/order_response/customer_details_in_orders_response.dart';
import 'package:swadesic/model/seller_all_order_response/seller_order_details.dart';
import 'package:swadesic/services/seller_all_order_service/seller_all_order_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum BuyerMyOrderDetailsState { Loading, Success, Failed, Empty }

enum UserDetailState { Loading, Success, Failed, Empty }

class BuyerMyOrderDetailBloc {
  // region Common Variables
  BuildContext context;
  late SellerAllOrderServices sellerAllOrderServices;
  late SellerOrdersDetailsResponse sellerOrdersDetailsResponse;
  bool isGrandTotalVisible = false;
  bool isProductOrderedVisible = false;
  bool isDeliveryContactVisible = true;

  bool isNotesVisible = true;
  bool isBillingInfoVisible = true;
  late CustomerDetailsInOrders sellerAllOrderUserDetailResponse =
      CustomerDetailsInOrders();
  // final Order order;
  final String orderNumber;
  List<GroupedCancelledOrReturnedOrder> groups = [];
  // endregion

  //region Controller
  final dropDownCtrl = StreamController<bool>.broadcast();
  final allOrderDetailCtrl =
      StreamController<BuyerMyOrderDetailsState>.broadcast();
  final userDetailStateCtrl = StreamController<UserDetailState>.broadcast();

  //endregion

  // region | Constructor |
  BuyerMyOrderDetailBloc(this.context, this.orderNumber);
  // endregion

  // region Init
  void init() {
    sellerAllOrderServices = SellerAllOrderServices();
    getOrderDetails();
    //splitPrice();
  }
// endregion

  //region Get Order details

  void getOrderDetails() async {
    // sellerOrdersDetailsResponse = await sellerAllOrderServices.getOrderDetails(orderNumber);
    //
    // //print(sellerOrdersDetailsResponse);
    // return;
    /*
    1. To get Dynamic key and list object we capture productDetails as a string.
    2. Decode the string to get json object.
    3. Find the dynamic "group[int]" and put all onjects into
     */
    //region Try
    try {
      allOrderDetailCtrl.sink.add(BuyerMyOrderDetailsState.Loading);

      sellerOrdersDetailsResponse =
          await sellerAllOrderServices.getOrderDetails(orderNumber);

      //Remove last list from price split
      // sellerOrdersDetailsResponse.grandTotals!.removeLast();
      // var productDetails = sellerOrdersDetailsResponse.data!.productDetail;
      var productDetails = sellerOrdersDetailsResponse.productDetail;

      var decodedData = json.decode(productDetails!);

      groups.clear();

      for (int i = 0; i < decodedData.length; i++) {
        var groupName = "group${i + 1}";
        var groupData = decodedData[groupName];
        List<GroupedCancelledOrReturnedOrder> posts =
            List<GroupedCancelledOrReturnedOrder>.from(groupData.map(
                (model) => GroupedCancelledOrReturnedOrder.fromJson(model)));
        for (var data in posts) {
          data.groupName = groupName;
        }
        // posts[i].groupName = groupName;
        //print(posts.length);

        groups.addAll(posts);

        //print(groups);
      }
      //print(groups);
      //Success
      allOrderDetailCtrl.sink.add(BuyerMyOrderDetailsState.Success);
      //Get user detail
      getUserDetail();
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      //Failed
      allOrderDetailCtrl.sink.add(BuyerMyOrderDetailsState.Failed);
      context.mounted
          ? CommonMethods.toastMessage(error.message!, context)
          : null;
    } catch (error) {
      //print(error);
      //Failed
      allOrderDetailCtrl.sink.add(BuyerMyOrderDetailsState.Failed);
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
    }
  }
//endregion

  //region Order returned back
  getUserDetail() async {
    //region Try
    try {
      //Loading
      userDetailStateCtrl.sink.add(UserDetailState.Loading);
      sellerAllOrderUserDetailResponse =
          await sellerAllOrderServices.sellerAllOrderUserDetail(
              orderNumber, AppConstants.appData.userId.toString());
      //Success
      userDetailStateCtrl.sink.add(UserDetailState.Success);
    }
    //endregion
    on ApiErrorResponseMessage {
      userDetailStateCtrl.sink.add(UserDetailState.Failed);
    } catch (error) {
      //print(error);
      userDetailStateCtrl.sink.add(UserDetailState.Failed);
    }
  }
//endregion

  //region Split price
  String breakupPrice({required String rawBreakupPrice}) {
    String breakupPrice = rawBreakupPrice;
    List<String> finalList = [];

    List<String> breakupPriceSplit = breakupPrice.split("+");

    //print(breakupPriceSplit);

    ///Add rupee and + .
    for (var price in breakupPriceSplit) {
      if (price != breakupPriceSplit.last) {
        finalList.add(" ₹ $price + ");
      }
      finalList.add(" ₹ $price ");
    }
    //print(finalList.join());

    ///Return final structure

    return finalList.join();
  }
  //endregion

  //region Go To Store Screen
  goToBuyerViewStore({required String storeReference}) {
    ///Access check
    if (BuyerHomeBloc.userDetailsResponse.userDetail!.viewStores! != "1") {
      return CommonMethods.toastMessage(AppStrings.noAccess, context);
    }

    var screen = BuyerViewStoreScreen(
      storeReference: storeReference,
    );
    // var screen = BuyerViewStoreScreen(storeReference: "S4185590",);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      // screenRefreshCtrl.sink.add(true);
    });
  }
//endregion

  //region On tap Delivery and contact
  void onTapDeliveryContact() {
    isDeliveryContactVisible = !isDeliveryContactVisible;
    dropDownCtrl.sink.add(true);
  }
//endregion

//region On tap Notes
  void onTapNotes() {
    isNotesVisible = !isNotesVisible;
    dropDownCtrl.sink.add(true);
  }
//endregion

//region On tap Billing info
  void onTapBillingInfo() {
    isBillingInfoVisible = !isBillingInfoVisible;
    dropDownCtrl.sink.add(true);
  }
//endregion

//region On tap Grand total
  void onTapGrandTotal() {
    sellerAllOrderServices = SellerAllOrderServices();
    isGrandTotalVisible = !isGrandTotalVisible;
    dropDownCtrl.sink.add(true);
  }
//endregion

//region On tap Product ordered
  void onTapProductOrdered() {
    isProductOrderedVisible = !isProductOrderedVisible;
    dropDownCtrl.sink.add(true);
  }
//endregion
}
