import 'dart:async';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/bottom_navigations/store_bottom_navigation/store_bottom_navigation_bloc.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_comment/buyer_product_comment_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/all_store_notification.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/user_or_store_notification.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_sub_order/seller_sub_order_screen.dart';
import 'package:swadesic/features/support/issue_suggestion_dialog/issue_suggestion_dialog.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/buyer_search_response/store_list_response.dart';
import 'package:swadesic/model/notification_response/notification_response.dart';
import 'package:swadesic/model/store_info/store_info.dart';
import 'package:swadesic/services/notification_services/notification_service.dart';
import 'package:swadesic/services/store_and_user_reference_services/store_and_user_reference_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum NotificationState { Loading, Success, Failed, Empty }

class NotificationBloc {
  // region Common Variables
  BuildContext context;
  final TabController notificationTabCtrl;
  late UserOrStoreNotificationDataModel personalNotificationDataModel;
  late AllStoreNotificationDataModel allStoreNotification;

  // endregion

  //region Controller
  final tabRefreshCtrl = StreamController<bool>.broadcast();

  //endregion
  final int? storeId;

  // region | Constructor |
  NotificationBloc(this.context, this.storeId, this.notificationTabCtrl);

  // endregion

  // region Init
  init() {
    //Personal notification data model initialization
    personalNotificationDataModel =
        Provider.of<UserOrStoreNotificationDataModel>(context, listen: false);
    //Personal notification data model initialization
    allStoreNotification =
        Provider.of<AllStoreNotificationDataModel>(context, listen: false);

    notificationTabCtrl.addListener(() {
      tabRefreshCtrl.sink.add(true);
    });
  }

// endregion

  //region On tap mark all as read
  void onTapMarkAllAsRead() {
    //If buyer
    if (AppConstants.appData.isUserView!) {
      notificationTabCtrl.index == 0
          ? personalAllNotificationSeen()
          : businessAllNotificationSeen();
    }
    //If seller
    else {
      notificationTabCtrl.index == 0
          ? businessAllNotificationSeen()
          : personalAllNotificationSeen();
    }
  }

  //endregion

  //Personal
  //region Personal all notification seen
  Future<void> personalAllNotificationSeen() async {
    //region Try
    try {
      //Mark all notification as seen api call
      await NotificationService().updateNotification(body: {
        "notified_user": [AppConstants.appData.userReference],
        "is_all_notifications": true
      });
      //Get updated api
      GetNotificationResponse getNotificationResponse =
          await NotificationService()
              .getUserOrStoreNotification(limit: 1000, offset: 0);
      //Add notification data to data model
      personalNotificationDataModel.addAllStoreNotification(
          notificationData: getNotificationResponse);
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message!, context)
          : null;
    } catch (error) {
      return context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
    }
  }

//endregion

  // Business
  //region Business all notification seen
  Future<void> businessAllNotificationSeen() async {
    //region Try
    try {
      //Store list
      List<StoreInfo> storeList = [];

      if (AppConstants.appData.isUserView!) {
        storeList.addAll(BuyerHomeBloc.storeListResponse.storeList!);
      } else {
        storeList
            .addAll(StoreBottomNavigationBloc.storeListResponse.storeList!);
      }

      //If store list is empty then return
      if (storeList.isEmpty) {
        return;
      }

      //Mark all notification as seen api call
      await NotificationService().updateNotification(body: {
        "notified_user": storeList.map((e) => e.storeReference).toList(),
        "is_all_notifications": true
      });
      //Get updated api
      GetNotificationResponse getNotificationResponse =
          await NotificationService().getAllStoreNotification();
      //Add notification data to data model
      allStoreNotification.addAllStoreNotification(
          notificationData: getNotificationResponse);
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message!, context)
          : null;
    } catch (error) {
      return context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
    }
  }

//endregion

  //region Dispose
  void dispose() {
    tabRefreshCtrl.close();
  }

  //endregion

  void personal() {
    //print("Personal");
  }

  void business() {
    //print("Business");
  }
}
