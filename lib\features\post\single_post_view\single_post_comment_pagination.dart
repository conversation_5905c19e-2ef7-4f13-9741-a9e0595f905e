import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_bloc.dart';
import 'package:swadesic/features/buyers/buyer_search/search_post_store_product_and_people/search_post_store_product_and_people_bloc.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/post/feed/feed_bloc.dart';
import 'package:swadesic/features/post/post_screen_bloc.dart';
import 'package:swadesic/features/post/recommened_posts/recommended_post_bloc.dart';
import 'package:swadesic/features/post/single_post_view/single_post_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/buyer_search_response/search_response.dart';
import 'package:swadesic/model/buyer_search_response/store_list_response.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/store_info/store_info.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/buyer_search_services/buyer_search_services.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

// enum SearchPostStoreProductAndPeoplePaginationState { Loading, Done, Empty }

class SinglePostCommentPagination {
  //region Context
  late BuildContext context;
  late SinglePostViewBloc singlePostViewBloc;
  // bool isLoadingPaginationData = false;
  CommentPaginationState currentPaginationState =
      CommentPaginationState.Loading;

  //endregion

//region Constructor
  SinglePostCommentPagination(this.context, this.singlePostViewBloc) {
    //Scroll listener
    singlePostViewBloc.scrollController.addListener(() {
      //If pagination ended and value notifier is true then return
      if (singlePostViewBloc.isPaginationEnded ||
          singlePostViewBloc.commentPaginationStateCtrl.value ==
              CommentPaginationState.End) {
        return;
      }
      scrollListener();
    });
  }

//endregion

  //region Scroll listener
  void scrollListener() async {
    if (singlePostViewBloc.scrollController.position.pixels >=
        singlePostViewBloc.scrollController.position.maxScrollExtent) {
      //Increase offset
      singlePostViewBloc.offset =
          singlePostViewBloc.limit + singlePostViewBloc.offset;
      // Fetch more feed posts when list is scrolled to the bottom
      await getCommentsPagination();
    }
  }

  //endregion

  //region Get comment pagination
  Future<void> getCommentsPagination() async {
    try {
      // Get reference to the PostDataModel
      var postDataModel = Provider.of<PostDataModel>(context, listen: false);
      //Pagination loading
      singlePostViewBloc.commentPaginationStateCtrl.value =
          CommentPaginationState.Loading;

      //Api call
      List<PostDetail> commentList = await PostService().getCommentList(
          offset: singlePostViewBloc.offset,
          limit: singlePostViewBloc.limit,
          parentReference: singlePostViewBloc.postProductCommentReference);
      //Remove deleted comments
      commentList.removeWhere((element) => element.isDeleted!);
      //If Current post reference is not in the list then return Empty
      if (commentList.isEmpty) {
        //Pagination ended
        singlePostViewBloc.isPaginationEnded = true;
        //Pagination Empty
        singlePostViewBloc.commentPaginationStateCtrl.value =
            CommentPaginationState.End;
      } else {
        //Add data to the comment list
        singlePostViewBloc.commentList.addAll(commentList);
        //Add data to data model
        postDataModel.addPostIntoList(postList: commentList);
      }
      //Pagination Stop loading
      singlePostViewBloc.commentPaginationStateCtrl.value =
          CommentPaginationState.Success;
      //Success
      singlePostViewBloc.commentStateCtrl.sink.add(CommentsState.Success);
    } on ApiErrorResponseMessage catch (error) {
      // isLoadingPaginationData = false;
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      //Pagination End
      singlePostViewBloc.commentPaginationStateCtrl.value =
          CommentPaginationState.End;
    } catch (error) {
      // isLoadingPaginationData = false;
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
      //Pagination End
      singlePostViewBloc.commentPaginationStateCtrl.value =
          CommentPaginationState.End;
    }
  }
//endregion
}
