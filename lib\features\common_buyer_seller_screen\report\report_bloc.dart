import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/support/create_support_response.dart';
import 'package:swadesic/services/add_feedback_responses/add_feedback_services.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class ReportBloc {
  //region Common variable
  late BuildContext context;
  final String reference;
  final bool isStore;
  final bool isProduct;
  final bool isCommentOrPost;
  final bool isUser;

  ///Report reason
  List<String> reportReasons = [];
  String selectedReason = "";
  bool isReasonFieldVisible = false;

  ///Service and model
  late AddSupportServices addSupportServices;
  late CreateSupportResponse createSupportResponse;
  //endregion

//region Text Editing Controller
  final TextEditingController reasonTextCtrl = TextEditingController();
  final TextEditingController detailTextCtrl = TextEditingController();
//endregion

//region Controller
  final reasonSelectCtrl = StreamController<bool>.broadcast();

//endregion
  //region Constructor
  ReportBloc(this.context, this.reference, this.isStore, this.isProduct,
      this.isCommentOrPost, this.isUser);
  //endregion
//region Init
  void init() {
    addSupportServices = AddSupportServices();
    addReason();
  }
//endregion

  //region Add reason
  void addReason() {
    //If from store
    if (isStore) {
      reportReasons.addAll(AppStrings.storeReportReason);
    }
    //If from product
    if (isProduct) {
      reportReasons.addAll(AppStrings.productReportReason);
    }
    //If from comment
    if (isCommentOrPost) {
      reportReasons.addAll(AppStrings.commentReportReason);
    }
    //If from user
    if (isUser) {
      reportReasons.addAll(AppStrings.userReportReason);
    }
  }
  //endregion

  //region On select reason
  void onSelectReason({required String reason}) {
    //Add data to reason text field
    reasonTextCtrl.text = reason;
    //Add data to selected reason
    selectedReason = reason;
    //Hide reason field
    isReasonFieldVisible = false;
    //Refresh reason
    reasonSelectCtrl.sink.add(true);
  }
  //endregion

  //region On tap other
  onTapOther() {
    //Clear reason field
    isReasonFieldVisible ? null : reasonTextCtrl.clear();
    //Add data to selected reason
    selectedReason = reasonTextCtrl.text;
    //Visible reason field
    isReasonFieldVisible = true;
    //Refresh reason
    reasonSelectCtrl.sink.add(true);
  }
  //endregion

  //region Add feedback
  addReport() async {
    //Detail field check
    if (detailTextCtrl.text.isEmpty) {
      return CommonMethods.toastMessage("Detail field can't be empty", context);
    }
    //Reason check
    if (reasonTextCtrl.text.isEmpty) {
      return CommonMethods.toastMessage(
          "Please select a reason to report", context);
    }
    //region Try
    try {
      //print("worked");
      //Api call
      createSupportResponse = await addSupportServices.addSupport(
          screenCategory:
              "${isStore ? "Store" : ""}${isProduct ? "Product" : ""}${isCommentOrPost ? "Comment" : ""}${isUser ? "User" : ""}",
          title: "$reference \n$selectedReason",
          detail: detailTextCtrl.text,
          feedbackType: "USER_REPORT",
          context: context);

      //Your feedback received
      CommonMethods.toastMessage(AppStrings.thankYouForYourAction, context);
      //Close screen
      Navigator.pop(context);
    }

    //endregion
    on ApiErrorResponseMessage {
      // giveFeedbackCtrlbackCtrlbackCtrlkCtrl.sink.add(GivefeedbackState.Faileded);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    } catch (error) {
      // giveFeedbackCtrlbackCtrlbackCtrl.sink.add(GivefeedbackState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }
//endregion

//region Dispose
  void dispose() {
    reasonSelectCtrl.close();
  }
//endregion
}
