import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_search/buyer_search_bloc.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/model/buyer_search_response/buyer_search_history_response.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/single_store_info_response/single_store_info_response.dart';
import 'package:swadesic/services/buyer_search_services/buyer_search_services.dart';
import 'package:swadesic/services/store_info_service/store_info_service.dart';

enum BuyerSearchHistoryState { Loading, Success, Failed, Empty }

class BuyerSearchHistoryBloc {
  // region Common Variables
  BuildContext context;
  late BuyerSearchServices buyerSearchServices;
  late BuyerSearchHistoryResponse buyerSearchHistoryResponse =
      BuyerSearchHistoryResponse();

  ///Single store info
  late SingleStoreInfoResponse singleStoreInfoResponse;
  late SingleStoreInfoServices singleStoreInfoServices;
  final TabController tabController;
  // String selectedData = "All";

  // endregion

  //region Controller
  final buyerSearchHistoryCtrl =
      StreamController<BuyerSearchHistoryState>.broadcast();
  final tabViewRefreshCtrl = StreamController<bool>.broadcast();
  // region | Constructor |
  BuyerSearchHistoryBloc(this.context, this.tabController);

  // endregion

  // region Init
  init() async {
    buyerSearchServices = BuyerSearchServices();
    singleStoreInfoServices = SingleStoreInfoServices();
    getHistory();
    tabController.addListener(onChangeTabController);
  }

// endregion

  //region Tab controller
  void onChangeTabController() {
    int currentIndex = tabController.index;
    tabViewRefreshCtrl.sink.add(true);
    BuyerSearchBloc.searchScreenSelectedTab = tabController.index;
    //print("Selected tab index: $currentIndex");
    // Perform actions based on the selected tab index
    // For example, you can update the UI, fetch data, etc.
  }

  //endregion

  //region On select option
  void onSelectOptions({required int index}) {
    tabController.index = index;
    BuyerSearchBloc.searchScreenSelectedTab = index;
    tabViewRefreshCtrl.sink.add(true);
  }
  //endregion

  //region Get History
  getHistory() async {
    try {
      // buyerSearchHistoryCtrl.sink.add(BuyerSearchHistoryState.Loading);
      buyerSearchHistoryResponse =
          await buyerSearchServices.getSearchedHistory();

      if (buyerSearchHistoryResponse.recentSearches!.isEmpty &&
          buyerSearchHistoryResponse.store!.isEmpty &&
          buyerSearchHistoryResponse.product!.isEmpty &&
          buyerSearchHistoryResponse.user!.isEmpty) {
        return buyerSearchHistoryCtrl.sink.add(BuyerSearchHistoryState.Empty);
      }
      //Short recently searched data
      shortSearchedKeywords();
      buyerSearchHistoryCtrl.sink.add(BuyerSearchHistoryState.Success);
    } on ApiErrorResponseMessage {
      buyerSearchHistoryCtrl.sink.add(BuyerSearchHistoryState.Failed);
      // CommonMethods.toastMessage(AppStrings.error, context);
      return;
    } catch (error) {
      buyerSearchHistoryCtrl.sink.add(BuyerSearchHistoryState.Failed);
      // CommonMethods.toastMessage(AppStrings.error, context);
      return;
    }
  }
  //endregion

  //region Short recent searched
  void shortSearchedKeywords() {
    // Sort the list of MyObject objects based on the dateTimeVariable
    buyerSearchHistoryResponse.recentSearches!
        .sort((b, a) => a.date!.compareTo(b.date!));
  }
  //endregion

  //region Remove single history
  removeSingleHistory({required History history}) async {
    try {
      await buyerSearchServices.removeSingleHistory(
          itemId: history.searchedItemId!, historyId: history.searchHistoryId!);
      //Get history
      getHistory();
    } on ApiErrorResponseMessage {
      // buyerSearchHistoryCtrl.sink.add(BuyerSearchHistoryState.Failed);
      // CommonMethods.snackBar(AppStrings.error, context);
      return;
    } catch (error) {
      // buyerSearchHistoryCtrl.sink.add(BuyerSearchHistoryState.Failed);
      // CommonMethods.snackBar(AppStrings.error, context);
      return;
    }
  }
  //endregion

  //region Remove All History
  removeAllHistory() async {
    try {
      buyerSearchHistoryResponse.product!.clear();
      buyerSearchHistoryResponse.store!.clear();
      buyerSearchHistoryResponse.recentSearches!.clear();
      buyerSearchHistoryResponse.user!.clear();

      buyerSearchHistoryCtrl.sink.add(BuyerSearchHistoryState.Empty);

      await buyerSearchServices.removeAllHistory();
      //
      // getHistory();
    } on ApiErrorResponseMessage {
      // buyerSearchHistoryCtrl.sink.add(BuyerSearchHistoryState.Failed);
      // CommonMethods.snackBar(AppStrings.error, context);
      return;
    } catch (error) {
      // buyerSearchHistoryCtrl.sink.add(BuyerSearchHistoryState.Failed);
      // CommonMethods.snackBar(AppStrings.error, context);
      return;
    }
  }
  //endregion

  ///Not in use
  //region Get Store Info Api call
  // getSingleStoreInfo({required String storeReference}) async {
  //   try {
  //     singleStoreInfoResponse = await singleStoreInfoServices.getSingleStoreInfo(storeReference);
  //     //Go to single store screen
  //
  //   }  on ApiErrorResponseMessage catch (error) {
  //     CommonMethods.toastMessage(AppStrings.commonErrorMessage, context, commonErrorMessage: true);
  //     return;
  //     var snackBar = SnackBar(content: Text(error.message.toString()));
  //     // ScaffoldMessenger.of(context).showSnackBar(snackBar);
  //     return;
  //   } catch (error) {
  //     var snackBar = SnackBar(content: Text(error.toString()));
  //     // ScaffoldMessenger.of(context).showSnackBar(snackBar);
  //     return;
  //   }
  // }
//endregion

  ///Go to screen
  ///
  ///
  //region Go to single store screen
  void goToSingleStoreScreen({required String storeReference}) {
    var screen = BuyerViewStoreScreen(
      storeReference: storeReference,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

  // region Go to Single product screen
  goToSingleProductScreen({required String productReference}) {
    var screen =
        BuyerViewSingleProductScreen(productReference: productReference);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

  //region Go to Profile screen
  goToUserProfileScreen({required String userReference}) {
    var screen = UserProfileScreen(
      userReference: userReference,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }

//endregion

//region Dispose
  void dispose() {
    imageCache.clear();
    tabController.dispose();
  }
//endregion
}
