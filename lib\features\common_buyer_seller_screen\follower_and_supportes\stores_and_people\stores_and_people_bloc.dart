import 'dart:async';

import 'package:contacts_service/contacts_service.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/bottom_navigations/store_bottom_navigation/store_bottom_navigation_bloc.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_screen.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/recommended_store_and_user/recommended_store_and_user_pagination.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_comment/buyer_product_comment_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/follower_and_supportes/stores_and_people/store_and_people_pagination.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/all_store_notification.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/user_or_store_notification.dart';
import 'package:swadesic/features/data_model/recommended_store_and_user_data_model/recommended_sore_and_user_data_model.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_sub_order/seller_sub_order_screen.dart';
import 'package:swadesic/features/support/issue_suggestion_dialog/issue_suggestion_dialog.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/recommended_store_and_user/recommended_store_and_user.dart';
import 'package:swadesic/services/find_your_customers_services/find_your_customers_services.dart';
import 'package:swadesic/services/follow_support_services/follow_support_service.dart';
import 'package:swadesic/services/followers_and_supporters_service/followers_and_supporters_service.dart';
import 'package:swadesic/services/notification_services/notification_service.dart';
import 'package:swadesic/services/recommended_store_and_user_service/recommended_store_and_user_service.dart';
import 'package:swadesic/services/store_and_user_reference_services/store_and_user_reference_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum StoreAndPeopleState { Loading, Success, Failed, Empty }

enum StoreAndPeoplePaginationState { Loading, Success, End }

class StoreAndPeopleBloc {
  // region Common Variables
  BuildContext context;
  final String reference;
  final EntityType entityType;
  final FollowEnum requiredList;
  late StoreAndPeoplePagination storeAndPeoplePagination;
  List<UserAndStoreInfo> storesAndPeople = [];
  bool isPaginationEnded = false;
  int limit = 30;
  int offset = 0;
  List<Contact> allContactFromMobile = [];

  // endregion

  //region Controller
  final storeAndPeopleCtrl = StreamController<StoreAndPeopleState>.broadcast();
  ValueNotifier<StoreAndPeoplePaginationState>
      paginationIsLoadingValueNotifier =
      ValueNotifier(StoreAndPeoplePaginationState.Success);

  late ScrollController scrollController = ScrollController();

  //endregion

  // region | Constructor |
  StoreAndPeopleBloc(
      this.context, this.reference, this.entityType, this.requiredList);

  // endregion

// region Init
  init() async {
    if (!kIsWeb) {
      PermissionStatus status = await Permission.contacts.status;
      // If permission is granted
      if (status == PermissionStatus.granted) {
        getContact();
      }
    }
    storeAndPeoplePagination = StoreAndPeoplePagination(context, this);
    await getStoresAndPeople();
  }
// endregion

  //region Get stores and user
  Future<void> getStoresAndPeople() async {
    //region Try
    try {
      offset = 0;
      //Pagination ended
      isPaginationEnded = false;
      //Api call
      // List<RecommendedStoreAndUser> data = await RecommendedStoreAndUserServices().getRecommendedStoreAndUser(isRecommendedStore: true, limit: limit, offset: offset);
      List<UserAndStoreInfo> data = await FollowersAndSupportersService()
          .followersAndSupporters(
              reference: reference,
              entityType: entityType,
              followEnum: requiredList,
              limit: limit,
              offset: offset);

      //Empty
      if (data.isEmpty) {
        return storeAndPeopleCtrl.sink.add(StoreAndPeopleState.Empty);
      }
      //Clear store and people
      storesAndPeople.clear();
      //Add all data in storesAndPeople
      storesAndPeople.addAll(data);
      //Assign the name to the un-register contact
      assignTheNameToUnRegisterContact();

      //Success
      storeAndPeopleCtrl.sink.add(StoreAndPeopleState.Success);
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message!, context)
          : null;
    } catch (error) {
      //Failed
      storeAndPeopleCtrl.sink.add(StoreAndPeopleState.Failed);
      // return context.mounted ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context, commonErrorMessage: true) : null;
    }
  }

//endregion

  ///1
  //region Get contact
  Future<void> getContact() async {
    //Number list
    List<String> contactNumberList = [];
    //Valid number list
    List<String> validContactsList = [];
    // Fetch all contacts from mobile
    allContactFromMobile = await ContactsService.getContacts(
      photoHighResolution: false,
      withThumbnails: false,
    );
    // //("Contact is fetched");

    //Run a loop and get all contacts
    for (var contact in allContactFromMobile) {
      //If contact is not null
      if (contact.phones != null) {
        //Add all contact number
        contactNumberList.addAll(contact.phones!.map(
            (e) => CommonMethods().fixMobileNumber(mobileNumber: e.value!)));
      } else {
        continue;
      }
    }

    //Filter valid number
    for (var number in contactNumberList) {
      //If then number is same as logged in user then ignore
      if (number == AppConstants.appData.mobileNumber) {
        continue;
      }

      //If valid then add to the valid list
      if (CommonMethods().isValidNumber(mobileNumber: number)) {
        validContactsList.add(number);
      } else {
        continue;
      }
    }

    //Asign the name
    assignTheNameToUnRegisterContact();
  }
  //endregion

  ///2
  //region Assign the name to the un-register contact
  Future<void> assignTheNameToUnRegisterContact() async {
    //Contact loop
    for (var phone in allContactFromMobile) {
      //If phone is null then continue
      if (phone.phones == null) {
        continue;
      }
      //Value
      for (var value in phone.phones!) {
        //(value.value);
        //Run loop for searchResult
        for (var data in storesAndPeople) {
          //If data number and value is same then replace then name with the phone name
          if (data.phoneNumber ==
              CommonMethods().fixMobileNumber(mobileNumber: value.value!)) {
            data.handle = phone.displayName;
          }
          // else{
          //   data.name = AppStrings.deletedContact;
          // }
        }
      }
    }
  }

  //endregion

  //region Dispose
  void dispose() {
    storeAndPeopleCtrl.close();
  }

//endregion
}
