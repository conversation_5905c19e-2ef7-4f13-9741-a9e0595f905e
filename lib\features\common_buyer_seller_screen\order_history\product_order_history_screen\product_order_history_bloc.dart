import 'dart:async';

import 'package:flutter/material.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/model/sub_order_history_response/sub_order_history_response.dart';
import 'package:swadesic/services/sub_order_history_service/sub_order_history_service.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum ProductOrderHistoryState { Loading, Success, Failed, Empty }

class ProductOrderHistoryBloc {
  // region Common Variables
  BuildContext context;
  late SubOrderHistoryService subOrderHistoryService;
  late SubOrderHistoryResponse subOrderHistoryResponse;
  final SubOrder subOrder;

  // endregion

  //region Controller
  final productOrderHistoryCtrl =
      StreamController<ProductOrderHistoryState>.broadcast();

  //endregion

  //region Text Controller
  //endregion

  // region | Constructor |
  ProductOrderHistoryBloc(this.context, this.subOrder);
  // endregion

  // region Init
  void init() {
    subOrderHistoryService = SubOrderHistoryService();
    getHistory();
  }
// endregion

  //region Get history
  getHistory() async {
    //region Try
    try {
      //shippingHistoryCtrl.sink.add(ShippingHistoryState.Loading);
      subOrderHistoryResponse = await subOrderHistoryService.getSubOrderHistory(
          subOrderNumber: subOrder.suborderNumber!);
      //Add date time to AM and PM
      for (var data in subOrderHistoryResponse.subOrderHistoryList!) {
        List<String> dateInfo = CommonMethods.dateTimeAmPm(date: data.date!);
        data.date = "${dateInfo[1]} ${dateInfo[2]}";
        //print(data.date);
      }
      productOrderHistoryCtrl.sink.add(ProductOrderHistoryState.Success);
    }
    //endregion
    on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    } catch (error) {
      //print(error);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }
//endregion

  // region Dispose
  void dispose() {}
// endregion
}
