import 'dart:async';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/support/create_support_response.dart';
import 'package:swadesic/services/add_feedback_responses/add_feedback_services.dart';
import 'package:swadesic/services/store_dashboard_services/store_dashboard_service.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum ExitSurveyState { Loading, Success, Failed }

class ExitSurveyBloc {
  //region Common variable
  late BuildContext context;
  final String reference;

  List<String> otherFileTypeList = ["doc", "zip", "pdf", "mp4", "docx"];
  List<String> categoryList = <String>[
    "Account deletion",
    "Orders/returns",
    "Onboarding",
    "Invites",
    "Products",
    "Seller - onboarding",
    "Search"
  ];

  String selectedCategory = "Account deletion";
  late List<PlatformFile?> selectedFiles = [];
  late List<File> files = [];
  late String fileName;

  ///Upload File
  late var uploadFileService = UploadFileService();

  ///Service and model
  late AddSupportServices addSupportServices;
  late CreateSupportResponse createSupportResponse;

  ///Store dash board service and model
  late StoreDashboardService storeDashboardService;
  //endregion

//region Text Editing Controller
  final descriptionTextCtrl = TextEditingController();
//endregion

//region Controller
  final exitSurveyStateCtrl = StreamController<ExitSurveyState>.broadcast();

//endregion
  //region Constructor
  ExitSurveyBloc(this.context, this.reference);
  //endregion
//region Init
  void init() {
    storeDashboardService = StoreDashboardService();
    addSupportServices = AddSupportServices();
  }
//endregion

//region On Tap Add File
  void onTapAddFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
        allowCompression: true,
        type: FileType.custom,
        allowMultiple: true,
        allowedExtensions: ['pdf', 'docx', "doc", 'png', 'jpeg', "zip", "mp4"]);
    if (result != null) {
      ///Add all selected file to
      selectedFiles = result.files;

      ///Add all file
      for (var data in selectedFiles) {
        files.add(File(data!.path!));
      }
      //Clear selected file
      selectedFiles.clear();
      //Refresh
      exitSurveyStateCtrl.sink.add(ExitSurveyState.Success);
    } else {
      // User canceled the picker
    }
  }
//endregion

  //region On tap remove image
  onTapRemoveFile({required File data}) {
    files.remove(data);

    //Refresh
    exitSurveyStateCtrl.sink.add(ExitSurveyState.Success);
  }
//endregion

  //region Add feedback
  addFeedback() async {
    if (descriptionTextCtrl.text.isEmpty) {
      return CommonMethods.toastMessage(
          AppStrings.descriptionCanNotBeEmpty, context);
    }
    //region Try
    try {
      //Loading
      exitSurveyStateCtrl.sink.add(ExitSurveyState.Loading);

      createSupportResponse = await addSupportServices.addSupport(
          screenCategory: selectedCategory,
          title: reference,
          detail: descriptionTextCtrl.text,
          feedbackType: "REPORT",
          context: context);

      ///Upload files
      if (files.isNotEmpty) {
        uploadFile();
      }
      //Clear files and fields
      files.clear();
      descriptionTextCtrl.clear();
      //Success
      exitSurveyStateCtrl.sink.add(ExitSurveyState.Success);
      //If seller deleting store
      if (AppConstants.appData.isStoreView!) {
        //Delete store
        return deleteStore();
      } else {
        return CommonMethods.toastMessage(
            AppStrings.yourUserProfileWillDeleteSoon, context,
            toastShowTimer: 5);
      }
    }
    //endregion
    on ApiErrorResponseMessage {
      exitSurveyStateCtrl.sink.add(ExitSurveyState.Failed);

      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    } catch (error) {
      exitSurveyStateCtrl.sink.add(ExitSurveyState.Failed);

      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }
//endregion

//region Upload File
  void uploadFile() async {
    try {
      for (File data in files) {
        //File name
        fileName = data.path.split('/').last;
        //Is file is image or other
        String extension = CommonMethods.returnExtension(file: data);
        //Is only image
        bool isOnlyImage;
        //If extension contain doc,zip and pdf then make isOnlyImage to false
        if (otherFileTypeList.contains(extension)) {
          isOnlyImage = false;
        } else {
          isOnlyImage = true;
        }

        ///Api call
        await uploadFileService.addSupportDocuments(
          fileNameWithExtension: fileName.toString(),
          filePath: data.path,
          feedbackId: createSupportResponse.data!.feedbackId!,
          isOnlyImage: isOnlyImage,
        );
      }
    } on ApiErrorResponseMessage {
      exitSurveyStateCtrl.sink.add(ExitSurveyState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    } catch (error) {
      exitSurveyStateCtrl.sink.add(ExitSurveyState.Failed);

      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }
//endregion

  //region Delete store
  deleteStore() async {
    //region Try
    try {
      //Delete store api call
      await storeDashboardService.deleteStore(storeReference: reference);
      //Store deletes message
      CommonMethods.toastMessage(AppStrings.yourStoreIsDeleted,
          AppConstants.userStoreCommonBottomNavigationContext,
          toastShowTimer: 5);
      //Switch to buyer
      CommonMethods.switchToBuyer(context: context);
    }
    //endregion
    on ApiErrorResponseMessage {
      exitSurveyStateCtrl.sink.add(ExitSurveyState.Failed);

      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    } catch (error) {
      exitSurveyStateCtrl.sink.add(ExitSurveyState.Failed);

      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }
//endregion
}
