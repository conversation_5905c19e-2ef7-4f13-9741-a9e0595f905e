import 'dart:async';
import 'package:flutter/material.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/label_data/label_data.dart';
import 'package:swadesic/model/single_store_info_response/single_store_info_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/seller_trust_center_service/seller_trust_center_service.dart';
import 'package:swadesic/services/store_info_service/store_info_service.dart';
import 'package:swadesic/util/app_strings.dart';

import 'package:swadesic/util/common_methods.dart';

enum LabelsState { Loading, Success, Failed, Empty }

class LabelsBloc {
  // region Common Methods
  BuildContext context;
  //Default data list and selected
  List<Label> ownedList = [];
  late Label selectedOwned;
  List<Label> brandList = [];
  late Label selectedBrand;
  List<Label> madeList = [];
  late Label selectedMade;

  //Store reference,trust center id and is from trust center
  final String storeReference;
  final bool isFromTrustCenter;
  final int? trustCenterId;

  //Made,Brand and Owned
  final String? owned;
  final String? brand;
  final String? made;

  //Product
  final Product? product;

  ///Store service and model
  late SingleStoreInfoServices singleStoreInfoServices;
  late SingleStoreInfoResponse singleStoreInfoResponse =
      SingleStoreInfoResponse();

  ///Trust center service
  late SellerTrustCenterService sellerTrustCenterService =
      SellerTrustCenterService();

  //endregion
  //region Controller
  final labelSliderCtrl = StreamController<bool>.broadcast();
  final labelCtrl = StreamController<LabelsState>.broadcast();

  //endregion

  // region | Constructor |
  LabelsBloc(this.context, this.storeReference, this.isFromTrustCenter,
      this.owned, this.brand, this.made, this.trustCenterId, this.product);
  // endregion

  // region Init
  init() async {
    //Add default data
    addDefaultData();
  }
  // endregion

  //region On tap save
  void onTapSave() {
    //If from trust center
    if (isFromTrustCenter) {
      updateOwned();
    } else {
      updateAllLabel();
    }
  }
  //endregion

  //region Get Store Info Api call
  getSingleStoreInfo() async {
    try {
      //Loading
      labelCtrl.sink.add(LabelsState.Loading);
      singleStoreInfoResponse =
          await singleStoreInfoServices.getSingleStoreInfo(storeReference);

      // selectedOwned = ownedList.firstWhere((element) => element.labelStatus == singleStoreInfoResponse.data.)
      //Success
      labelCtrl.sink.add(LabelsState.Success);
    } on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);

      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }
//endregion

  //region Update swadeshi owned
  void updateOwned() async {
    try {
      //Update swadeshi owned
      await sellerTrustCenterService.updateSwadeshiOwned(
          label: selectedOwned.labelStatus, storeReference: storeReference);
      //If context is mounted then close
      if (context.mounted) {
        Navigator.pop(context, selectedOwned.labelStatus);
      }
    } on ApiErrorResponseMessage {
      //Failed
      labelCtrl.sink.add(LabelsState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    } catch (error) {
      //Failed
      labelCtrl.sink.add(LabelsState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }
  //endregion

  //region Update all label
  void updateAllLabel() {
    product!.swadeshiBrand = selectedBrand.labelStatus;
    product!.swadeshiMade = selectedMade.labelStatus;
    product!.swadeshiOwned = selectedOwned.labelStatus;
    //Close the screen and send data to back screen
    if (context.mounted) {
      Navigator.pop(context, product);
    }
  }
  //endregion

  //region Add data to the list
  void addDefaultData() {
    //Owned data
    ownedList.add(Label(
        "Swadeshi Owned (Swadeshi label of your store)",
        "Videshi Owned",
        0.0,
        "Businesses that are entirely foreign-owned",
        "NOT_SWADESHI_OWNED"));
    ownedList.add(Label(
        "Swadeshi Owned (Swadeshi label of your store)",
        "Partially Swadeshi Owned",
        1.0,
        "Businesses with a minority Indian ownership (below 50% equity) and majority foreign ownership",
        "PARTIALLY_SWADESHI_OWNED"));
    ownedList.add(Label(
        "Swadeshi Owned (Swadeshi label of your store)",
        "Mainly Swadeshi Owned",
        2.0,
        "Businesses with a majority Indian ownership (above 50% equity) but may have some foreign investment or involvement",
        "MAINLY_SWADESHI_OWNED"));
    ownedList.add(Label(
        "Swadeshi Owned (Swadeshi label of your store)",
        "Fully Swadeshi Owned",
        3.0,
        "Businesses that are fully Indian-owned and operated",
        "FULLY_SWADESHI_OWNED"));
    //Add selected data or default one if null
    if (owned == null) {
      selectedOwned = ownedList.first;
    } else {
      selectedOwned =
          ownedList.firstWhere((element) => element.labelStatus == owned);
    }
    //Brand data
    brandList.add(Label(
        "Swadeshi Brand (Swadeshi label of the brand)",
        "Videshi Brand",
        0.0,
        "Brands that are entirely foreign-owned",
        "NOT_SWADESHI_BRAND"));
    brandList.add(Label(
        "Swadeshi Brand (Swadeshi label of the brand)",
        "Partially Swadeshi Brand",
        1.0,
        "Brands with a minority Indian ownership (below 50% equity) and majority foreign ownership",
        "PARTIALLY_SWADESHI_BRAND"));
    brandList.add(Label(
        "Swadeshi Brand (Swadeshi label of the brand)",
        "Mainly Swadeshi Brand",
        2.0,
        "Brands with a majority Indian ownership (above 50% equity) but may have some foreign investment or involvement",
        "MAINLY_SWADESHI_BRAND"));
    brandList.add(Label(
        "Swadeshi Brand (Swadeshi label of the brand)",
        "Fully Swadeshi Brand",
        3.0,
        "Brands that are fully Indian-owned and operated",
        "FULLY_SWADESHI_BRAND"));
    //Add selected data or default one if null
    if (brand == null) {
      selectedBrand = brandList.first;
    } else {
      selectedBrand =
          brandList.firstWhere((element) => element.labelStatus == brand);
    }
    //Made data
    madeList.add(Label(
        "Swadeshi Made (Swadeshi label of the product)",
        "Videshi Made",
        0.0,
        "Products entirely made outside India, using foreign materials and labor.",
        "NOT_SWADESHI_MADE"));
    madeList.add(Label(
        "Swadeshi Made (Swadeshi label of the product)",
        "Partially Swadeshi Made",
        1.0,
        "Products assembled in India with local labor with mostly foreign components",
        "PARTIALLY_SWADESHI_MADE"));
    madeList.add(Label(
        "Swadeshi Made (Swadeshi label of the product)",
        "Mainly Swadeshi Made",
        2.0,
        "Products mostly made in India with some foreign materials and labor.",
        "MAINLY_SWADESHI_MADE"));
    madeList.add(Label(
        "Swadeshi Made (Swadeshi label of the product)",
        "Fully Swadeshi Made",
        3.0,
        "Products made entirely in India, using local materials and labor.",
        "FULLY_SWADESHI_MADE"));
    //Add selected data or default one if null
    if (made == null) {
      selectedMade = madeList.first;
    } else {
      selectedMade =
          madeList.firstWhere((element) => element.labelStatus == made);
    }
    //Refresh ui
    labelSliderCtrl.sink.add(true);
  }
  //endregion

  //region On change slider
  void onChangeSlider() {
    labelSliderCtrl.sink.add(true);
  }
  //endregion

//region Dispose
  void dispose() {
    labelSliderCtrl.close();
    labelCtrl.close();
  }
//endregion
}
