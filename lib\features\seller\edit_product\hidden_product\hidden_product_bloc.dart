// import 'dart:async';
//
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:swadesic/model/api_response_message.dart';
// import 'package:swadesic/model/hide_delete_response/delete_response.dart';
// import 'package:swadesic/model/store_product_response/store_hidden_product_response.dart' as hidden_product;
// import 'package:swadesic/services/edit_product_image/edit_product_image.dart';
// import 'package:swadesic/services/seller_hide_delete_service/seller_hide_delete_service.dart';
// import 'package:swadesic/services/store_product_services/store_product_services.dart';
// import 'package:swadesic/util/app_colors.dart';
// import 'package:swadesic/util/app_constants.dart';
// import 'package:swadesic/util/app_images.dart';
// import 'package:swadesic/util/common_methods.dart';
// import 'package:swadesic/util/common_widgets.dart';
//
// enum HiddenProductState { Loading, Success, Failed }
//
// class HiddenProductBloc {
//   // region Common Variables
//   BuildContext context;
//   List drawerOptionsList = [
//     "edit & save",
//     "unhide to publish",
//     "delete products",
//   ];
//   bool drawerVisibility = false;
//   final String storeReference;
//
//   //List AppConstants.selectedProducts = [];
//   ///Delete and Hide
//   late SellerHideDeleteService sellerHideDeleteService;
//   late HideDeleteApiResponse hideDeleteApiResponse;
//
//   ///Edit Product
//   late EditProductAndImageServices editProductAndImageServices;
//
//   /// Get Store Hidden Products
//   late StoreProductServices storeProductServices;
//   late hidden_product.StoreHiddenProductResponse storeHiddenProductResponse;
//   late List<hidden_product.HiddenProduct> hiddenProductList = [];
//
//   // endregion
//
//   //region Controller
//   final editProductCtrl = StreamController<HiddenProductState>.broadcast();
//   final selectCtrl = StreamController<String>.broadcast();
//   final drawerCtrl = StreamController<bool>.broadcast();
//   //endregion
//
//   //region Text Editing Controller
//   TextEditingController searchTextCtrl = TextEditingController();
//   TextEditingController stockText = TextEditingController();
//   //endregion
//
//   // region | Constructor |
//   HiddenProductBloc(this.context, this.storeReference);
//   // endregion
//
//   // region Init
//   void init() {
//     ///Clear Hidden Product List
//     hiddenProductList.clear();
//
//     ///Get Store Hidden product
//     storeProductServices = StoreProductServices();
//
//     ///Delete and Hide
//     sellerHideDeleteService = SellerHideDeleteService();
//
//     ///Edit Stock
//     editProductAndImageServices = EditProductAndImageServices();
//     getHiddenProductApiCall();
//   }
// // endregion
//
//   //region OnChange Search Product
//   void onChangeSearchField(String value) {
//     //print(value);
//     hiddenProductList.clear();
//     for (var data in storeHiddenProductResponse.data!) {
//       if (data.productName!.toLowerCase().contains(value.toLowerCase()) || data.storehandle!.toLowerCase().contains(value.toLowerCase()))
//         hiddenProductList.add(data);
//     }
//     //if product list is empty then stream empty
//     if(hiddenProductList.isEmpty){
//
//       return editProductCtrl.sink.add(HiddenProductState.Success);
//     }
//
//     // // set state
//     // editProductCtrl.sink.add(HiddenProductState.Success);
//   }
//   //endregion
//
//   //region Get Hidden Product List Api Call
//   void getHiddenProductApiCall() async {
//     try {
//       editProductCtrl.sink.add(HiddenProductState.Loading);
//       storeHiddenProductResponse = await storeProductServices.getStoreHiddenProduct(storeReference);
//       hiddenProductList.addAll(storeHiddenProductResponse.data!);
//
//       editProductCtrl.sink.add(HiddenProductState.Success);
//     }  on ApiErrorResponseMessage catch (error) {
//       CommonMethods.toastMessage(AppStrings.commonErrorMessage, context, commonErrorMessage: true);
//       return;
//       editProductCtrl.sink.add(HiddenProductState.Failed);
//       var snackBar = SnackBar(content: Text(error.message.toString()));
//       ScaffoldMessenger.of(context).showSnackBar(snackBar);
//       return;
//     } catch (error) {
//       editProductCtrl.sink.add(HiddenProductState.Failed);
//       var snackBar = SnackBar(content: Text(error.toString()));
//       ScaffoldMessenger.of(context).showSnackBar(snackBar);
//       return;
//     }
//   }
//
// //endregion
//
//   //region On Tap Product
//   void onTapProduct(String productReference) {
//     if (AppConstants.selectedProducts.contains(productReference)) {
//       AppConstants.selectedProducts.remove(productReference);
//       selectCtrl.sink.add(productReference);
//     } else {
//       AppConstants.selectedProducts.add(productReference);
//       selectCtrl.sink.add(productReference);
//     }
//     //print(AppConstants.selectedProducts.toString());
//   }
//   //endregion
//
//   //region On Tap Drawer
//   onTapDrawer() {
//     if (AppConstants.selectedProducts.isEmpty) {
//       CommonMethods.toastMessage("Please select any product to continue", context);
//       return;
//     }
//     drawerVisibility = !drawerVisibility;
//     drawerCtrl.sink.add(drawerVisibility);
//   }
//   //endregion
//
//   //region Drawer Option Select
//   void drawerOptionSelect(int index) {
//     onTapDrawer();
//     if (index == 0) {
//       return;
//     }
//     if (index == 2) {
//       deleteApiCall();
//       return;
//     }
//     if (index == 1) {
//       unHideApiCall();
//       return;
//     }
//   }
//   //endregion
//
//   //region Update Stock Dialog
//   void updateStockDialog() {
//     showDialog(
//         context: context,
//         builder: (BuildContext context) {
//           return AlertDialog(
//             title: Stack(
//               alignment: Alignment.center,
//               children: [
//                 const Text("Update stock"),
//                 Align(
//                     alignment: Alignment.centerRight,
//                     child: SvgPicture.asset(
//                       AppImages.exclamation,
//                       fit: BoxFit.cover,
//                     ))
//               ],
//             ),
//             titleTextStyle: TextStyle(color: AppColors.writingColor2, fontSize: 16, fontFamily: "LatoSemiBold", fontWeight: FontWeight.w600),
//             content: Column(
//               mainAxisSize: MainAxisSize.min,
//               mainAxisAlignment: MainAxisAlignment.center,
//               crossAxisAlignment: CrossAxisAlignment.center,
//               children: [
//                 Padding(
//                   padding: EdgeInsets.symmetric(horizontal: 20),
//                   child: TextFormField(
//                     controller: stockText,
//                     keyboardType: TextInputType.number,
//
//                     maxLines: 1,
//                     // readOnly: true,
//
//                     style: TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: AppColors.appBlack),
//                     decoration: InputDecoration(
//                       // prefixIcon: Padding(
//                       //   padding: EdgeInsets.symmetric(horizontal: 11.73),
//                       //   child: SvgPicture.asset(AppImages.searchBarIcon,fit: BoxFit.contain,color: AppColors.appBlack7,),
//                       // ),
//                       // suffixIcon: CupertinoButton(
//                       //     padding: EdgeInsets.zero,
//                       //     onPressed: (){
//                       //       Navigator.pop(context);
//                       //     },
//                       //     child: Icon(Icons.done,color: AppColors.appBlack5,size: 25,)),
//                       filled: true,
//
//                       //contentPadding: EdgeInsets.symmetric(vertical: 19.53),
//
//                       fillColor: AppColors.lightestGrey,
//
//                       isDense: true,
//
//                       // hintText: "search products, stores & your friends",
//                       // hintStyle: TextStyle(
//                       //     fontSize: 12,
//                       //     fontWeight: FontWeight.w400,
//                       //     color: AppColors.appBlack.withOpacity(0.4)
//                       // ),
//                       focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(10), borderSide: BorderSide.none),
//                       enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(10), borderSide: BorderSide.none),
//                     ),
//                   ),
//                 ),
//                 verticalSizedBox(20),
//                 Row(
//                   children: [
//                     Expanded(
//                       child: CupertinoButton(
//                           padding: EdgeInsets.symmetric(vertical: 10),
//                           borderRadius: BorderRadius.all(Radius.circular(30)),
//                           color: AppColors.inActiveGreen,
//                           child: Text(
//                             "add",
//                             style: TextStyle(fontWeight: FontWeight.w600, fontFamily: "LatoSemiBold", fontSize: 15, color: AppColors.brandGreen),
//                           ),
//                           onPressed: () {
//                             Navigator.pop(context);
//                           }),
//                     ),
//                     horizontalSizedBox(20),
//                     Expanded(
//                       child: CupertinoButton(
//                           padding: EdgeInsets.symmetric(vertical: 10),
//                           borderRadius: BorderRadius.all(Radius.circular(30)),
//                           color: AppColors.inActiveGreen,
//                           child: Text(
//                             "remove",
//                             style: TextStyle(fontWeight: FontWeight.w600, fontFamily: "LatoSemiBold", fontSize: 15, color: AppColors.brandGreen),
//                           ),
//                           onPressed: () {
//                             Navigator.pop(context);
//                           }),
//                     ),
//                   ],
//                 )
//               ],
//             ),
//           );
//         });
//   }
//   //endregion
//
//   //region Delete Api Call
//   void deleteApiCall() async {
//     try {
//       editProductCtrl.sink.add(HiddenProductState.Loading);
//       for (int i = 0; i < AppConstants.selectedProducts.length; i++) {
//         sellerHideDeleteService.deleteProduct(AppConstants.selectedProducts[i]);
//         hiddenProductList.removeWhere((element) => element.productReference == AppConstants.selectedProducts[i]);
//       }
//       editProductCtrl.sink.add(HiddenProductState.Success);
//       CommonMethods.toastMessage("${AppConstants.selectedProducts.length} Product deleted", context);
//       AppConstants.selectedProducts.clear();
//     }  on ApiErrorResponseMessage catch (error) {
//       CommonMethods.toastMessage(AppStrings.commonErrorMessage, context, commonErrorMessage: true);
//       return;
//       editProductCtrl.sink.add(HiddenProductState.Failed);
//       var snackBar = SnackBar(content: Text(error.message.toString()));
//       ScaffoldMessenger.of(context).showSnackBar(snackBar);
//       return;
//     } catch (error) {
//       editProductCtrl.sink.add(HiddenProductState.Failed);
//       var snackBar = SnackBar(content: Text(error.toString()));
//       ScaffoldMessenger.of(context).showSnackBar(snackBar);
//       return;
//     }
//   }
// //endregion
//
//   //region Un Hide Api Call
//   void unHideApiCall() async {
//     try {
//       editProductCtrl.sink.add(HiddenProductState.Loading);
//       for (int i = 0; i < AppConstants.selectedProducts.length; i++) {
//         sellerHideDeleteService.unHideProduct(AppConstants.selectedProducts[i]);
//         hiddenProductList.removeWhere((element) => element.productReference == AppConstants.selectedProducts[i]);
//       }
//       CommonMethods.toastMessage("${AppConstants.selectedProducts.length} Product published", context);
//       AppConstants.selectedProducts.clear();
//       editProductCtrl.sink.add(HiddenProductState.Success);
//     }  on ApiErrorResponseMessage catch (error) {
//       CommonMethods.toastMessage(AppStrings.commonErrorMessage, context, commonErrorMessage: true);
//       return;
//       editProductCtrl.sink.add(HiddenProductState.Failed);
//       var snackBar = SnackBar(content: Text(error.message.toString()));
//       ScaffoldMessenger.of(context).showSnackBar(snackBar);
//       return;
//     } catch (error) {
//       editProductCtrl.sink.add(HiddenProductState.Failed);
//       var snackBar = SnackBar(content: Text(error.toString()));
//       ScaffoldMessenger.of(context).showSnackBar(snackBar);
//       return;
//     }
//   }
// //endregion
//
// }
