import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_view_store/store_products/store_products.dart';
import 'package:swadesic/features/common_buyer_seller_screen/report/report_screen.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/features/seller/seller_accounts/seller_accounts_screen.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/single_store_info_response/single_store_info_response.dart';
import 'package:swadesic/services/app_analytics/app_analytics.dart';
import 'package:swadesic/services/store_follow_services/store_follow_services.dart';
import 'package:swadesic/services/store_info_service/store_info_service.dart';
import 'package:swadesic/services/store_visit_services/store_visit_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

// enum AppState { Loading, Success, Failed,Empty }
enum BuyerViewStoreState {
  Loading,
  Success,
  Failed,
  Empty,
  SearchEmpty,
  Deleted,
  InActive,
  NotOwnedTestStore
}

class BuyerViewStoreBloc {
  // region Common Variables
  BuildContext context;
  final String? storeReference;
  int maxStoreDescLine = 3;
  bool isStoreDetailExpand = false;
  Key widgetKey = UniqueKey();
  GlobalKey<StoreProductsState> storeProductsKey =
      GlobalKey<StoreProductsState>();

  // final store_list_response.StoreInfo selectedStore;
  ///Store service and model
  late SingleStoreInfoServices singleStoreInfoServices;
  late SingleStoreInfoResponse singleStoreInfoResponse =
      SingleStoreInfoResponse();

  ///Visit first time store
  late StoreVisitService storeVisitService = StoreVisitService();

  ///Seller own store info data model
  late SellerOwnStoreInfoDataModel sellerOwnStoreInfoDataModel;
  // endregion

  //region Controller
  final storeDetailCtrl = StreamController<BuyerViewStoreState>.broadcast();
  final tabRefreshCtrl = StreamController<bool>.broadcast();
  final ScrollController scrollController = ScrollController();
  //endregion

  //region Tab controller
  final TabController tabController;
  //endregion

  //region Scroll controller
  final ScrollController nestedScrollController = ScrollController();
  final ScrollController storeInfoScrollController = ScrollController();
  //endregion

  // region | Constructor |
  BuyerViewStoreBloc(this.context, this.storeReference, this.tabController);

  // endregion
//  ///Seller own store info data model
//   sellerOwnStoreInfoDataModel = Provider.of<SellerOwnStoreInfoDataModel>(context, listen: false);
  // region Init
  Future<void> init() async {
    sellerOwnStoreInfoDataModel =
        Provider.of<SellerOwnStoreInfoDataModel>(context, listen: false);
    singleStoreInfoServices = SingleStoreInfoServices();

    ///Get store Info
    getSingleStoreInfo();

    ///Add tab controller listener
    tabController.addListener(handleTabController);
    //Add scroll controller listiner
    storeInfoScrollController.addListener(() {
      scrollListen();
    });
    nestedScrollController.addListener(() {
      scrollListen();
    });
    tabSwitchToLast();
  }
// endregion

  //region If seller view own store then open last tab view
  void tabSwitchToLast() {
    //If admin view then switch to last tab (index 4 is the store details tab)
    if (AppConstants.appData.isStoreView! &&
        storeReference == AppConstants.appData.storeReference!) {
      tabController.animateTo(4);
    }
  }
  //endregion

  void scrollListen() {
    if (storeInfoScrollController.position == 0) {
      // Overscrolling at the top
      //print("Overscroll at the top");
    } else {
      // Overscrolling at the bottom
      //print("Overscroll at the bottom");
    }

    // if (storeInfoScrollController.position.pixels != nestedScrollController.position.pixels) {
    //   // Scroll  storeInfoScrollController to match nestedScrollController
    //   storeInfoScrollController.jumpTo(nestedScrollController.position.pixels);
    // }

    // if ( storeInfoScrollController.position.pixels >=  storeInfoScrollController.position.maxScrollExtent) {
    //   // When  storeInfoScrollController over-scrolls to the bottom, jump to nestedScrollController
    //   nestedScrollController.jumpTo(nestedScrollController.position.pixels + 1);
    // }
    // else if (nestedScrollController.position.pixels <= nestedScrollController.position.minScrollExtent) {
    //   // When nestedScrollController scrolls all the way to the top, switch to  storeInfoScrollController
    //    storeInfoScrollController.jumpTo( storeInfoScrollController.position.pixels + 1);
    // }
    // else if (nestedScrollController.position.pixels >= nestedScrollController.position.maxScrollExtent) {
    //   // When _controller2 over-scrolls to the bottom, jump to _controller1
    //   storeInfoScrollController.jumpTo(storeInfoScrollController.position.pixels + 1);
    // }
  }

  //region Handle tab change
  void handleTabController() async {
    // if (tabController.indexIsChanging) {
    //   //print(tabController.index);
    // } else {
    //
    // }
    //Refresh ui
    tabRefreshCtrl.sink.add(true);
  }
//endregion

  //region Go to Seller Accounts Screen
  void goToSellerAccountScreen() {
    var screen = SellerAccountsScreen(
        userReference: AppConstants.appData.userReference!);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

  //region On Tap leading if it is from store level
  onTapLeading() {
    AppConstants.userPersistentTabController.jumpToTab(0);
    AppConstants.bottomNavigationRefreshCtrl.sink.add(true);
  }
  //endregion

  ///Get store info
  //region Get Store Info Api call
  getSingleStoreInfo() async {
    try {
      final store = singleStoreInfoServices.getSingleStoreInfo(storeReference!);

      final followStatus =
          StoreFollowServices().getFollowStatus(reference: storeReference!);

      // Wait for both futures to complete
      final List<dynamic> results = await Future.wait([store, followStatus]);

      //Store detail
      singleStoreInfoResponse = results[0];
      //Follow status
      singleStoreInfoResponse.data!.followStatus = results[1];

      // storeDetailCtrl.sink.add(AppState.Loading);
      // ///Get store detail
      // singleStoreInfoResponse = await singleStoreInfoServices.getSingleStoreInfo(storeReference!);
      // //Check follow status
      // singleStoreInfoResponse.data!.followStatus = await StoreFollowServices().getFollowStatus(reference: storeInfo.storeReference!);
      //

      // final userDetailFuture = userDetailsServices.getUserDetail(userResponse: userReference);
      // final followStatusFuture = StoreFollowServices().getFollowStatus(reference: userReference);
      //
      // // Wait for both futures to complete
      // final List<dynamic> results = await Future.wait([userDetailFuture, followStatusFuture]);
      //
      // // Extract the results
      // otherUserInfoResponse = results[0];
      // otherUserInfoResponse.followStatus = results[1];

      //Store visited
      StoreVisitService().visitNewStore(
          storeReference: singleStoreInfoResponse.data!.storeReference!);
      //

      ///If not admin and has product then select Product tab else select post tab
      if (singleStoreInfoResponse.data!.storeReference !=
          AppConstants.appData.storeReference) {
        //If has product and has post then select product Tab
        if (singleStoreInfoResponse.data!.hasProducts! &&
            singleStoreInfoResponse.data!.hasPosts!) {
          tabController.animateTo(0);
        }
        //If has product then select product tab
        else if (singleStoreInfoResponse.data!.hasProducts!) {
          tabController.animateTo(0);
        }
        //If has post then select post tab
        else if (singleStoreInfoResponse.data!.hasPosts!) {
          tabController.animateTo(1);
        }
      }

      ///If store view
      if (AppConstants.appData.isStoreView!) {
        return sellerViewCondition();
      }

      ///If user view
      if (AppConstants.appData.isUserView!) {
        return userViewCondition();
      }
    } catch (error) {
      storeDetailCtrl.sink.add(BuyerViewStoreState.Failed);

      // CommonMethods.toastMessage(AppStrings.commonErrorMessage, context, commonErrorMessage: true);
      return;
    }
  }
//endregion

  //region Seller view condition
  void sellerViewCondition() {
    //If seller viewing own store then save store info to data model
    if (storeReference == AppConstants.appData.storeReference) {
      sellerOwnStoreInfoDataModel
          .setStoreInfoResponse(singleStoreInfoResponse.data!);
      storeDetailCtrl.sink.add(BuyerViewStoreState.Success);
      return;
    }
    //If Store is deleted
    if (singleStoreInfoResponse.data!.isDeleted!) {
      storeDetailCtrl.sink.add(BuyerViewStoreState.Deleted);
      return;
    }
    //If store is not activated
    if (!singleStoreInfoResponse.data!.isActive!) {
      return storeDetailCtrl.sink.add(BuyerViewStoreState.Success);
    }
    //1. If store is test store
    //2. Not belongs to the logged in user
    if (singleStoreInfoResponse.data!.storehandle!.startsWith("test") &&
        !CommonMethods().isStoreBelongsToLoggedInUser(
            context: context,
            storeReference: singleStoreInfoResponse.data!.storeReference!)) {
      return storeDetailCtrl.sink.add(BuyerViewStoreState.NotOwnedTestStore);
    }
    storeDetailCtrl.sink.add(BuyerViewStoreState.Success);
  }
  //endregion

  //region User view condition
  void userViewCondition() {
    //If seller viewing own store then save store info to data model
    if (storeReference == AppConstants.appData.storeReference) {
      sellerOwnStoreInfoDataModel
          .setStoreInfoResponse(singleStoreInfoResponse.data!);
      storeDetailCtrl.sink.add(BuyerViewStoreState.Success);
      return;
    }
    //If Store is deleted
    if (singleStoreInfoResponse.data!.isDeleted!) {
      storeDetailCtrl.sink.add(BuyerViewStoreState.Deleted);
      return;
    }
    //If store is not activated
    if (!singleStoreInfoResponse.data!.isActive!) {
      return storeDetailCtrl.sink.add(BuyerViewStoreState.Success);
    }
    //1. If store is test store
    //2. Not belongs to the logged in user
    if (singleStoreInfoResponse.data!.storehandle!.startsWith("test") &&
        !CommonMethods().isStoreBelongsToLoggedInUser(
            context: context,
            storeReference: singleStoreInfoResponse.data!.storeReference!)) {
      return storeDetailCtrl.sink.add(BuyerViewStoreState.NotOwnedTestStore);
    }
    storeDetailCtrl.sink.add(BuyerViewStoreState.Success);
  }
  //endregion

  //region if from bottom navigation switch to home
  goToFirstBottomNavigation() {
    AppConstants.storePersistentTabController.jumpToTab(0);
  }
  //endregion

  //region Go to Seller All Order
  void goToSellerAllOrder() {
    var screen = SellerAllOrdersScreen(
      storeId: AppConstants.appData.storeId!,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

  //region Refresh Store Products
  Future<void> refreshStoreProducts() async {
    if (storeProductsKey.currentState != null) {
      await storeProductsKey.currentState!.refreshProducts();
    }
  }
  //endregion

  //region Go to report screen
  void goToReportScreen() async {
    //If non register user
    if (CommonMethods().isStaticUser()) {
      CommonMethods().goToSignUpFlow();
      return;
    }
    await Future.delayed(Duration.zero);
    var screen = ReportScreen(
      reference: singleStoreInfoResponse.data!.storeReference!,
      isStore: true,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

//region Dispose
  void dispose() {
    imageCache.clear();
    storeDetailCtrl.close();
    tabRefreshCtrl.close();
    storeDetailCtrl.close();
  }
//endregion
}
