import 'package:flutter/material.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/auto_hide_navigation_service.dart';
import 'package:swadesic/features/navigation/widgets/navigation_page_wrapper.dart';
import 'package:swadesic/util/common_methods.dart';

/// Base navigation widget that provides the core navigation structure
/// with IndexedStack, Navigator management, and gesture handling
abstract class BaseNavigation extends StatefulWidget {
  const BaseNavigation({super.key});
}

abstract class BaseNavigationState<T extends BaseNavigation> extends State<T> {
  int _currentIndex = 0;
  late final List<GlobalKey<NavigatorState>> _navigatorKeys;

  // Protected setter for currentIndex to allow subclasses to set initial value
  @protected
  set currentIndex(int value) => _currentIndex = value;

  // Auto-hide navigation service
  final AutoHideNavigationService _autoHideService =
      AutoHideNavigationService();

  // Abstract methods to be implemented by subclasses
  List<Widget> get tabPages;
  Widget buildCustomBottomNav();
  void onProfileTabSelected();

  // Getters
  int get currentIndex => _currentIndex;
  List<GlobalKey<NavigatorState>> get navigatorKeys => _navigatorKeys;

  // Get the number of tabs (including profile tab)
  int get tabCount => tabPages.length;

  @override
  void initState() {
    super.initState();
    _navigatorKeys = List.generate(
      tabCount,
      (_) => GlobalKey<NavigatorState>(),
    );
    _autoHideService.initialize();
  }

  @override
  void dispose() {
    super.dispose();
  }

  /// Handle tab selection with single tap behavior
  void onTabSelected(int index) {
    if (index == _currentIndex) {
      // Single tap on current tab - no action (double tap handled separately)
      return;
    }

    // Update the persistent tab controller
    AppConstants.storePersistentTabController.index = index;

    // Handle profile tab special behavior (last tab is always profile)
    if (index == tabCount - 1) {
      if (CommonMethods().isStaticUser()) {
        AppConstants.isSignInScreenOpenedFromProfileTab = true;
        CommonMethods().goToSignUpFlow();
        return;
      }
      onProfileTabSelected();
    }

    setState(() => _currentIndex = index);
  }

  /// Reset tab to root and refresh the page (double tap behavior)
  void resetTab(int index) {
    _navigatorKeys[index].currentState!.popUntil((r) => r.isFirst);

    // Show feedback to user
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text("Reset ${_getTabName(index)} tab")),
    );
  }

  /// Handle back button behavior
  Future<bool> onWillPop() async {
    final NavigatorState currentNavigator =
        _navigatorKeys[_currentIndex].currentState!;

    // Try to pop within current tab stack
    if (await currentNavigator.maybePop()) {
      return false;
    }

    // If at root and not on Home tab, switch to Home tab
    if (_currentIndex != 0) {
      setState(() => _currentIndex = 0);
      return false;
    }

    // Exit app only if already on Home tab root
    return true;
  }

  /// Handle horizontal swipe on grouped tabs (all except last tab)
  void onHorizontalSwipe(double velocity) {
    if (velocity > 0 && _currentIndex > 0) {
      // Swipe right - go to previous tab
      onTabSelected(_currentIndex - 1);
    } else if (velocity < 0 && _currentIndex < tabCount - 2) {
      // Swipe left - go to next tab (only if not on last tab)
      onTabSelected(_currentIndex + 1);
    }
  }

  /// Show quick menu (long press behavior)
  void showQuickMenu(int index) {
    // showDialog(
    //   context: context,
    //   builder: (_) => AlertDialog(
    //     title: Text("Quick menu - ${_getTabName(index)}"),
    //     content: const Text("Mock quick action menu"),
    //     actions: [
    //       TextButton(
    //         onPressed: () => Navigator.of(context).pop(),
    //         child: const Text("Close"),
    //       ),
    //     ],
    //   ),
    // );
  }

  /// Handle swipe gesture on icon (mock action)
  void onIconSwipe(int index) {
    // ScaffoldMessenger.of(context).showSnackBar(
    //   SnackBar(content: Text("Swipe gesture on ${_getTabName(index)}")),
    // );
  }

  /// Get tab name for feedback messages
  String _getTabName(int index) {
    // To be overridden by subclasses with specific tab names
    const defaultNames = ["Home", "Search", "Notifications", "Add", "Profile"];
    return defaultNames[index];
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
      stream: AppConstants.bottomNavigationRefreshCtrl.stream,
      initialData: true,
      builder: (context, refreshSnapshot) {
        return PopScope(
          canPop: false,
          onPopInvoked: (didPop) async {
            if (!didPop) {
              final shouldPop = await onWillPop();
              if (shouldPop && context.mounted) {
                Navigator.of(context).pop();
              }
            }
          },
          child: Scaffold(
            body: IndexedStack(
              index: _currentIndex,
              children: List.generate(
                  tabCount, (index) => _buildOffstageNavigator(index)),
            ),
            bottomNavigationBar: StreamBuilder<bool>(
              stream: _autoHideService.bottomNavVisibilityStream,
              initialData: true,
              builder: (context, snapshot) {
                final isVisible = snapshot.data ?? true;
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                  height: isVisible ? null : 0,
                  child: AnimatedOpacity(
                    duration: const Duration(milliseconds: 300),
                    opacity: isVisible ? 1.0 : 0.0,
                    child: isVisible
                        ? buildCustomBottomNav()
                        : const SizedBox.shrink(),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  /// Build offstage navigator for each tab
  Widget _buildOffstageNavigator(int index) {
    return Offstage(
      offstage: _currentIndex != index,
      child: Navigator(
        key: _navigatorKeys[index],
        onGenerateRoute: (settings) {
          return MaterialPageRoute(
            builder: (_) => NavigationPageWrapper(
              enableAutoHide: true,
              child: tabPages[index],
            ),
          );
        },
      ),
    );
  }
}
