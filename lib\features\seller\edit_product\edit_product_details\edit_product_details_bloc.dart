import 'dart:async';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/seller/add_and_edit_image/selected_image_edit_preview/selected_image_edit_preview_screen.dart';
import 'package:swadesic/features/seller/add_edit_product_fields/add_edit_product_fields_bloc.dart';
import 'package:swadesic/features/seller/add_image/add_image_screen.dart';
import 'package:swadesic/features/seller/labels/labels_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_return_store_warranty/seller_return_store_warranty_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_delivery_setting/seller_store_delivey_setting_screen.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/store_dashboard_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/getProduct_image_response/get_seller_product_Image_response.dart';
import 'package:swadesic/model/getProduct_image_response/get_seller_product_detail.dart';
import 'package:swadesic/model/product_variant/product_variant.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';

import 'package:swadesic/services/edit_product_image/edit_product_image.dart';
import 'package:swadesic/services/get_product_and_image/get_product_and_image.dart';
import 'package:swadesic/services/seller_hide_delete_service/seller_hide_delete_service.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/services/product_variant_service/product_variant_service.dart';
import 'package:swadesic/model/product_variant/product_variant.dart';

import '../../inventory_options/inventory_options_screen.dart';
import '../../../../../features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';

enum EditProductDetailsState { Loading, Success, Failed }

enum EditProductImageState { Loading, Success, Failed }

class EditProductDetailsBloc {
  // region Common Variables
  BuildContext context;
  final int storeId;
  late ProductAndImageServices productAndImageServices;
  late EditProductAndImageServices editProductAndImageServices;
  late ProductVariantService productVariantService;
  bool drawerVisibility = false;
  late GetOnlyProductDetail getOnlyProductResponse;
  late ProductImageResponse productImageResponse;
  int i = 0;
  late Product updatedLastProduct = Product();
  final List<String> productReferenceList;

  //final int storeId;

  // endregion

  //region Text Editing Controller
  //endregion

  //region Controller
  final editProductDetailCtrl =
      StreamController<EditProductDetailsState>.broadcast();
  final editProductImageCtrl =
      StreamController<EditProductImageState>.broadcast();
  final editProductNumberCtrl = StreamController<int>.broadcast();
  final imageCtrl = StreamController<bool>.broadcast();
  final drawerCtrl = StreamController<bool>.broadcast();
  //endregion

  // region | Constructor |
  EditProductDetailsBloc(this.context, this.storeId, this.productReferenceList);

  // endregion

  // region Init
  void init() {
    ///Put edit product not image
    editProductAndImageServices = EditProductAndImageServices();

    ///Get Product
    productAndImageServices = ProductAndImageServices();

    ///Product Variant Service
    productVariantService = ProductVariantService();

    getProductDetailAndAdd();
    // /// Api CAll Get Product Images
    // getProductImages();
    ///initialize photo Response
    productImageResponse = ProductImageResponse();
  }
  // endregion

  //region On Tap Drawer
  onTapDrawer() {
    if (productReferenceList.isEmpty) {
      CommonMethods.toastMessage(
          "Please select any product to continue", context);
      return;
    }
    drawerVisibility = !drawerVisibility;
    drawerCtrl.sink.add(drawerVisibility);
  }
  //endregion

  //region Drawer Option Select
  void drawerOptionSelect(int index) {
    onTapDrawer();
    if (index == 0) {
      //updateStockDialog();
      return;
    }
    if (index == 1) {
      //goToEditProductDetails();
      return;
    }
  }
  //endregion

  //region Go To Select Image Screen
  void goToAddImage() {
    var screen = const AddImageScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((_) {
      if (AppConstants.multipleSelectedImage == null) {
        return imageCtrl.sink.add(false);
      }
      imageCtrl.sink.add(true);
    });
  }
  //endregion

  // Track pending image changes
  bool hasPendingImageChanges = false;
  List<int> pendingImageDeletions = [];
  List<dynamic>? pendingImageChangesData;

  //region Apply Pending Image Changes
  Future<void> applyPendingImageChanges() async {
    if (AppConstants.hasPendingImageChanges) {
      try {
        // Apply pending image deletions
        if (AppConstants.pendingImageDeletions.isNotEmpty) {
          for (int imageId in AppConstants.pendingImageDeletions) {
            await SellerHideDeleteService().deleteImage(imageId);
          }
          AppConstants.pendingImageDeletions.clear();
        }

        // Apply pending image additions if any
        if (AppConstants.pendingImageAdditions.isNotEmpty) {
          // Handle image uploads for the current product
          String currentProductReference = productReferenceList[
              i < productReferenceList.length
                  ? i - 1
                  : productReferenceList.length - 1];

          for (XFile imageFile in AppConstants.pendingImageAdditions) {
            await UploadFileService().uploadProductImage(
              filePath: imageFile.path,
              url:
                  "${AppConstants.baseUrl}/product/productimages/$currentProductReference/",
              fileNameWithExtension: imageFile.name,
              parameter: {},
            );
          }
          AppConstants.pendingImageAdditions.clear();
        }

        // Apply pending image reorders if any
        if (AppConstants.pendingImageReorders.isNotEmpty) {
          for (String productRef in AppConstants.pendingImageReorders.keys) {
            List<dynamic> reorderedList =
                AppConstants.pendingImageReorders[productRef]!;
            // Apply reorder logic here if needed
            // This would require implementing the reorder API call
          }
          AppConstants.pendingImageReorders.clear();
        }

        // Clear pending changes flag
        AppConstants.hasPendingImageChanges = false;
      } catch (error) {
        // If there's an error, don't clear the pending changes
        // so user can try again
        throw error;
      }
    }
  }
  //endregion

  //region Go Back to Selected Image Edit Preview Screen
  void goToSelectedImageEditPreviewScreen() {
    var screen = SelectedImageEditPreviewScreen(
      productReference: productReferenceList[i].toString(),
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.globalNavigator.currentContext!, route)
        .then((returnValue) {
      // Handle return value if the user made changes but didn't save them
      if (returnValue != null && returnValue is Map) {
        hasPendingImageChanges = returnValue['hasChanges'] ?? false;
        pendingImageDeletions =
            List<int>.from(returnValue['imagesToDelete'] ?? []);
        pendingImageChangesData = returnValue['pendingImages'] ?? [];
      }

      getProductImages();
      imageCtrl.sink.add(true);
      // //print("Hello");
      // if(AppConstants.multipleSelectedImage == null) {
      //   return imageCtrl.sink.add(false);
      // }
      // imageCtrl.sink.add(true);
    });
  }
  //endregion

  //region Get Only Product And Add To Text Fields
  void getProductDetailAndAdd() async {
    try {
      //Close Keyboard
      editProductDetailCtrl.sink.add(EditProductDetailsState.Loading);
      //Api Call For get product details
      getOnlyProductResponse =
          await productAndImageServices.getOnlyProduct(productReferenceList[i]);

      ///Adding Api Data to the Text Field
      await addDataToField();

      /// Api CAll Get Product Images
      getProductImages();
      editProductDetailCtrl.sink.add(EditProductDetailsState.Success);
    } on ApiErrorResponseMessage {
      editProductDetailCtrl.sink.add(EditProductDetailsState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    } catch (error) {
      editProductDetailCtrl.sink.add(EditProductDetailsState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }
//endregion

  //region Add product detail to field
  addDataToField() {
    AddEditProductFieldsBloc.brandNameTextCtrl.text =
        getOnlyProductResponse.singleProductData!.brandName!;
    AddEditProductFieldsBloc.productNameTextCtrl.text =
        getOnlyProductResponse.singleProductData!.productName!;
    AddEditProductFieldsBloc.productCategoryTextCtrl.text =
        getOnlyProductResponse.singleProductData!.productCategory ?? "";
    AddEditProductFieldsBloc.promoLinkTextCtrl.text =
        getOnlyProductResponse.singleProductData!.promotionLink!;
    AddEditProductFieldsBloc.productDescNameTextCtrl.text =
        getOnlyProductResponse.singleProductData!.productDescription!;
    AddEditProductFieldsBloc.inStockTextCtrl.text =
        getOnlyProductResponse.singleProductData!.inStock!.toString();
    AddEditProductFieldsBloc.hashTagsTextCtrl.text =
        getOnlyProductResponse.singleProductData!.hashTag!.toString();
    AddEditProductFieldsBloc.gender =
        getOnlyProductResponse.singleProductData!.targetGender!;
    //inStockTextCtrl.text = "100";
    AddEditProductFieldsBloc.mrpTextCtrl.text =
        getOnlyProductResponse.singleProductData!.mrpPrice!.toString();
    AddEditProductFieldsBloc.sellingPriceTextCtrl.text =
        getOnlyProductResponse.singleProductData!.sellingPrice!.toString();
    AddEditProductFieldsBloc.productSlugTextCtrl.text =
        getOnlyProductResponse.singleProductData!.productSlug!;
    AddEditProductFieldsBloc.productCodeTextCtrl.text =
        getOnlyProductResponse.singleProductData!.productCode!;

    // Load inventory options and variants data
    AddEditProductFieldsBloc.loadInventoryDataFromProduct(
      getOnlyProductResponse.singleProductData!.options,
      getOnlyProductResponse.singleProductData!.variants,
    );

    //print("In stock is ${getOnlyProductResponse.singleProductData!.inStock!}");
    //Refresh ui
    editProductDetailCtrl.sink.add(EditProductDetailsState.Success);
    AddEditProductFieldsBloc.storeHandle =
        getOnlyProductResponse.singleProductData!.storehandle!;
  }
  //endregion

  //region Put Edit product Api Call
  void putEditApiCall() async {
    //region Try
    try {
      //Brand name
      if (AddEditProductFieldsBloc.brandNameTextCtrl.text.trim().isEmpty) {
        return CommonMethods.toastMessage(
            AppStrings.brandNameCanNotBeEmpty, context);
      }
      //Product name
      if (AddEditProductFieldsBloc.productNameTextCtrl.text.trim().isEmpty) {
        return CommonMethods.toastMessage(
            AppStrings.productNameCanNotBeEmpty, context);
      }
      //Product category
      if (AddEditProductFieldsBloc.productCategoryTextCtrl.text
          .trim()
          .isEmpty) {
        return CommonMethods.toastMessage(
            AppStrings.productCategoryCanNotBeEmpty, context);
      }
      //Product description
      if (AddEditProductFieldsBloc.productDescNameTextCtrl.text
          .trim()
          .isEmpty) {
        return CommonMethods.toastMessage(
            AppStrings.productDescriptionCanNotBeEmpty, context);
      }
      //    if(AddEditProductFieldsBloc.promoLinkTextCtrl.text.isNotEmpty && !AddEditProductFieldsBloc.isUrlValid!){
      //Check url
      if (AddEditProductFieldsBloc.promoLinkTextCtrl.text.isNotEmpty &&
          AddEditProductFieldsBloc.isUrlValid != null &&
          !AddEditProductFieldsBloc.isUrlValid!) {
        CommonMethods.toastMessage(
            AppStrings.pleaseEnterValidPromotionLink, context);
        return;
      }
      //Stocks
      // if (AddEditProductFieldsBloc.inStockTextCtrl.text.trim().isEmpty ||
      //     int.parse(AddEditProductFieldsBloc.inStockTextCtrl.text) <= 0) {
      //   return CommonMethods.toastMessage(
      //       AppStrings.stockQuantityCanNotBeEmptyOrZero, context);
      // }
      // //MRP
      // if (AddEditProductFieldsBloc.mrpTextCtrl.text.trim().isEmpty ||
      //     int.parse(AddEditProductFieldsBloc.mrpTextCtrl.text) <= 0) {
      //   return CommonMethods.toastMessage(
      //       AppStrings.mrpCanNotBeEmptyOrZero, context);
      // }
      // //Selling price
      // if (AddEditProductFieldsBloc.sellingPriceTextCtrl.text.trim().isEmpty) {
      //   return CommonMethods.toastMessage(
      //       AppStrings.sellingPriceCanNotBeEmptyOrZero, context);
      // }

      // // Validate selling price against MRP
      // double mrp =
      //     double.parse(AddEditProductFieldsBloc.mrpTextCtrl.text.trim());
      // double sellingPrice = double.parse(
      //     AddEditProductFieldsBloc.sellingPriceTextCtrl.text.trim());
      // if (sellingPrice > mrp) {
      //   return CommonMethods.toastMessage(
      //       AppStrings.sellingPriceShouldbe, context);
      // }

      //Affiliate Promotion
      if (AddEditProductFieldsBloc.getIsAffiliatePromotionEnabled) {
        if (AddEditProductFieldsBloc.affiliateCommissionTextCtrl.text
            .trim()
            .isEmpty) {
          return CommonMethods.toastMessage(
              AppStrings.affiliateCommissionCanNotBeEmpty, context);
        }

        double commission = double.parse(
            AddEditProductFieldsBloc.affiliateCommissionTextCtrl.text.trim());
        double sellingPrice = double.parse(
            AddEditProductFieldsBloc.sellingPriceTextCtrl.text.trim());

        if (commission <= 0) {
          return CommonMethods.toastMessage(
              AppStrings.affiliateCommissionMustBeGreaterThanZero, context);
        }

        if (commission >= sellingPrice) {
          return CommonMethods.toastMessage(
              AppStrings.affiliateCommissionShouldBeLessThanSellingPrice,
              context);
        }
      }

      //product slug
      if (AddEditProductFieldsBloc.productSlugTextCtrl.text.trim().isEmpty) {
        return CommonMethods.toastMessage(
            AppStrings.productSlugCanNotBeEmpty, context);
      }

      editProductDetailCtrl.sink.add(EditProductDetailsState.Loading);

      await editProductAndImageServices.editOnlyProduct(
          productReference:
              getOnlyProductResponse.singleProductData!.productReference!,
          productName: AddEditProductFieldsBloc.productNameTextCtrl.text.trim(),
          brandName: AddEditProductFieldsBloc.brandNameTextCtrl.text.trim(),
          productCategory:
              AddEditProductFieldsBloc.productCategoryTextCtrl.text.trim(),
          productDescription:
              AddEditProductFieldsBloc.productDescNameTextCtrl.text.trim(),
          promotionLink: CommonMethods.addHttpAndHttps(
              url: AddEditProductFieldsBloc.promoLinkTextCtrl.text),
          inStock: int.parse(AddEditProductFieldsBloc.inStockTextCtrl.text),
          mrpPrice: int.parse(AddEditProductFieldsBloc.mrpTextCtrl.text),
          sellingPrice:
              int.parse(AddEditProductFieldsBloc.sellingPriceTextCtrl.text),
          storeid: getOnlyProductResponse.singleProductData!.storeid!,
          swadeshiMade: getOnlyProductResponse.singleProductData!.swadeshiMade!,
          swadeshiBrand:
              getOnlyProductResponse.singleProductData!.swadeshiBrand!,
          swadeshiOwned:
              getOnlyProductResponse.singleProductData!.swadeshiOwned!,
          hashTag: getOnlyProductResponse.singleProductData!.hashTag!,
          productSlug: AddEditProductFieldsBloc.productSlugTextCtrl.text.trim(),
          productCode: AddEditProductFieldsBloc.productCodeTextCtrl.text.trim(),
          gender: AddEditProductFieldsBloc.gender,
          isAffiliatePromotionEnabled:
              AddEditProductFieldsBloc.getIsAffiliatePromotionEnabled,
          affiliateCommissionAmount:
              AddEditProductFieldsBloc.getIsAffiliatePromotionEnabled
                  ? double.parse(AddEditProductFieldsBloc
                      .affiliateCommissionTextCtrl.text
                      .trim())
                  : 0.0,
          options: AddEditProductFieldsBloc.productOptions);

      // Save product variants if they exist
      await saveProductVariants();

      //Api Call For get product details
      getOnlyProductResponse = await productAndImageServices
          .getOnlyProduct(productReferenceList.last);

      editProductDetailCtrl.sink.add(EditProductDetailsState.Success);

      ///Add updated product detail to updatedLastProduct
      updatedLastProduct = getOnlyProductResponse.singleProductData!;

      ///Clear Text Fields after put api is success
      cleanTextCtrl();

      ///Increase the value of I and check is it smaller then the selected product length or not
      i++;
      if (i < productReferenceList.length) {
        //Clear selected pic
        // AppConstants.multipleSelectedImage.clear();
        //Edit product ctrl
        editProductNumberCtrl.sink.add(i);
        //GEt product detail
        return getProductDetailAndAdd();
      } else {
        // Apply any pending image changes for all products before finishing
        await applyPendingImageChanges();

        ///Send back updated data
        sendBackUpdatedData();

        /// Navigate to product view screen
        if (context.mounted) {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => BuyerViewStoreScreen(
                storeReference:
                    getOnlyProductResponse.singleProductData!.storeReference!,
              ),
            ),
          );
        }
      }

      //productId = addProductDetailResponse.data!.productid!;
      //startUpload();
    }
    //endregion
    on ApiErrorResponseMessage {
      editProductDetailCtrl.sink.add(EditProductDetailsState.Failed);
      return CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
    } catch (error) {
      editProductDetailCtrl.sink.add(EditProductDetailsState.Failed);
      return CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
    }
  }
  //endregion

  //region Get product detail

  //endregion

  //region Send back last updated data
  void sendBackUpdatedData() async {
    try {
      //Close Keyboard
      editProductDetailCtrl.sink.add(EditProductDetailsState.Loading);
      //Api Call For get product details
      getOnlyProductResponse = await productAndImageServices
          .getOnlyProduct(productReferenceList.last);

      //Api call to get all images
      productImageResponse = await productAndImageServices
          .getOnlyProductImage(productReferenceList.last);

      ///Add updated product detail to updatedLastProduct
      updatedLastProduct = getOnlyProductResponse.singleProductData!;

      ///Add updated product images to updatedLastProduct
      updatedLastProduct.prodImages = productImageResponse.data!;

      // updatedLastProduct.prodImages = productImageResponse.data;
      Navigator.pop(context, updatedLastProduct);

      editProductDetailCtrl.sink.add(EditProductDetailsState.Success);
    } on ApiErrorResponseMessage {
      editProductDetailCtrl.sink.add(EditProductDetailsState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    } catch (error) {
      editProductDetailCtrl.sink.add(EditProductDetailsState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }
  //endregion

  //region Get Product Images
  void getProductImages() async {
    try {
      //editProductDetailCtrl.sink.add(EditProductDetailsState.Loading);

      editProductImageCtrl.sink.add(EditProductImageState.Loading);
      productImageResponse = await productAndImageServices
          .getOnlyProductImage(productReferenceList[i]);

      ///Add product images to updatedLastProduct
      updatedLastProduct.prodImages = productImageResponse.data!;
      editProductImageCtrl.sink.add(EditProductImageState.Success);
    } on ApiErrorResponseMessage {
      editProductImageCtrl.sink.add(EditProductImageState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    } catch (error) {
      editProductImageCtrl.sink.add(EditProductImageState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }
  //endregion

  //region Go To Seller Return Product Warranty
  void goToSellerReturnProductWarranty() {
    var screen = SellerReturnStoreWarrantyScreen(
      storeRef: getOnlyProductResponse.singleProductData!.storeReference,
      productRef: getOnlyProductResponse.singleProductData!.productReference,
      fromEditProductScreen: true,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

  //region Go to Deliver setting
  void goToDeliverSettingScreen() {
    /// Reference to the StoreDashboardDataModel
    StoreDashboardDataModel storeDashboardDataModel =
        Provider.of<StoreDashboardDataModel>(context, listen: false);

    //Check is store has completed the trust center
    if (storeDashboardDataModel.storeDashBoard.trustcenterDetail == null ||
        !storeDashboardDataModel.storeDashBoard.trustcenterDetail!) {
      return CommonMethods.toastMessage(
          AppStrings.pleaseCompleteTrustCenterDetail, context);
    }
    var screen = SellerStoreDeliverySettingScreen(
      storeRef: getOnlyProductResponse.singleProductData!.storeReference!,
      productReference:
          getOnlyProductResponse.singleProductData!.productReference,
      productId: getOnlyProductResponse.singleProductData!.productid,
      isFromAddProduct: false,
      isFromEditProduct: true,
      isFromStore: false,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

  ///Go to label screen
  //region Go to labels
  void goToLabels({required String storeReference}) async {
    var screen = LabelsScreen(
      isFromTrustCenter: false,
      storeReference: storeReference,
      product: getOnlyProductResponse.singleProductData,
      brand: getOnlyProductResponse.singleProductData!.swadeshiBrand,
      owned: getOnlyProductResponse.singleProductData!.swadeshiOwned,
      made: getOnlyProductResponse.singleProductData!.swadeshiMade,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      if (value != null) {
        //If it has data
        if (value != null) {
          CommonMethods.toastMessage(
              AppStrings.yourLabelsForThisProductIsSaved, context,
              toastShowTimer: 3);
          editProductDetailCtrl.sink.add(EditProductDetailsState.Success);

          //print("data updated");
        }
      } else {
        return;
      }
    });
  }
//endregion

  //region Save Product Variants
  Future<void> saveProductVariants() async {
    try {
      // Get the product reference
      final productReference =
          getOnlyProductResponse.singleProductData!.productReference!;
      List<Map<String, dynamic>> updatedVariants = [];

      // Check if we have variants to save
      if (AddEditProductFieldsBloc.productVariants.isNotEmpty &&
          AddEditProductFieldsBloc.hasMultipleOptions) {
        // Save each variant and collect updated data (for products with options)
        for (final variant in AddEditProductFieldsBloc.productVariants) {
          final response = await productVariantService.createOrUpdateVariant(
            productReference: productReference,
            mrpPrice: variant.mrpPrice,
            sellingPrice: variant.sellingPrice,
            stock: variant.stock,
            combinations: variant.combinations,
          );
          // Add the updated variant data from API response
          updatedVariants.add(response.productVariant.toJson());
        }
      } else {
        // For "No options" scenario (or empty variants):
        // 1. First delete all existing variants with combinations
        await _deleteAllExistingVariantsWithCombinations(productReference);

        // 2. Then create a single variant with empty combinations
        // Use data from the variant if available, otherwise fallback to text controllers
        int mrpPrice, sellingPrice, stock;

        if (AddEditProductFieldsBloc.productVariants.isNotEmpty) {
          // Use data from the variant returned by InventoryOptionsBloc
          final variant = AddEditProductFieldsBloc.productVariants.first;
          mrpPrice = variant.mrpPrice;
          sellingPrice = variant.sellingPrice;
          stock = variant.stock;
        } else {
          // Fallback to text controllers if no variant data available
          mrpPrice = int.parse(AddEditProductFieldsBloc.mrpTextCtrl.text);
          sellingPrice =
              int.parse(AddEditProductFieldsBloc.sellingPriceTextCtrl.text);
          stock = int.parse(AddEditProductFieldsBloc.inStockTextCtrl.text);
        }

        // This ensures the product's pricing and stock data is saved to database
        final response = await productVariantService.createOrUpdateVariant(
          productReference: productReference,
          mrpPrice: mrpPrice,
          sellingPrice: sellingPrice,
          stock: stock,
          combinations: {}, // Empty combinations for no-options scenario
        );
        updatedVariants.add(response.productVariant.toJson());
      }

      // Update the global product data with latest variant information
      if (updatedVariants.isNotEmpty) {
        getOnlyProductResponse.singleProductData!.variants = updatedVariants;
        getOnlyProductResponse.singleProductData!.options =
            AddEditProductFieldsBloc.productOptions;
      }
    } catch (e) {
      // Log error but don't fail the entire save operation
      print('Error saving variants: $e');
    }
  }

  //region Delete All Existing Variants With Combinations
  Future<void> _deleteAllExistingVariantsWithCombinations(
      String productReference) async {
    try {
      // Get all existing variants for this product
      final response = await productVariantService.getProductVariants(
        productReference: productReference,
      );

      // Delete all variants that have combinations (not empty)
      for (final variant in response.productVariants) {
        if (variant.combinations.isNotEmpty &&
            variant.variantReference != null) {
          await productVariantService.deleteVariant(
            variantReference: variant.variantReference!,
          );
        }
      }
    } catch (e) {
      print('Error deleting existing variants: $e');
      // Don't throw error, let the main save operation continue
    }
  }
  //endregion

  //region Clear all Text Controller
  void cleanTextCtrl() {
    AddEditProductFieldsBloc.brandNameTextCtrl.clear();
    AddEditProductFieldsBloc.productNameTextCtrl.clear();
    AddEditProductFieldsBloc.productDescNameTextCtrl.clear();
    AddEditProductFieldsBloc.promoLinkTextCtrl.clear();
    AddEditProductFieldsBloc.hashTagsTextCtrl.clear();
    AddEditProductFieldsBloc.inStockTextCtrl.clear();
    AddEditProductFieldsBloc.mrpTextCtrl.clear();
    AddEditProductFieldsBloc.sellingPriceTextCtrl.clear();
  }
  //endregion

  //region Go to Inventory Options
  void goToInventoryOptions() async {
    var screen = InventoryOptionsScreen(
      storeReference: getOnlyProductResponse.singleProductData!.storeReference!,
      product: getOnlyProductResponse.singleProductData!,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      if (value != null) {
        //If it has data - update the AddEditProductFieldsBloc with the new variant data
        if (value != null && value is Map<String, dynamic>) {
          // Update the AddEditProductFieldsBloc with the returned data
          AddEditProductFieldsBloc.productOptions =
              Map<String, List<String>>.from(value['options'] ?? {});
          AddEditProductFieldsBloc.productVariants =
              List<ProductVariant>.from(value['variants'] ?? []);
          AddEditProductFieldsBloc.hasMultipleOptions =
              value['hasMultipleOptions'] ?? false;

          // Also update the product object with the new data for consistency
          getOnlyProductResponse.singleProductData!.options =
              AddEditProductFieldsBloc.productOptions.isNotEmpty
                  ? AddEditProductFieldsBloc.productOptions
                  : null;
          getOnlyProductResponse.singleProductData!.variants =
              AddEditProductFieldsBloc.productVariants.isNotEmpty
                  ? AddEditProductFieldsBloc.productVariants
                      .map((v) => v.toJson())
                      .toList()
                  : null;

          CommonMethods.toastMessage(
              AppStrings.inventoryOptionsUpdated, context,
              toastShowTimer: 3);
          //Refresh UI to show updated data
          editProductDetailCtrl.sink.add(EditProductDetailsState.Success);
        }
      } else {
        return;
      }
    });
  }
  //endregion
}
