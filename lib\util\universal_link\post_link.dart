import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/post/single_post_view/single_post_view_screen.dart';
import 'package:swadesic/util/app_constants.dart';

class PostLink{
  String postReference  = "";

  PostLink(this.postReference){
    action();
  }


  //region Action
  void action(){
    // Switch to appropriate tab before navigating
    _switchToAppropriateTab();

    var screen = SinglePostViewScreen(postReference: postReference,);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.currentSelectedTabContext, route);
    return;

  }

  /// Switch to the appropriate tab for post content
  void _switchToAppropriateTab() {
    try {
      // Posts should open in Home tab for both user and store views
      if (AppConstants.appData.isUserView ?? true) {
        // User view: switch to Home tab (index 0)
        AppConstants.userPersistentTabController.jumpToTab(0);
      } else if (AppConstants.appData.isStoreView ?? false) {
        // Store view: switch to Home tab (index 0)
        AppConstants.storePersistentTabController.jumpToTab(0);
      }
    } catch (e) {
      debugPrint("Error switching to appropriate tab for post: $e");
    }
  }
//endregion



}