import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/post/single_post_view/single_post_view_screen.dart';
import 'package:swadesic/util/app_constants.dart';

class PostLink{
  String postReference  = "";

  PostLink(this.postReference){
    action();
  }


  //region Action
  void action(){
    // Open post in current tab for immediate visibility
    var screen = SinglePostViewScreen(postReference: postReference,);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.currentSelectedTabContext, route);
    return;

  }
//endregion



}