import 'dart:async';
import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/util/web_file_picker.dart';
import 'package:swadesic/features/common_buyer_seller_screen/add_image_option/add_image_option_screen.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/post/single_post_view/single_post_bloc.dart';
import 'package:swadesic/features/post/upload_post_files_in_background/upload_post_files.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/comment/comment_access_detail.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/services/web_upload_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/my_reach_text_controller/my_reach_text_controller.dart';

enum CommentFieldState { Loading, Failed, Success }

class CommentFieldsBloc {
  //region Common variable
  late BuildContext context;
  late ScrollController scrollController = ScrollController();
  List<File> selectedImage = []; // For mobile platform
  List<Map<String, dynamic>> webSelectedImage = []; // For web platform
  final SinglePostViewBloc singlePostViewBloc;
  String ratingCount = "5";
  late CommentAccessDetails commentAccessDetails;

  //endregion
  //region Text editing ctrl
  MyReachTextController commentTextCtrl =
      MyReachTextController(patternMatchMap: {
    RegExp(AppConstants.atTag):
        AppTextStyle.contentText0(textColor: AppColors.brandBlack),
  }, onMatch: (List<String> match) {});
  //endregion
  //region Focus controller
  late FocusNode commentTextFocusCtrl = FocusNode();
  //endregion

//region Controller
  final commentFieldStatCtrl = StreamController<CommentFieldState>.broadcast();
  final replyHandleVisibleCtrl = StreamController<bool>.broadcast();
  final postAsVisibleCtrl = StreamController<bool>.broadcast();
  final postAsReviewVisible = StreamController<bool>.broadcast();

  // ValueNotifier<CommentPaginationState> commentPaginationStateCtrl = ValueNotifier(CommentPaginationState.Success);

//endregion
  //region Constructor
  CommentFieldsBloc(
    this.context,
    this.singlePostViewBloc,
  ) {
    init();
  }

  //endregion
//region Init
  init() {
    commentTextCtrl.addListener(() {
      commentFieldListener();
    });
  }
//endregion

  //region Get comment access details
  Future<void> getCommentAccessDetail() async {
    if (!singlePostViewBloc.isFromProduct) {
      return;
    }
    try {
      commentFieldStatCtrl.sink.add(CommentFieldState.Loading);
      commentAccessDetails = await PostService().getCommentAccessDetail(
          productReference: singlePostViewBloc.postProductCommentReference,
          reference: AppConstants.appData.isUserView!
              ? AppConstants.appData.userReference!
              : AppConstants.appData.storeReference!);
      commentFieldStatCtrl.sink.add(CommentFieldState.Success);
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      commentFieldStatCtrl.sink.add(CommentFieldState.Failed);

      return;
    } catch (error) {
      //Failed
      commentFieldStatCtrl.sink.add(CommentFieldState.Failed);
      return;
    }
  }
//endregion

  //region Listen comment field
  void commentFieldListener() {
    //If empty then hide post as option
    if (commentTextCtrl.text.trim().isEmpty) {
      postAsVisibleCtrl.sink.add(false);
    }
    //Comment field is not empty, From from screen and it's not a reply message
    else if (commentTextCtrl.text.trim().isNotEmpty &&
        singlePostViewBloc.isFromProduct &&
        singlePostViewBloc.replyCommentOrPostDetail['handle']
            .toString()
            .isEmpty) {
      postAsVisibleCtrl.sink.add(true);
    } else {
      postAsVisibleCtrl.sink.add(false);
    }
  }
  //endregion

  //region On tap reply
  void onTapReply({required Map<String, dynamic> replyCommentOrPostDetail}) {
    //Visible
    replyHandleVisibleCtrl.sink.add(true);
    //Focus the text field
    singlePostViewBloc.commentFieldsBloc.commentTextFocusCtrl.requestFocus();
    //Comment field listener
    commentFieldListener();
  }
  //endregion

  //region On tap remove reply
  void onTapRemoveReply() {
    singlePostViewBloc.addDefaultReplyCommentDetail();
    //Remove reply card
    //Hide
    replyHandleVisibleCtrl.sink.add(false);
    //Hide comment field
    singlePostViewBloc.commentFieldVisibilityCtrl.sink.add(false);
    //Un focus the text field
    singlePostViewBloc.commentFieldsBloc.commentTextFocusCtrl.unfocus();
  }
  //endregion

  //region Send comment
  Future<void> sendComment(
      {required Map<String, dynamic> replyCommentOrPostDetail,
      required CommentEnums commentEnums,
      String? reviewCount}) async {
    //If empty
    if (commentTextCtrl.text.trim().isEmpty &&
        selectedImage.isEmpty &&
        webSelectedImage.isEmpty) {
      return CommonMethods.toastMessage(
          AppStrings.emptyCommentCanNotBeAdded, context);
    }
    //If static user
    if (CommonMethods().isStaticUser()) {
      return CommonMethods().goToSignUpFlow();
    }

    try {
      //Remove reply option
      onTapRemoveReply();

      // For web platform
      if (kIsWeb && webSelectedImage.isNotEmpty) {
        // Create a URL for the comment creation API
        final url = AppConstants.createComment;

        // Create a map of form data
        final formData = {
          'comment_text': commentTextCtrl.text,
          'parent_reference': replyCommentOrPostDetail['reference'],
          'comment_type': commentEnums.toString().split('.').last,
          'entity_reference': AppConstants.appData.isUserView!
              ? AppConstants.appData.userReference
              : AppConstants.appData.storeReference,
        };

        // Add rating if it's a review
        if (commentEnums == CommentEnums.REVIEW && reviewCount != null) {
          formData['rating'] = reviewCount;
        }

        // Create a WebUploadService instance
        final webUploadService = WebUploadService();

        // Upload the comment with images
        await webUploadService
            .createPost(
          url: url,
          formData: formData,
          images: webSelectedImage,
        )
            .then((commentReference) {
          // Add new comment into the block list if it's a direct comment (not a reply)
          if (replyCommentOrPostDetail['handle'].toString().isEmpty) {
            // Get the comment details and add to the list
            PostService()
                .getSingleComment(commentReference: commentReference)
                .then((value) {
              singlePostViewBloc.commentList.add(value);

              // Post or comment data model
              var postDataModel =
                  Provider.of<PostDataModel>(context, listen: false);
              // Increase comment count of parent post or comment
              var data = postDataModel.allPostDetailList.firstWhere((element) =>
                  element.postOrCommentReference ==
                  replyCommentOrPostDetail['reference']);
              data.commentCount = data.commentCount! + 1;
              // Update data model UI
              postDataModel.updateUi();
              // Update single post or comments UI
              singlePostViewBloc.commentStateCtrl.sink
                  .add(CommentsState.Success);
            });
          }
        });
      }
      // For mobile platform
      else {
        //Api call
        PostAndCommentUploadFiles()
            .sendComment(
                reviewCount: reviewCount,
                addNewCommentToDataModel: true,
                parentReference: replyCommentOrPostDetail['reference'],
                commentText: commentTextCtrl.text,
                selectedFiles: selectedImage.map((file) => file.path).toList(),
                commentEnums: commentEnums)
            .then((value) {
          //Add new comment into the block list
          if (replyCommentOrPostDetail['handle'].toString().isEmpty) {
            singlePostViewBloc.commentList.add(value);
          }
          //Post or comment data model
          var postDataModel =
              Provider.of<PostDataModel>(context, listen: false);
          //Increase comment count of parent post or comment
          var data = postDataModel.allPostDetailList.firstWhere((element) =>
              element.postOrCommentReference ==
              replyCommentOrPostDetail['reference']);
          data.commentCount = data.commentCount! + 1;
          //Update data model ui
          postDataModel.updateUi();
          //Update single post or comments ui
          singlePostViewBloc.commentStateCtrl.sink.add(CommentsState.Success);
        });
      }

      //Close keyboard and clear data
      context.mounted ? CommonMethods.closeKeyboard(context) : null;
      commentTextCtrl.clear();
      selectedImage.clear();
      webSelectedImage.clear();

      //Update comment filed ui
      commentFieldStatCtrl.sink.add(CommentFieldState.Success);
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
    } catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
    }
  }
//endregion

  //region On tap add image
  void onTapAddImage() async {
    // For web platform
    if (kIsWeb) {
      try {
        // Pick multiple images
        final images = await WebFilePicker.pickMultipleImages();

        if (images != null && images.isNotEmpty) {
          // Add selected web images
          webSelectedImage.addAll(images);

          // Update UI
          commentFieldStatCtrl.sink.add(CommentFieldState.Success);
        }
      } catch (e) {
        // Handle error
        context.mounted
            ? CommonMethods.toastMessage(AppStrings.noImageIsSelected, context)
            : null;
      }
    }
    // For mobile platform
    else {
      Widget screen = const AddImageOptionScreen();
      var route = MaterialPageRoute(builder: (context) => screen);
      Navigator.push(context, route).then((value) {
        //If value is null
        if (value == null) {
          return;
        }

        //Add selected image
        selectedImage.addAll(value);

        //Update ui
        commentFieldStatCtrl.sink.add(CommentFieldState.Success);
      });
    }
  }
  //endregion

  //region On tap remove image
  void onTapRemoveImage({File? filePath, Map<String, dynamic>? webFilePath}) {
    if (kIsWeb && webFilePath != null) {
      // Remove web image from the list
      webSelectedImage.removeWhere((element) =>
          element['name'] == webFilePath['name'] &&
          element['size'] == webFilePath['size']);
    } else if (filePath != null) {
      // Remove mobile image from the list
      selectedImage.removeWhere((element) => element == filePath);
    }

    // Update UI
    commentFieldStatCtrl.sink.add(CommentFieldState.Success);
  }
  //endregion

//region Dispose
  void dispose() {
    // singlePostStateCtrl.close();
  }
//endregion
}
