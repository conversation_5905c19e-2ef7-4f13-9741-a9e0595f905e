import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:metadata_fetch/metadata_fetch.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/env.dart';
import 'package:swadesic/features/common_buyer_seller_screen/app_web_view/app_web_view.dart';
import 'package:swadesic/features/common_buyer_seller_screen/super_link/super_link_products/super_link_product_pagination.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/single_store_info_response/single_store_info_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/store_and_user_reference_services/store_and_user_reference_services.dart';
import 'dart:async';

import 'package:swadesic/services/store_info_service/store_info_service.dart';
import 'package:swadesic/services/store_product_services/store_product_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:url_launcher/url_launcher.dart';

enum SuperLinkStoreState { Loading, Success, Failed, Empty }

enum SuperLinkProductState { Loading, Success, Failed, Empty }

class SuperLinkBloc {
  //region Common Variables
  late BuildContext context;
  late SuperLinkProductPagination superLinkProductPagination;
  late SingleStoreInfoResponse singleStoreInfoResponse =
      SingleStoreInfoResponse();
  final ScrollController scrollController = ScrollController();
  final TabController tabController;
  List<Product> storeProductList = [];
  final String handle;
  String reference = "";
  List<String> domain = [
    "whatsApp",
    "instagram",
    "amazon",
    "flipkart",
    "youTube",
    "snapchat",
    "linkedin",
    "facebook",
    "x",
  ];

  List<String> superLinkIcons = [
    AppImages.amazonSuperLinkIcon,
    AppImages.facebookSuperLinkIcon,
    AppImages.flipkartSuperLinkIcon,
    AppImages.instagramSuperLinkIcon,
    AppImages.linkedinSuperLinkIcon,
    AppImages.snapchatSuperLinkIcon,
    AppImages.whatsappSuperLinkIcon,
    AppImages.xSuperLinkIcon,
    AppImages.youtubeLinkIcon,
  ];

  //endregion

  //region Controllers
  final superLinkStoreCtrl = StreamController<SuperLinkStoreState>.broadcast();
  final tabRefreshCtrl = StreamController<bool>.broadcast();
  final superLinkProductsCtrl =
      StreamController<SuperLinkProductState>.broadcast();

  //endregion

//region Constructor
  SuperLinkBloc(this.context, this.tabController, this.handle);

  //endregion

//region Init
  void init() {
    superLinkProductPagination = SuperLinkProductPagination(context, this);
    getStoreAndUserReference();
    tabController.addListener(() {
      tabRefreshCtrl.sink.add(true);
    });
  }

//endregion

  //region Get Store and user reference
  Future<void> getStoreAndUserReference() async {
    try {
      ///If user is logged in
      if (AppConstants.appData.userReference != null) {
        //Get reference from handle
        reference = await StoreAndUserReferenceServices()
            .getStoreAndUserReferences(handleAndUserName: handle);
        getSingleStoreInfo();
      }

      ///Else assign static user info and then get single store info
      else {
        //Assign static user info
        assignStaticUserInfo();
        //Get reference from handle
        reference = await StoreAndUserReferenceServices()
            .getStoreAndUserReferences(handleAndUserName: handle);
        //If empty reference
        if (reference.isEmpty) {
          superLinkStoreCtrl.sink.add(SuperLinkStoreState.Failed);
        }
        //Get store info
        getSingleStoreInfo();
      }
    } catch (error) {
      superLinkStoreCtrl.sink.add(SuperLinkStoreState.Failed);
    }
  }

//endregion

  //region Assign static user data in app constant
  void assignStaticUserInfo() {
    AppConstants.appData.accessToken =
        (AppConstants.appCurrentEnvironment == Environment.dev)
            ? AppConstants.staticDevAccessToken
            : AppConstants.staticProdAccessToken;
    AppConstants.appData.userReference =
        // "U1719603364911";
        AppConstants.staticUser;
    AppConstants.appData.accessToken =
        (AppConstants.appCurrentEnvironment == Environment.dev)
            ? AppConstants.staticDevAccessToken
            : AppConstants.staticProdAccessToken;
    AppConstants.appData.refreshToken =
        (AppConstants.appCurrentEnvironment == Environment.dev)
            ? AppConstants.staticDevRefreshToken
            : AppConstants.staticProdRefreshToken;
    AppConstants.appData.isUserView = true;
    AppConstants.appData.baseUrl =
        (AppConstants.appCurrentEnvironment == Environment.dev)
            ? "https://e2e-77-175.ssdcloudindia.net/dev"
            : "https://e2e-65-177.ssdcloudindia.net/prod";
    AppConstants.baseUrl =
        (AppConstants.appCurrentEnvironment == Environment.dev)
            ? "https://e2e-77-175.ssdcloudindia.net/dev"
            : "https://e2e-65-177.ssdcloudindia.net/prod";
  }

  //endregion

  ///Get store info
  //region Get Store Info Api call
  Future<void> getSingleStoreInfo() async {
    try {
      //Loading
      // superLinkStoreCtrl.sink.add(SuperLinkStoreState.Loading);
      singleStoreInfoResponse =
          await SingleStoreInfoServices().getSingleStoreInfo(reference);
      //Assign link icon
      await assignLinkIcon();
      superLinkStoreCtrl.sink.add(SuperLinkStoreState.Success);
      //Get store products
      getStoreProducts();
    } catch (error) {
      superLinkStoreCtrl.sink.add(SuperLinkStoreState.Failed);

      // CommonMethods.toastMessage(AppStrings.commonErrorMessage, context, commonErrorMessage: true);
      return;
    }
  }

//endregion

  ///Get store products
  //region Get Buyer Store Product Details
  Future<void> getStoreProducts() async {
    try {
      //Make pagination loading state
      superLinkProductPagination.superLinkStoreProductPaginationCtrl.sink
          .add(SuperLinkProductPaginationState.Loading);
      //Get reference to Product data model
      var productDataModel =
          Provider.of<ProductDataModel>(context, listen: false);
      //Api call to get list of products
      var data = await StoreProductServices().getBuyerStoreProduct(
          storeReference: reference, limit: 10, offset: 0);
      //Add all products into store product list
      storeProductList = data.data!;
      //Add all products in product data model
      productDataModel.addProductIntoList(products: storeProductList);
      //Success
      superLinkProductsCtrl.sink.add(SuperLinkProductState.Success);
    } on ApiErrorResponseMessage catch (error) {
      superLinkProductsCtrl.sink.add(SuperLinkProductState.Failed);
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      return;
    } catch (error) {
      superLinkProductsCtrl.sink.add(SuperLinkProductState.Failed);
      return;
    }
  }

  //endregion

  //region Assign link icon
  Future<void> assignLinkIcon() async {
    if (singleStoreInfoResponse.data!.storelinks != null &&
        singleStoreInfoResponse.data!.storelinks!.isNotEmpty) {
      for (var storeLink in singleStoreInfoResponse.data!.storelinks!) {
        //Assign link icon
        storeLink.linkIcon = getIconForUrl(url: storeLink.storeLink!);
      }
    }
  }

  //endregion

  //region Get icon from Url
  String getIconForUrl({required String url}) {
    List<String> superLinkIcons = [
      AppImages.amazonSuperLinkIcon,
      AppImages.facebookSuperLinkIcon,
      AppImages.flipkartSuperLinkIcon,
      AppImages.instagramSuperLinkIcon,
      AppImages.linkedinSuperLinkIcon,
      AppImages.snapchatSuperLinkIcon,
      AppImages.whatsappSuperLinkIcon,
      AppImages.xSuperLinkIcon,
      AppImages.youtubeLinkIcon,
      AppImages.chainLinkIcon,
    ];

    // Check the domain and return the corresponding icon
    if (url.contains("amazon.com")) {
      return superLinkIcons[0];
    } else if (url.contains("facebook.com")) {
      return superLinkIcons[1];
    } else if (url.contains("flipkart.com")) {
      return superLinkIcons[2];
    } else if (url.contains("instagram.com")) {
      return superLinkIcons[3];
    } else if (url.contains("linkedin.com")) {
      return superLinkIcons[4];
    } else if (url.contains("snapchat.com")) {
      return superLinkIcons[5];
    } else if (url.contains("whatsapp.com")) {
      return superLinkIcons[6];
    } else if (url.contains("twitter.com") || url.contains("x.com")) {
      return superLinkIcons[7];
    } else if (url.contains("youtube.com")) {
      return superLinkIcons[8];
    }

    // Return a default or placeholder icon if the domain does not match
    return AppImages.chainLinkIcon;
  }

  //endregion

  //region Open app web view
  void opeAppWebView(
      {required String webUrl, required BuildContext context}) async {
    launch(webUrl);
  }

//endregion
}
