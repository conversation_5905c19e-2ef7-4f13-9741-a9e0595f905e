import 'dart:async';

import 'package:flutter/material.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_all_orders_bloc.dart';
import 'package:swadesic/model/filters/all_orders_filter/all_orders_filter.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/services/seller_all_order_service/seller_all_order_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class AllOrderFilterBloc {
  //Common Variable
  BuildContext context;
  final SellerAllOrdersBloc sellerAllOrdersBloc;
  late AllOrdersFilter allOrdersFilter = AllOrdersFilter();
  late SellerAllOrderServices sellerAllOrderServices;

  // endregion

  //region Text ctrl
  final searchTextCtrl = TextEditingController();
  //endregion

  //region Controller
  final filterRefreshCtrl = StreamController<bool>.broadcast();
  Timer? _searchDebounceTimer;

  //endregion

  // region | Constructor |
  AllOrderFilterBloc(this.context, this.sellerAllOrdersBloc);

  // endregion

  // region Init
  void init() {
    sellerAllOrderServices = SellerAllOrderServices();
  }
  // endregion

  //region On Select root filter
  void onSelectRootFilter({required OrderFilterButton orderFilterButton}) {
    //Reset
    allOrdersFilter.resetAll();
    //Make selected root filter to true
    orderFilterButton.isSelected = true;
    //Refresh
    filterRefreshCtrl.sink.add(true);
  }
  //endregion

  //region On Select Child filter
  void onSelectChildFilter({required OrderFilterButton orderFilterButton}) {
    //Reset
    //allOrdersFilter.resetAll();
    //Make selected root filter to true
    orderFilterButton.isSelected = !orderFilterButton.isSelected;
    //Refresh
    filterRefreshCtrl.sink.add(true);
  }
  //endregion

  //region On Search
  /*
  This can search order number, Sub order number and product name using API
   */
  void onSearch({required String value}) {
    // Cancel previous timer if exists
    _searchDebounceTimer?.cancel();

    // If search value is empty, reload original orders immediately
    if (value.trim().isEmpty) {
      sellerAllOrdersBloc.init();
      return;
    }

    // Set up debounced search
    _searchDebounceTimer = Timer(const Duration(milliseconds: 500), () {
      _performSearch(value.trim());
    });
  }

  Future<void> _performSearch(String searchQuery) async {
    try {
      // Set loading state
      sellerAllOrdersBloc.sellerAllOrderCtrl.sink
          .add(SellerAllOrderState.Loading);

      // Clear current search results
      sellerAllOrdersBloc.searchedOrders.clear();

      // Get store reference from app data
      String storeReference = AppConstants.appData.storeReference ?? "";

      // Call search API
      GetOrderResponse searchResponse =
          await sellerAllOrderServices.searchStoreOrders(
        storeReference: storeReference,
        searchQuery: searchQuery,
        limit: 50, // Get more results for search
        offset: 0,
      );

      // Update searched orders with API results
      if (searchResponse.orderList != null &&
          searchResponse.orderList!.isNotEmpty) {
        sellerAllOrdersBloc.searchedOrders.addAll(searchResponse.orderList!);
        sellerAllOrdersBloc.sellerAllOrderCtrl.sink
            .add(SellerAllOrderState.Success);
      } else {
        sellerAllOrdersBloc.sellerAllOrderCtrl.sink
            .add(SellerAllOrderState.Empty);
      }
    } on ApiErrorResponseMessage {
      sellerAllOrdersBloc.sellerAllOrderCtrl.sink
          .add(SellerAllOrderState.Failed);
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
            commonErrorMessage: true);
      }
    } catch (error) {
      sellerAllOrdersBloc.sellerAllOrderCtrl.sink
          .add(SellerAllOrderState.Failed);
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
            commonErrorMessage: true);
      }
    }
  }
  //endregion

//region Dispose
  void dispose() {
    _searchDebounceTimer?.cancel();
    filterRefreshCtrl.close();
  }
//endregion
}
