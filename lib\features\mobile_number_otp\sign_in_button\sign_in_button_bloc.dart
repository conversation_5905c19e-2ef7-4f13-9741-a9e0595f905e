import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/mobile_number_otp/login_and_signup_with_google.dart';
import 'package:swadesic/features/mobile_number_otp/login_otp/login_otp.dart';
import 'package:swadesic/features/mobile_number_otp/mobile_number_otp_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/login_response/email_sign_in_sign_up_response.dart';
import 'package:swadesic/model/login_response/login_token_info.dart';
import 'package:swadesic/model/login_response/mobile_number_response.dart';
import 'package:swadesic/services/signin_services/signin_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class SignInButtonBloc {
  //region Common variable
  final BuildContext context;
  final MobileNumberOtpBloc mobileNumberOtpBloc;
  final bool isRegisterUser;

  //endregion

  //region Controller
  final isButtonLoadingCtrl = StreamController<bool>.broadcast();
  //endregion

  //region Constructor
  SignInButtonBloc(this.context, this.mobileNumberOtpBloc, this.isRegisterUser);
  //endregion

  //region Init
  void init() {}

  //endregion

  //region Email login
  void emailLogin() async {
    try {
      //Loading
      context.mounted ? isButtonLoadingCtrl.sink.add(true) : null;
      //Body
      Map<String, dynamic> body = {
        "phonenumber": "+91${mobileNumberOtpBloc.mobileNumberTextCtrl.text}",
        // "email": mobileNumberOtpBloc.mobileNumberResponse.cachingToken
      };
      //Get access token and otp field visible status
      EmailSignInSignUpResponse emailSignInSignUpResponse =
          await SignInServices().emailLogin(body: body).then((value) {
        //Push to otp screen
        goToLogInOtpScreen(
            // email: mobileNumberOtpBloc.mobileNumberResponse.cachingToken!,
            phoneNumber: "+91${mobileNumberOtpBloc.mobileNumberTextCtrl.text}",
            isEmailOtp: value.sentEmailOtp!,
            isPhoneOtp: value.sentPhonenumberOtp!);
        //Loading stop
        context.mounted ? isButtonLoadingCtrl.sink.add(false) : null;
        //Return value
        return value;
      });
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message!, context,
              toastShowTimer: 5)
          : null;
      //Loading stop
      context.mounted ? isButtonLoadingCtrl.sink.add(false) : null;
    } catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
      //Loading stop
      context.mounted ? isButtonLoadingCtrl.sink.add(false) : null;
    }
  }

//endregion

  //region Go to login otp screen
  void goToLogInOtpScreen(
      {required String phoneNumber,
      required bool isEmailOtp,
      required bool isPhoneOtp}) {
    var screen = LoginOtpScreen(
      isRegisterUser: isRegisterUser,
      phoneNumber: phoneNumber,
      isEmailOtp: isEmailOtp,
      isPhoneOtp: isPhoneOtp,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion

  ///Google login
//region Google sign in
  void googleSignIn() async {
    //Open google sign in request
    await LoginAndSignupWithGoogle(
            context,
            "+91${mobileNumberOtpBloc.mobileNumberTextCtrl.text}",
            mobileNumberOtpBloc,
            isButtonLoadingCtrl,
            isRegisterUser)
        .openGoogleLoginDialog();
  }
//endregion

//region Dispose
  void dispose() {
    isButtonLoadingCtrl.close();
  }
//endregion
}
