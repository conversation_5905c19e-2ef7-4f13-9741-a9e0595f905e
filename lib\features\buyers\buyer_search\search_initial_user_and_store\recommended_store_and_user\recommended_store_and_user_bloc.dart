import 'dart:async';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/bottom_navigations/store_bottom_navigation/store_bottom_navigation_bloc.dart';
import 'package:swadesic/features/buyers/buyer_home/buyer_home_bloc.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_screen.dart';
import 'package:swadesic/features/buyers/buyer_search/search_initial_user_and_store/recommended_store_and_user/recommended_store_and_user_pagination.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_product_comment/buyer_product_comment_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_all_product/buyer_view_product/buyer_view_single_product/buyer_view_single_product_screen.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/all_store_notification.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/user_or_store_notification.dart';
import 'package:swadesic/features/data_model/recommended_store_and_user_data_model/recommended_sore_and_user_data_model.dart';
import 'package:swadesic/features/seller/seller_all_orders/seller_sub_order/seller_sub_order_screen.dart';
import 'package:swadesic/features/support/issue_suggestion_dialog/issue_suggestion_dialog.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/recommended_store_and_user/recommended_store_and_user.dart';
import 'package:swadesic/services/find_your_customers_services/find_your_customers_services.dart';
import 'package:swadesic/services/follow_support_services/follow_support_service.dart';
import 'package:swadesic/services/notification_services/notification_service.dart';
import 'package:swadesic/services/recommended_store_and_user_service/recommended_store_and_user_service.dart';
import 'package:swadesic/services/store_and_user_reference_services/store_and_user_reference_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum RecommendedStoreAndUserState { Loading, Success, Failed, Empty }

class RecommendedStoreAndUserBloc {
  // region Common Variables
  BuildContext context;
  final bool isRecommendedStore;
  List<UserAndStoreInfo> recommendedStoreAndUserList = [];
  late RecommendedStoreAndUserPagination recommendedStoreAndUserPagination;
  // endregion

  //region Controller
  final recommendedStoreAndUserCtrl =
      StreamController<RecommendedStoreAndUserState>.broadcast();
  final ScrollController scrollController;

  //endregion

  // region | Constructor |
  RecommendedStoreAndUserBloc(
      this.context, this.isRecommendedStore, this.scrollController);

  // endregion

  // region Init
  init() async {
    recommendedStoreAndUserPagination =
        RecommendedStoreAndUserPagination(context, this, isRecommendedStore);
    await getRecommendedStoreAndUser();
  }

// endregion

  //region Get recommended store and user
  Future<void> getRecommendedStoreAndUser() async {
    // Get reference to the RecommendedStoreAndUserDataModel
    var recommendedStoreAndUserDataModel =
        Provider.of<RecommendedStoreAndUserDataModel>(context, listen: false);

    //region Try
    try {
      //Api call
      recommendedStoreAndUserList = await RecommendedStoreAndUserServices()
          .getRecommendedStoreAndUser(
              isRecommendedStore: isRecommendedStore, limit: 30, offset: 0);
      //Clear store in data model
      isRecommendedStore
          ? recommendedStoreAndUserDataModel.clearAllStoreList()
          : recommendedStoreAndUserDataModel.clearAllUserList();
      //Add recommended store to data model
      recommendedStoreAndUserDataModel.addRecommendedStoreOrUser(
          storeList: recommendedStoreAndUserList);

      //Success
      recommendedStoreAndUserCtrl.sink
          .add(RecommendedStoreAndUserState.Success);
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message!, context)
          : null;
      //Success
      recommendedStoreAndUserCtrl.sink.add(RecommendedStoreAndUserState.Failed);
    } catch (error) {
      //Failed
      recommendedStoreAndUserCtrl.sink.add(RecommendedStoreAndUserState.Failed);
      // return context.mounted ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context, commonErrorMessage: true) : null;
    }
  }
//endregion

  //region On tap follow/un-follow
  // void onTapFollowUnFollow({required RecommendedStoreAndUser recommendedStoreAndUser})async{
  //  try{
  //    // Get reference to the RecommendedStoreAndUserDataModel
  //    var recommendedStoreAndUserDataModel = Provider.of<RecommendedStoreAndUserDataModel>(context, listen: false);
  //
  //    //Store
  //    if(recommendedStoreAndUser.entityType == EntityType.STORE.name){
  //      recommendedStoreAndUser.followStatus = await FollowSupportService().followAndUnFollowRegisterUserAndStore(
  //          referencesList: [recommendedStoreAndUser.reference!],
  //          reference: AppConstants.appData.isUserView!?AppConstants.appData.userReference!:AppConstants.appData.storeReference!,
  //          toFollow: recommendedStoreAndUser.followStatus == "Support"?true:false);
  //    }
  //    //User
  //    if(recommendedStoreAndUser.entityType == EntityType.USER.name){
  //      recommendedStoreAndUser.followStatus = await FollowSupportService().followAndUnFollowRegisterUserAndStore(
  //          referencesList: [recommendedStoreAndUser.reference!],
  //          reference: AppConstants.appData.isUserView!?AppConstants.appData.userReference!:AppConstants.appData.storeReference!,
  //          toFollow: recommendedStoreAndUser.followStatus == "Follow"?true:false);
  //    }
  //
  //    //Refresh ui
  //    recommendedStoreAndUserDataModel.refreshUi();
  //  }
  //  on ApiErrorResponseMessage catch (error) {
  //    context.mounted ? CommonMethods.toastMessage(error.message!, context) : null;
  //  } catch (error) {
  //    context.mounted ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context, commonErrorMessage: true) : null;
  //  }
  // }
  //endregion

  ///Single follow and support
  //region On tap Follow and support
  void onTapFollowAndSupport(
      {required UserAndStoreInfo recommendedStoreAndUser}) async {
    // Get reference to the RecommendedStoreAndUserDataModel
    var recommendedStoreAndUserDataModel =
        Provider.of<RecommendedStoreAndUserDataModel>(context, listen: false);

    try {
      //Api call to update follow and support status
      recommendedStoreAndUser.followStatus = await FindYourCustomersServices()
          .followAndUnFollow(reference: recommendedStoreAndUser.reference!);

      //Add and subtract as per the follow and support status
      if (recommendedStoreAndUser.followStatus!
              .toLowerCase()
              .contains(FollowEnum.SUPPORTING.name.toLowerCase()) ||
          recommendedStoreAndUser.followStatus!
              .toLowerCase()
              .contains(FollowEnum.FOLLOWING.name.toLowerCase())) {
        recommendedStoreAndUser.followersOrSupportersCount =
            recommendedStoreAndUser.followersOrSupportersCount! + 1;
      } else {
        recommendedStoreAndUser.followersOrSupportersCount =
            recommendedStoreAndUser.followersOrSupportersCount! > 0
                ? recommendedStoreAndUser.followersOrSupportersCount! - 1
                : 0;
      }
      //Refresh ui
      recommendedStoreAndUserDataModel.refreshUi();
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message!, context)
          : null;
    } catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
    }
  }
  //endregion

  //region On tap icon
  onTapIcon({required UserAndStoreInfo recommendedStoreAndUser}) {
    var screen = recommendedStoreAndUser.entityType == EntityType.STORE.name
        ? BuyerViewStoreScreen(
            storeReference: recommendedStoreAndUser.reference)
        : UserProfileScreen(
            userReference: recommendedStoreAndUser.reference!,
          );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

  //region Dispose
  void dispose() {
    recommendedStoreAndUserCtrl.close();
    scrollController.dispose();
  }

//endregion
}
