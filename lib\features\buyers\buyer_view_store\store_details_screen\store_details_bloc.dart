import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/common_buyer_seller_screen/follower_and_supportes/follower_and_supporters.dart';
import 'package:swadesic/features/common_buyer_seller_screen/messaging_and_requests/all_messages/message_detail/message_detail_screen.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/features/seller/seller_store/seller_store_profile/seller_store_profile_screen.dart';
import 'package:swadesic/features/seller/seller_store/seller_trust_center/seller_trust_center_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/buyer_search_response/store_list_response.dart';
import 'package:swadesic/model/follow_status_response/follow_ststus_response.dart';
import 'package:swadesic/model/store_info/store_info.dart';
import 'package:swadesic/services/find_your_customers_services/find_your_customers_services.dart';
import 'package:swadesic/services/store_follow_services/store_follow_services.dart';
import 'package:swadesic/services/store_visit_services/store_visit_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/features/buyers/buyer_view_store/store_reviews_screen/store_reviews_screen.dart';

class StoreDetailsBloc {
  // region Common Variables
  BuildContext context;
  final StoreInfo storeInfo;
  int maxStoreDescLine = 3;

  ///Store Follow Status
  late FollowStatusResponse followStatusResponse;
  // endregion

  //region Controller
  final followUnFollowStateCtrl = StreamController<AppState>.broadcast();
  final refreshCountCtrl = StreamController<bool>.broadcast();
  //endregion

  // region | Constructor |
  StoreDetailsBloc(this.context, this.storeInfo);
  // endregion

  // region Init
  void init() async {
    // checkFollowState();
  }
// endregion

  //Visit store and check follow state
  // Future<void> checkFollowState()async{
  //   try {
  //     //Loading
  //     followUnFollowStateCtrl.sink.add(AppState.Loading);
  //
  //     // refreshSupportStatus.sink.add(AppState.Loading);
  //     //Store visited
  //      StoreVisitService().visitNewStore(storeReference:storeInfo.storeReference!);
  //     //Check follow status
  //     storeInfo.followStatus = await StoreFollowServices().getFollowStatus(reference: storeInfo.storeReference!);
  //
  //     //Success
  //     followUnFollowStateCtrl.sink.add(AppState.Success);
  //     //True
  //     refreshCountCtrl.sink.add(true);
  //
  //   } on ApiErrorResponseMessage {
  //     followUnFollowStateCtrl.sink.add(AppState.Failed);
  //     //CommonMethods.toastMessage(AppStrings.error, context);
  //     return;
  //   } catch (error) {
  //     followUnFollowStateCtrl.sink.add(AppState.Failed);
  //     //CommonMethods.toastMessage(AppStrings.error, context);
  //     return;
  //   }
  // }
  //endregion

  //region On tap support support
  onTapSupportUnSupport() async {
    ///Access check
    // if (BuyerHomeBloc.userDetailsResponse.userDetail!.followStores! != "1") {
    //   return CommonMethods.toastMessage(AppStrings.noAccess, context);
    // }
    try {
      followStatusResponse = await StoreFollowServices()
          .supportUnSupportStore(storeReference: storeInfo.storeReference!);
      storeInfo.isSupported = followStatusResponse.following!;
      //If follow is true
      if (storeInfo.isSupported) {
        storeInfo.supports = storeInfo.supports! + 1;
      } else {
        storeInfo.supports = storeInfo.supports! - 1;
      }
      //Success
      followUnFollowStateCtrl.sink.add(AppState.Success);
      //True
      refreshCountCtrl.sink.add(true);
    } on ApiErrorResponseMessage {
      // storeDetailCtrl.sink.add(StoreDetailState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    } catch (error) {
      // storeDetailCtrl.sink.add(StoreDetailState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }
  //endregion

  ///Single follow and support
  //region On tap Follow and support
  void onTapFollowAndSupport() async {
    //If static user
    if (CommonMethods().isStaticUser()) {
      CommonMethods().goToSignUpFlow();
      return;
    }
    try {
      storeInfo.followStatus = await FindYourCustomersServices()
          .followAndUnFollow(reference: storeInfo.storeReference!);
      //If status is Following the increase by 1 else reduce by 1
      if (storeInfo.followStatus!.toLowerCase() ==
          FollowEnum.SUPPORTING.name.toLowerCase()) {
        storeInfo.supports = storeInfo.supports! + 1;
      } else {
        storeInfo.supports =
            storeInfo.supports! > 0 ? storeInfo.supports! - 1 : 0;
      }

      //refresh count
      refreshCountCtrl.sink.add(true);
      //Success
      followUnFollowStateCtrl.sink.add(AppState.Success);
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message!, context)
          : null;
    } catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
    }
  }
  //endregion

  //region On tap store description
  void onTapStoreDesc({required int maxLine}) {
    if (maxLine == 1000) {
      maxStoreDescLine = 3;
    } else {
      maxStoreDescLine = 1000;
    }
    //True
    refreshCountCtrl.sink.add(true);
  }
  //endregion

  //region On tap Edit store
  void onTapEditStore() {
    var screen =
        SellerStoreProfileScreen(storeReference: storeInfo.storeReference!);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      //If value is not null then update the store info and refresh ui
      if (value != null) {
        ///Add store info to data model if seller is viewing own store
        if (storeInfo.storeReference! == AppConstants.appData.storeReference!) {
          late SellerOwnStoreInfoDataModel sellerOwnStoreInfoDataModel =
              SellerOwnStoreInfoDataModel();
          sellerOwnStoreInfoDataModel =
              Provider.of<SellerOwnStoreInfoDataModel>(context, listen: false);
          sellerOwnStoreInfoDataModel.setStoreInfoResponse(value);
        }
        //Add store name, category, desc, icon, and cover image.
        storeInfo.storeName = value.storeName;
        storeInfo.categoryName = value.categoryName;
        storeInfo.businessDescription = value.businessDescription;
        storeInfo.icon = value.icon;
        storeInfo.coverImage = value.coverImage;
        //Refresh ui
        followUnFollowStateCtrl.sink.add(AppState.Success);
        //True
        refreshCountCtrl.sink.add(true);
      } else {
        return;
      }
    });
  }
  //endregion

  //region Go to Trust Center
  void goToTrustCenter({required storeReference}) {
    var screen = SellerTrustCenterScreen(
      storeRef: storeReference,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

  // region Go to Message
  void goToMessage() {
    var screen =
        MessageDetailScreen(toEntityReference: storeInfo.storeReference!);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route);
  }
  //endregion

  //region Go to follower and supporters
  void goToFollowerAndSupporters({required String storeReference}) {
    var screen = FollowerAndSupporters(
      reference: storeReference,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

  //region Go to follower and supporters
  void goToStoreReviews({required StoreInfo storeInfo}) {
    var screen = StoreReviewsScreen(storeInfo: storeInfo);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

//region Dispose
  void dispose() {
    imageCache.clear();
    followUnFollowStateCtrl.close();
    refreshCountCtrl.close();
  }
//endregion
}
