import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:swadesic/env.dart';
import 'package:swadesic/features/navigation/navigation_router.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/buyer_on_boarding/buyer_on_boarding_screen.dart';
import 'package:swadesic/features/mobile_number_otp/intro_slider/intro_slider_bottom_sheet.dart';
import 'package:swadesic/features/mobile_number_otp/login_otp/login_otp.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/login_response/email_sign_in_sign_up_response.dart';
import 'package:swadesic/model/login_response/mobile_number_response.dart';
import 'package:swadesic/model/login_response/sign_in_response.dart';
import 'package:swadesic/services/get_app_data_services/get_app_data_service.dart';
import 'package:swadesic/services/signin_services/email_signin_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/buyer_onboarding_new/initial_onboarding_screen.dart';

enum SignInOptionState { Loading, Success, Failed }

class MobileNumberOtpBloc {
  // region Common Methods
  BuildContext context;
  late MobileNumberResponse mobileNumberResponse;
  bool isEmailFieldVisible = false;
  bool showEmailField = false; // Controls whether to show email field
  SignInOptionState signInOptionsState = SignInOptionState.Failed;

  // endregion

  //region Controller

  final countDownCtrl = StreamController<bool>.broadcast();
  final otpFieldVisibleCtrl = StreamController<bool>.broadcast();
  final referralCtrl = StreamController<bool>.broadcast();
  final signInOptionsStateCtrl =
      StreamController<SignInOptionState>.broadcast();
  final visibleEmailAddressOptions = StreamController<bool>.broadcast();
  final showEmailFieldCtrl =
      StreamController<bool>.broadcast(); // Controls email field visibility
  final GlobalKey<FormState> emailFormKey = GlobalKey<FormState>();
  final KeyboardVisibilityController keyboardVisibilityController =
      KeyboardVisibilityController();
  final googleSignInLoadingCtrl = StreamController<bool>.broadcast();
  Stream<bool> get googleSignInLoading => googleSignInLoadingCtrl.stream;

  // Loading state for OTP button
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  void setLoading(bool value) {
    _isLoading = value;
  }

  //endregion

  //region Text Editing Controller
  TextEditingController mobileNumberTextCtrl = TextEditingController();
  TextEditingController emailAddressTextCtrl = TextEditingController();
  TextEditingController otpTextCtrl = TextEditingController();

  //endregion

  //region Focus node
  final FocusNode referralFocusNode = FocusNode();

  //endregion

  // region | Constructor |
  MobileNumberOtpBloc(this.context);

  // endregion

  // region Init
  void init() {
    // Initialize MobileNumberResponse
    mobileNumberResponse = MobileNumberResponse(
      userExists: false,
      userEmail: null,
    );

    // Initially hide email field
    showEmailField = false;
    showEmailFieldCtrl.sink.add(showEmailField);

    // Add listener to email field - but don't validate during typing
    // We'll validate only when the user explicitly submits the form
    // This prevents keyboard from closing prematurely
    emailAddressTextCtrl.addListener(() {
      // Don't perform validation during typing
      // This will be handled by the form's onEditingComplete
      googleSignInLoadingCtrl.sink.add(false);
    });
  }

  // endregion

  //region On change phone number

  //endregion

  ///1
  //region Sign in info
  void signInInfo() async {
    // Don't close keyboard here - let the user finish typing
    // CommonMethods.closeKeyboard(context);

    // Validate email
    if (!CommonMethods.validateEmail(emailAddressTextCtrl.text)) {
      // Don't show toast message during typing - it's disruptive
      // CommonMethods.toastMessage("Enter a valid email address", context);
      return;
    }

    try {
      //Loading
      signInOptionsState = SignInOptionState.Loading;
      signInOptionsStateCtrl.sink.add(signInOptionsState);

      //Get email status
      EmailSignInServices emailSignInServices = EmailSignInServices();
      mobileNumberResponse = await emailSignInServices.checkUserInfoByEmail(
          email: emailAddressTextCtrl.text.trim());

      //Success
      signInOptionsState = SignInOptionState.Success;
      signInOptionsStateCtrl.sink.add(signInOptionsState);
    } on ApiErrorResponseMessage catch (error) {
      //Failed
      signInOptionsState = SignInOptionState.Failed;
      signInOptionsStateCtrl.sink.add(signInOptionsState);
      if (context.mounted) {
        CommonMethods.toastMessage(error.message!, context);
      }
    } catch (error) {
      //Failed
      signInOptionsState = SignInOptionState.Failed;
      signInOptionsStateCtrl.sink.add(signInOptionsState);
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
            commonErrorMessage: true);
      }
    }
  }
  //endregion

  //region Google sign in
  void googleSignIn() async {
    try {
      // Show loading state
      signInOptionsState = SignInOptionState.Loading;
      googleSignInLoadingCtrl.sink.add(true);
      signInOptionsStateCtrl.sink.add(signInOptionsState);

      // Initialize Google Sign-In with appropriate client ID for web
      GoogleSignIn googleSignIn;

      if (kIsWeb) {
        // Use web-specific configuration
        googleSignIn = GoogleSignIn(
          scopes: ['email'],
          clientId: AppConstants.appCurrentEnvironment == Environment.prod
              ? AppConstants.prodGoogleClintId
              : AppConstants.devGoogleClintId,
        );
        debugPrint(
            "Using web client ID: ${kIsWeb ? (AppConstants.appCurrentEnvironment == Environment.prod ? AppConstants.prodGoogleClintId : AppConstants.devGoogleClintId) : 'Native client ID'}");
      } else {
        // Use mobile configuration
        googleSignIn = GoogleSignIn(
          scopes: ['email'],
        );
      }

      // Sign out any existing Google account
      await googleSignIn.signOut();

      // Attempt to sign in with Google
      debugPrint("Attempting to sign in with Google...");
      final GoogleSignInAccount? googleUser = await googleSignIn.signIn();

      // If sign-in successful
      if (googleUser != null) {
        debugPrint("Google sign-in successful for: ${googleUser.email}");

        // Get authentication token
        final GoogleSignInAuthentication googleAuth =
            await googleUser.authentication;
        debugPrint("Got Google authentication token");

        // Go to login OTP screen with Google authentication
        goToLogInOtpScreen(
          isGoogleLogin: true,
          email: googleUser.email,
          googleToken: googleAuth.accessToken ?? "",
          isEmailOtp: false,
          isPhoneOtp: false,
        );
      } else {
        // User cancelled Google sign-in
        debugPrint("Google sign-in cancelled by user");
        signInOptionsState = SignInOptionState.Failed;
        signInOptionsStateCtrl.sink.add(signInOptionsState);
        googleSignInLoadingCtrl.sink.add(false);
      }
    } catch (error) {
      // Handle errors
      debugPrint("Google sign-in error: $error");
      signInOptionsState = SignInOptionState.Failed;
      signInOptionsStateCtrl.sink.add(signInOptionsState);
      googleSignInLoadingCtrl.sink.add(false);

      if (context.mounted) {
        CommonMethods.toastMessage(
            "Failed to sign in with Google. Please try again.", context);
      }
    }
  }

  //endregion

  //region Go to login otp screen
  Future<void> goToLogInOtpScreen({
    bool isGoogleLogin = false,
    required String email,
    String googleToken = "",
    bool isEmailOtp = true,
    bool isPhoneOtp = false,
  }) async {
    // If using Google login, handle it directly
    if (isGoogleLogin) {
      try {
        // Show loading state
        signInOptionsState = SignInOptionState.Loading;
        signInOptionsStateCtrl.sink.add(signInOptionsState);

        // Create request body for Google login
        Map<String, dynamic> body = {
          "email": email,
          "access_token": googleToken,
        };

        // Log the request
        debugPrint("Sending Google login request with body: $body");

        // Call the API for Google login
        EmailSignInServices emailSignInServices = EmailSignInServices();
        try {
          // Attempt to sign in with Google token
          var signInResponse =
              await emailSignInServices.googleLoginRequest(body: body);
          debugPrint("Google login request successful");

          // If successful, navigate directly to the app home page
          if (context.mounted) {
            // Reset loading state
            signInOptionsState = SignInOptionState.Success;
            signInOptionsStateCtrl.sink.add(signInOptionsState);

            // Process the response directly
            if (!signInResponse.loginUserInfo!.profileComplete!) {
              // Save token info to global variable for temporary use
              AppConstants.appData.accessToken =
                  signInResponse.loginTokenInfo!.access;
              AppConstants.appData.accessTokenExpire =
                  signInResponse.loginTokenInfo!.accessTokenValidity;
              AppConstants.appData.refreshToken =
                  signInResponse.loginTokenInfo!.refresh;
              AppConstants.appData.refreshExpire =
                  signInResponse.loginTokenInfo!.refreshTokenValidity;

              // Go to initial onboarding
              var screen = InitialOnboardingScreen(
                userReference: signInResponse.loginUserInfo!.userReference!,
                icon: signInResponse.loginUserInfo!.icon,
              );
              var route = MaterialPageRoute(builder: (context) => screen);
              Navigator.push(context, route);
            } else {
              // Save the login info in cache
              saveUserInfoInCache(signInResponse: signInResponse);

              // Navigate directly to the home page
              navigateToUserBottomNavigation();
            }
          }
          return;
        } catch (error) {
          debugPrint("Google login error: $error");
          // If error occurs, fall back to OTP verification
          if (context.mounted) {
            CommonMethods.toastMessage(
                "Failed to authenticate with Google. Please try again.",
                context);
          }

          // Reset loading state
          signInOptionsState = SignInOptionState.Failed;
          signInOptionsStateCtrl.sink.add(signInOptionsState);
          return;
        }
      } catch (error) {
        // Handle error
        signInOptionsState = SignInOptionState.Failed;
        signInOptionsStateCtrl.sink.add(signInOptionsState);

        if (context.mounted) {
          CommonMethods.toastMessage(
              "Failed to authenticate with Google. Please try again.", context);
        }
        return;
      }
    }

    // If using email OTP, send the OTP first
    if (isEmailOtp && !isGoogleLogin) {
      try {
        // Show loading state
        signInOptionsState = SignInOptionState.Loading;
        signInOptionsStateCtrl.sink.add(signInOptionsState);

        // Create request body for sending OTP
        Map<String, dynamic> body = {"email": email};

        // Log the request
        debugPrint("Sending email OTP request with body: $body");

        // Call the API to send OTP
        EmailSignInServices emailSignInServices = EmailSignInServices();
        debugPrint("Calling emailLogin API...");
        EmailSignInSignUpResponse response =
            await emailSignInServices.emailLogin(body: body);

        // Log the response
        debugPrint("Email OTP response received: ${response.toString()}");

        // Check if OTP was sent successfully
        if (response.sentEmailOtp != true) {
          if (context.mounted) {
            CommonMethods.toastMessage(
                "Failed to send OTP. Please try again.", context);
          }

          // Reset loading state
          signInOptionsState = SignInOptionState.Failed;
          signInOptionsStateCtrl.sink.add(signInOptionsState);
          return;
        }

        // Reset loading state
        signInOptionsState = SignInOptionState.Success;
        signInOptionsStateCtrl.sink.add(signInOptionsState);

        // Show success message
        if (context.mounted) {
          CommonMethods.toastMessage("OTP sent to your email", context);
        }
      } catch (error) {
        // Handle error
        signInOptionsState = SignInOptionState.Failed;
        signInOptionsStateCtrl.sink.add(signInOptionsState);

        if (context.mounted) {
          CommonMethods.toastMessage(
              "Failed to send OTP. Please try again.", context);
        }
        return;
      }
    }

    // Store a reference to the context to avoid using it across async gaps
    final currentContext = context;

    // Only proceed if the context is still valid
    if (currentContext.mounted) {
      // Navigate to OTP verification screen
      var screen = LoginOtpScreen(
        email: email,
        phoneNumber: "", // No longer needed for email authentication
        isEmailOtp: isEmailOtp, // Using email OTP by default
        isPhoneOtp: isPhoneOtp, // Not using phone OTP by default
        googleAccessToken: isGoogleLogin
            ? googleToken
            : null, // Add Google token if using Google login
        isRegisterUser:
            mobileNumberResponse.userExists ?? false, // Check if user exists
      );
      var route = MaterialPageRoute(builder: (context) => screen);
      Navigator.push(currentContext, route);
    }
  }

  //endregion

  // region Go to intro slider screen
  void openIntroSliderScreen() async {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const IntroSliderBottomSheet(),
    );
  }
  // endregion

  // region Save user info in cache
  void saveUserInfoInCache({required SignInResponse signInResponse}) async {
    // Add access token
    AppConstants.appData.accessToken = signInResponse.loginTokenInfo!.access;
    // Add access token expire
    AppConstants.appData.accessTokenExpire =
        signInResponse.loginTokenInfo!.accessTokenValidity;
    // Add refresh token
    AppConstants.appData.refreshToken = signInResponse.loginTokenInfo!.refresh;
    // Add refresh token expire
    AppConstants.appData.refreshExpire =
        signInResponse.loginTokenInfo!.refreshTokenValidity;
    // Add user id
    AppConstants.appData.userId = signInResponse.loginUserInfo!.userid!;
    // Add user reference
    AppConstants.appData.userReference =
        signInResponse.loginUserInfo!.userReference!;
    // Mark user view is true
    AppConstants.appData.isUserView = true;
    // Mark store view is false
    AppConstants.appData.isStoreView = false;
    // Pin code
    AppConstants.appData.pinCode = signInResponse.loginUserInfo!.pinCode;
    // Add all data to share pref
    await AppDataService().addAppData();
  }
  // endregion

  // region Navigate to user bottom navigation
  void navigateToUserBottomNavigation() {
    if (context.mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => const NavigationRouter()),
            (Route<dynamic> route) => false);
      });
    }
  }
  // endregion

//region Dispose
  void dispose() {
    countDownCtrl.close();
    otpFieldVisibleCtrl.close();
    referralCtrl.close();
    signInOptionsStateCtrl.close();
    visibleEmailAddressOptions.close();
    showEmailFieldCtrl.close();
    googleSignInLoadingCtrl.close(); // Close Google loading controller
  }
//endregion
}
