import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/common_referral_page/common_referral_page.dart';
import 'package:swadesic/features/user_profile/user_profile_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/recommended_store_and_user/recommended_store_and_user.dart';
import 'package:swadesic/model/user_rewards_and_invitees_response/user_invitees_count.dart';
import 'package:swadesic/model/user_rewards_and_invitees_response/user_rewards.dart';
import 'package:swadesic/services/find_your_customers_services/find_your_customers_services.dart';
import 'package:swadesic/services/user_rewards_and_invitees_service/user_reward_and_invitees_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum YourInviteesState { Loading, Success, Failed }

class YourInviteesBloc {
  // region Common Variables
  BuildContext context;
  late InviteeCounts inviteeCounts;
  String inviteCode = "";
  List<UserAndStoreInfo> invitedUsers = [];
  List<UserAndStoreInfo> invitedStores = [];
  // endregion

  //region Controller
  final inviteeCountsCtrl = StreamController<YourInviteesState>.broadcast();
  final yourInviteesCtrl = StreamController<YourInviteesState>.broadcast();
  //endregion

  // region | Constructor |
  YourInviteesBloc(this.context);
  // endregion

  // region Init
  void init() {
    getInviteeCounts();
  }
// endregion

  //region Get invitee counts
  Future<void> getInviteeCounts() async {
    //region Try
    try {
      //Api call
      //Get referral code
      inviteCode = await UserRewardAndInviteesService().getInviteCode();

      //Get invitee counts
      inviteeCounts = await UserRewardAndInviteesService()
          .getInviteeCount(inviteCode: inviteCode);
      //Success
      inviteeCountsCtrl.sink.add(YourInviteesState.Success);
      //Get invited user and store
      getInviteesUserAndStore(inviteCode: inviteCode);
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message!, context)
          : null;
      //Failed
      inviteeCountsCtrl.sink.add(YourInviteesState.Failed);
    } catch (error) {
      //Failed
      inviteeCountsCtrl.sink.add(YourInviteesState.Failed);
    }
  }
  //endregion

  //region Get invitees user and stores
  Future<void> getInviteesUserAndStore({required String inviteCode}) async {
    //region Try
    try {
      //Invited users
      invitedUsers = await UserRewardAndInviteesService()
          .getInvitedUserAndStore(
              inviteCode: inviteCode,
              isInvitedUsers: true,
              limit: 100,
              offset: 0);

      //Invited stores
      invitedStores = await UserRewardAndInviteesService()
          .getInvitedUserAndStore(
              inviteCode: inviteCode,
              isInvitedUsers: false,
              limit: 100,
              offset: 0);

      //Success
      yourInviteesCtrl.sink.add(YourInviteesState.Success);
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message!, context)
          : null;
      //Failed
      yourInviteesCtrl.sink.add(YourInviteesState.Failed);
    } catch (error) {
      //Failed
      yourInviteesCtrl.sink.add(YourInviteesState.Failed);
    }
  }
//endregion

  //region On tap icon
  onTapIcon({required UserAndStoreInfo invitedUsersOrStore}) {
    var screen = invitedUsersOrStore.entityType == EntityType.STORE.name
        ? BuyerViewStoreScreen(storeReference: invitedUsersOrStore.reference)
        : UserProfileScreen(
            userReference: invitedUsersOrStore.reference!,
          );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

  ///Single follow and support
  //region On tap Follow and support
  void onTapFollowAndSupport(
      {required UserAndStoreInfo invitedUserAndStore}) async {
    try {
      //Api call to update follow and support status
      invitedUserAndStore.followStatus = await FindYourCustomersServices()
          .followAndUnFollow(reference: invitedUserAndStore.reference!);

      //Add and subtract as per the follow and support status
      if (invitedUserAndStore.followStatus!
              .toLowerCase()
              .contains(FollowEnum.SUPPORTING.name.toLowerCase()) ||
          invitedUserAndStore.followStatus!
              .toLowerCase()
              .contains(FollowEnum.FOLLOWING.name.toLowerCase())) {
        invitedUserAndStore.followersOrSupportersCount =
            invitedUserAndStore.followersOrSupportersCount! + 1;
      } else {
        invitedUserAndStore.followersOrSupportersCount =
            invitedUserAndStore.followersOrSupportersCount! > 0
                ? invitedUserAndStore.followersOrSupportersCount! - 1
                : 0;
      }
      //Success
      yourInviteesCtrl.sink.add(YourInviteesState.Success);
    } on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message!, context)
          : null;
    } catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
    }
  }
  //endregion

  //region Go To common referral
  void goToCommonReferral() {
    var screen = CommonReferralPage();
    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route);
  }
  //endregion

//region Dispose
  void dispose() {
    inviteeCountsCtrl.close();
    yourInviteesCtrl.close();
  }
//endregion
}
