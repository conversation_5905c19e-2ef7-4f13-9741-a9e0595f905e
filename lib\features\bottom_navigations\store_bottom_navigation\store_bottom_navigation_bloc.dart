import 'dart:async';

import 'package:app_settings/app_settings.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/buyers/buyer_home/update_pincode/update_pincode.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_screen.dart';
import 'package:swadesic/features/buyers/supported_stores/supported_stores_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/create_store_options/create_store_options.dart';
import 'package:swadesic/features/common_buyer_seller_screen/open_app_settings/open_app_settings.dart';
import 'package:swadesic/features/data_model/app_config_data_model/app_config_data_model.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/all_store_notification.dart';
import 'package:swadesic/features/data_model/notification_data_mdel/user_or_store_notification.dart';
import 'package:swadesic/features/data_model/seller_own_store_info_data_model/seller_own_store_info_data_model.dart';
import 'package:swadesic/features/data_model/shopping_cart_quantity_data_model/shopping_cart_quantity_data_model.dart';
import 'package:swadesic/features/data_model/store_dashboard_data_model/store_dashboard_data_model.dart';
import 'package:swadesic/features/data_model/user_created_stores/user_created_stores.dart';
import 'package:swadesic/features/post/add_post/add_post_screen.dart';
import 'package:swadesic/features/seller/add_post_and_product/add_post_and_product.dart';
import 'package:swadesic/features/seller/seller_store/store_dashboard/wating_orders_dialog/waiting_orders_dialog.dart';
import 'package:swadesic/features/widgets/app_animated_dialog/app_animated_dialog.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/app_config_response/app_config_response.dart';
import 'package:swadesic/model/buyer_search_response/store_list_response.dart';
import 'package:swadesic/model/notification_response/notification_response.dart';
import 'package:swadesic/model/shopping_cart_responses/cart_details_response.dart';
import 'package:swadesic/model/shopping_cart_responses/cart_items_responses.dart';
import 'package:swadesic/model/single_store_info_response/single_store_info_response.dart';
import 'package:swadesic/model/store_dashboard_response/store_dashboard_response.dart';
import 'package:swadesic/model/user_details_response/user_details_response.dart';
import 'package:swadesic/services/app_config_services/app_config_service.dart';
import 'package:swadesic/services/app_token_service/app_token_service.dart';
import 'package:swadesic/services/cloud_messaging/cloud_messaging.dart';
import 'package:swadesic/services/get_app_data_services/get_app_data_service.dart';
import 'package:swadesic/services/notification_services/notification_service.dart';
import 'package:swadesic/services/seller_home_service/seller_home_service.dart';
import 'package:swadesic/services/shopping_cart_service/shopping_cart_service.dart';
import 'package:swadesic/services/store_dashboard_services/store_dashboard_service.dart';
import 'package:swadesic/services/store_info_service/store_info_service.dart';
import 'package:swadesic/services/user_detail_services/user_detail_services.dart';
import 'package:swadesic/services/user_profile/user_profile_service.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/services/app_link_services/handle_url.dart';



class StoreBottomNavigationBloc {
  // region Common Variables
  BuildContext context;
  static late StoreListResponse storeListResponse;
  late SingleStoreInfoResponse singleStoreInfoResponse = SingleStoreInfoResponse();
  ///Seller own store info data model
  late SellerOwnStoreInfoDataModel sellerOwnStoreInfoDataModel;

  bool isAddPostAndProductOpened = false;
  // endregion

  //region Controller
  final screenRefreshCtrl = StreamController<bool>.broadcast();
  //endregion

  //region Text Controller
  //endregion

  // region | Constructor |
  StoreBottomNavigationBloc(this.context);
  // endregion


  // region Init
  init() async {
    //Check app update
    CommonMethods.appUpdateCheck();
    //Start timer to compare toke expiration time
    AppTokenService().compareExpireTime();
    //Cloud messaging initialize
    await CloudMessaging().initMessaging();
    //For ground
    getForGroundNotification();
    //Get uer or store notification
    getUserOrStoreNotification();
    //Get all store notification
    getAllStoreNotification();
    //Get all store created by user
    // getStoreListCreatedByUser();
    getStoreListCreatedByUser();
    //Get logged in store
    getSingleStoreInfo();
    //Get dash board
    getStoreDashboard();
    //Get logged in user detail
    getLoggedInUserDetail();
    // HandleUrl is now handled centrally in App.dart to avoid multiple instances
    AppConstants.storePersistentTabController.addListener(() {

      //print("Tapped same or other ");

    });
    //Get app config
    await getAppConfig();
  }
// endregion


  //region Get for ground notification
  void getForGroundNotification(){
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      //If context is mounted
      if(context.mounted){
        //Get user or store notification
        getUserOrStoreNotification();
        //Get all store notification
        getAllStoreNotification();
      }
    });
  }
  //endregion


//region Get User or store Notifications
  Future<void>getUserOrStoreNotification() async {
    //region Try
    try {
      // _showNotification();
      //Api call
      GetNotificationResponse getNotificationResponse = await NotificationService().getUserOrStoreNotification();
      //Add notification data to data model
      UserOrStoreNotificationDataModel notificationDataModel =  Provider.of<UserOrStoreNotificationDataModel>(context, listen: false);
      notificationDataModel.addAllStoreNotification(notificationData: getNotificationResponse);
    }
    on ApiErrorResponseMessage catch (error) {
      //print(error.message);

      // context.mounted?CommonMethods.toastMessage(error.message!, context):null;
    } catch (error) {
      //print(error);

      // return context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
    }
  }
  //endregion

  //region Get All store Notifications
  Future<void>getAllStoreNotification() async {
    //region Try
    try {
      //Api call
      GetNotificationResponse getNotificationResponse = await NotificationService().getAllStoreNotification();
      //Add notification data to data model
      AllStoreNotificationDataModel allStoreNotification =  Provider.of<AllStoreNotificationDataModel>(context, listen: false);
      allStoreNotification.addAllStoreNotification(notificationData: getNotificationResponse);
    }
    on ApiErrorResponseMessage catch (error) {
      //print(error.message);

      // context.mounted?CommonMethods.toastMessage(error.message!, context):null;
    } catch (error) {
      //print(error);

      // return context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
    }
  }
  //endregion

  //region Get StoreList created by user
  void getStoreListCreatedByUser()async{
    try{
      storeListResponse = await SellerHomeService().getSellerStore();
    }
    on ApiErrorResponseMessage catch(e) {
      //print(e.message);

      // CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
    catch(error){
      //print(error);
      // CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }
//endregion


  ///Get store info
  //region Get Store Info Api call
  getSingleStoreInfo() async {

    try {
      // storeDetailCtrl.sink.add(AppState.Loading);
      ///Get store detail
      singleStoreInfoResponse = await SingleStoreInfoServices().getSingleStoreInfo(AppConstants.appData.storeReference!);

      //Seller admin data model
      sellerOwnStoreInfoDataModel = Provider.of<SellerOwnStoreInfoDataModel>(context, listen: false);

      //Add data in data model
      sellerOwnStoreInfoDataModel.setStoreInfoResponse(singleStoreInfoResponse.data!);


    } catch (error) {
      //print(error);

      // context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
      return;
    }
  }
//endregion

  ///Get store dashboard
  //region Get store dashboard
  getStoreDashboard()async{
    try{
      /// Retrieve to the StoreDashboardDataModel
      StoreDashboardDataModel storeDashboardDataModel = Provider.of<StoreDashboardDataModel>(context, listen: false);
      //Api call
      StoreDashboardResponse storeDashboardResponse = await StoreDashboardService().getStoreDashboard(storeReference:AppConstants.appData.storeReference!);

      //Open if order alert message is not null
      if (storeDashboardResponse.data!.orderAlertMessage != null) {
        openWaitingOrderDialog();
      }

      ///Set data into data model
      storeDashboardDataModel.addDashboard(data: storeDashboardResponse.data!);
    }
    on ApiErrorResponseMessage catch (error){
      debugPrint(error.message);
      // CommonMethods.toastMessage(error.message.toString(), context);
      // return;
    }
    catch(error){
      debugPrint(error.toString());
      // CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      // return;
    }
  }
  //endregion

  //region Open waiting order dialog
  Future<void> openWaitingOrderDialog(){
    return  showDialog(

      context: context,

      builder: (_) => AppAnimatedDialog(child: WaitingOrdersDialog()),
    );
  }
  //endregion

  //region Get StoreList created by user
  // void getStoreListCreatedByUser()async{
  //   try{
  //     StoreListResponse storeListResponse = await SellerHomeService().getSellerStore();
  //     //Add data to data model
  //     UserCreatedStoresDataModel userCreatedStoresDataModel =  Provider.of<UserCreatedStoresDataModel>(context, listen: false);
  //     userCreatedStoresDataModel.addStoreList(storeListResponse: storeListResponse);
  //   }
  //   on ApiErrorResponseMessage catch (error) {
  //     context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
  //   }
  //   catch(error){
  //     context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
  //   }
  // }
//endregion


  Future<void> _showNotification() async {
    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

    // initialise the plugin. app_icon needs to be a added as a drawable resource to the Android head project
    const AndroidInitializationSettings initializationSettingsAndroid =
    AndroidInitializationSettings('@mipmap/ic_launcher');
    final DarwinInitializationSettings initializationSettingsDarwin =
    DarwinInitializationSettings(
      onDidReceiveLocalNotification: (id, title, body, payload) => null,);
    final LinuxInitializationSettings initializationSettingsLinux =
    LinuxInitializationSettings(
        defaultActionName: 'Open notification');
    final InitializationSettings initializationSettings = InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsDarwin,
        linux: initializationSettingsLinux);
    flutterLocalNotificationsPlugin.initialize(initializationSettings,
      onDidReceiveNotificationResponse: (details) => null,);





    const AndroidNotificationDetails androidNotificationDetails =
    AndroidNotificationDetails('your channel id', 'your channel name',
        channelDescription: 'your channel description',
        importance: Importance.max,
        priority: Priority.high,
        ongoing: true,
        ticker: 'ticker');
    const NotificationDetails notificationDetails =
    NotificationDetails(android: androidNotificationDetails);
    await flutterLocalNotificationsPlugin.show(
        0, 'plain title', 'plain body', notificationDetails,
        payload: 'item x');

  }


  //region Go to add post
  void goToAddPost(){
    Widget screen= AddPostScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route);
  }
  //endregion



//region Add post and product bottom sheet
  Future<void> addPostAndProductBottomSheet({
    required BuildContext context,
    BuildContext? tabContext,
  }) async {
    if(!isAddPostAndProductOpened) {
      //Toggle the is bottom sheet opened
      isAddPostAndProductOpened = true;
      await CommonMethods.appMinimumBottomSheets(
        context: context,
        screen: AddPostAndProduct(tabContext: tabContext),
      ).then((value) {
        isAddPostAndProductOpened = value!;
      });
      return;
    } else {
      Navigator.pop(context);
    }
  }
//endregion



  //region App config
  Future<void>getAppConfig()async{
    //Get app config reference from data model
    AppConfigDataModel appConfigDataModel =  Provider.of<AppConfigDataModel>(context, listen: false);

    try{
      AppConfigResponse appConfigResponse = await AppConfigService().getAppConfig();
      //Add data to data model
      appConfigDataModel.addAppConfig(data: appConfigResponse.appConfig!);

      //print("Worked post limit ${appConfigResponse.appConfig!.postImageLimit}");
    }
    on ApiErrorResponseMessage catch (error) {
      // context.mounted?CommonMethods.toastMessage(error.message.toString(), context):null;
    }
    catch(error){
      // context.mounted?CommonMethods.toastMessage(AppStrings.commonErrorMessage, context):null;
    }
  }
  //endregion


  //region Get logged in user detail
  Future <void> getLoggedInUserDetail() async {
    //Get logged in user info data model
    late LoggedInUserInfoDataModel loggedInUserInfoDataModel = Provider.of<LoggedInUserInfoDataModel>(context, listen: false);

    //region Try
    try {
      //selectedAddressRefreshCtrl.sink.add(SellerReturnWarrantyState.Loading);
      GetUserDetailsResponse userDetailsResponse = await UserDetailsServices().getLoggedInUserDetail(userReference: AppConstants.appData.userReference!);

      ///Add user info to logged in user data model
      loggedInUserInfoDataModel.setUserInfoResponse(data: userDetailsResponse.userDetail!);
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      CommonMethods.toastMessage(error.message!, context);
      return;
    } catch (error) {
      //print(error);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      return;
    }
  }
  //endregion

//region Dispose
  void dispose() {
    imageCache.clear();

  }
//endregion

}
