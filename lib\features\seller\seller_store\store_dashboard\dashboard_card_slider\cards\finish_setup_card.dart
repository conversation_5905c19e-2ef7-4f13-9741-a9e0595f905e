import 'package:flutter/material.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:swadesic/model/store_dashboard_response/store_dashboard_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class FinishSetupCard {
  static Widget buildTitle() {
    return Text(
      "Finish Setup & Start Sharing",
      style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
    );
  }

  static Widget buildContent(StoreDashBoard storeDashboard) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Text(
            "Complete your store so people with your StoreLink can see it instantly.",
            style: AppTextStyle.smallTextRegular(textColor: AppColors.appBlack)
                .copyWith(
              fontSize: 12,
            ),
          ),
        ),
        // Progress indicator
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15),
          child: CircularPercentIndicator(
            radius: 20.0,
            lineWidth: 5.0,
            percent: storeDashboard.dashboardProgress / 100,
            backgroundColor: AppColors.brandBlack.withOpacity(0.1),
            center: appText(
              "${storeDashboard.dashboardProgress.round()}%",
              color: AppColors.appBlack,
              maxLine: 1,
              fontFamily: AppConstants.rRegular,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
            progressColor: AppColors.brandBlack,
          ),
        ),
      ],
    );
  }
}
