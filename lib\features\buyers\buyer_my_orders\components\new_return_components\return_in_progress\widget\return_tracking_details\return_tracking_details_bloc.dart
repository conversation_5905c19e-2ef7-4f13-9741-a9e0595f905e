import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:swadesic/features/common_buyer_seller_screen/search/search_screen.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/logistics_partner_response/logistics_partner_response.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/model/seller_all_order_response/return_tracking_detail_ressponse.dart';
import 'package:swadesic/services/logistics_partner_services/logistics_partner_services.dart';
import 'package:swadesic/services/buyer_my_order_services/buyer_my_order_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:url_launcher/url_launcher.dart';

enum ReturnTrackingDetailState { Loading, Success, Failed }

class ReturnTrackingDetailsBloc {
  // region Common Variables
  BuildContext context;
  late BuyerMyOrderServices buyerMyOrderServices;
  final BuyerSubOrderBloc buyerSubOrderBloc;

  late ReturnTrackingDetailResponse returnTrackingDetailResponse;
  final String packageNumber;
  final Order order;
  bool isSelfReturn = false;
  bool? isUrlValid;

  // endregion

  //region Controller
  final bottomSheetRefresh = StreamController<bool>.broadcast();
  final logisticCtrl = StreamController<bool>.broadcast();
  final returnTrackingDetailCtrl =
      StreamController<ReturnTrackingDetailState>.broadcast();

  //endregion

  //region Text Controller
  ///Seller text ctrl
  final nameTextCtrl = TextEditingController();
  final phoneNumberTextCtrl = TextEditingController();

  ///Logistics text ctrl
  final logisticPartnerTextCtrl = TextEditingController();
  final trackingLinkTextCtrl = TextEditingController();
  final trackingNumberTextCtrl = TextEditingController();

  ///Common
  final notesTextCtrl = TextEditingController();
  final groupNameTextCtrl = TextEditingController();

  ///Logistics partners

  late LogisticsPartnerServices logisticsPartnerServices;
  late LogisticsPartnerResponse logisticsPartnerResponse;

  //endregion

  // region | Constructor |
  ReturnTrackingDetailsBloc(this.context, this.order, this.buyerSubOrderBloc,
      List<SubOrder> suborderList, this.packageNumber);
  // endregion

  // region Init
  void init() {
    buyerMyOrderServices = BuyerMyOrderServices();
    getReturnTrackingDetail(packageNumber: packageNumber);
    logisticsPartnerServices = LogisticsPartnerServices();
    getLogisticsPartner();
  }
  // endregion

  //region Get return tracking detail
  getReturnTrackingDetail({required String packageNumber}) async {
    //region Try
    try {
      returnTrackingDetailCtrl.sink.add(ReturnTrackingDetailState.Loading);
      returnTrackingDetailResponse = await buyerMyOrderServices
          .getReturnTrackingDetail(packageNumber: packageNumber);
      returnTrackingDetailCtrl.sink.add(ReturnTrackingDetailState.Success);
      //Add all data to text field
      addAllDataToField();
    }
    //endregion
    on ApiErrorResponseMessage {
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
            commonErrorMessage: true);
      }
      return;
    } catch (error) {
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
            commonErrorMessage: true);
      }
      return;
    }
  }
  //endregion

  //region Add all data to the field
  void addAllDataToField() {
    //Group name
    groupNameTextCtrl.text =
        returnTrackingDetailResponse.data!.displayReturnPackageNumber ?? "";

    //Self return
    if (returnTrackingDetailResponse.data!.selfReturnByStore ?? false) {
      isSelfReturn = true;
      nameTextCtrl.text =
          returnTrackingDetailResponse.data!.returnPersonName ?? "";
      phoneNumberTextCtrl.text = returnTrackingDetailResponse
              .data!.returnPersonContact
              ?.replaceAll("+91", "") ??
          "";
    }

    //Logistic
    if (returnTrackingDetailResponse.data!.returnByLogisticPartner ?? false) {
      isSelfReturn = false;
      logisticPartnerTextCtrl.text =
          returnTrackingDetailResponse.data!.returnPickupLogisticPartner ?? "";
      trackingNumberTextCtrl.text =
          returnTrackingDetailResponse.data!.returnTrackingNumber ?? "";
      trackingLinkTextCtrl.text =
          returnTrackingDetailResponse.data!.returnTrackingLink ?? "";
    }

    //Notes
    notesTextCtrl.text =
        returnTrackingDetailResponse.data!.additionalReturnNotes ?? "";
  }
  //endregion

  //region On select return method
  void onSelectReturnMethod(bool value) {
    isSelfReturn = value;
    returnTrackingDetailCtrl.sink.add(ReturnTrackingDetailState.Success);
  }
  //endregion

  //region Get logistics partners
  void getLogisticsPartner() async {
    //region Try
    try {
      logisticsPartnerResponse =
          await logisticsPartnerServices.getLogisticsPartners();
    }
    //endregion
    on ApiErrorResponseMessage {
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
            commonErrorMessage: true);
      }
      return;
    } catch (error) {
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
            commonErrorMessage: true);
      }
      return;
    }
  }
  //endregion

  //region On tap logistics partner
  void onTapLogisticsPartners() {
    List<String> dataList = [];
    for (var data in logisticsPartnerResponse.data!) {
      dataList.add(data.courierName!);
    }

    var screen = SearchScreen(
      dataList: dataList,
      searchTitle: AppStrings.selectLogisticPartner,
      isAddFeatureEnable: true,
    );

    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route)
        .then((value) {
      if (value == null) {
        return;
      }
      //Add value
      logisticPartnerTextCtrl.text = value;
      //Success
      returnTrackingDetailCtrl.sink.add(ReturnTrackingDetailState.Success);
      //Find the link belongs to this logistic partner
      var data = logisticsPartnerResponse.data!.firstWhere((element) {
        return element.courierName!.toLowerCase() == value.toLowerCase();
      });
      trackingLinkTextCtrl.text = data.trackingLink!;
    });
  }
  //endregion

  //region Validate URL
  void validateUrl(String url) async {
    if (url.isEmpty) {
      isUrlValid = null;
      return;
    }
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        isUrlValid = true;
      } else {
        isUrlValid = false;
      }
    } catch (e) {
      isUrlValid = false;
    }
    logisticCtrl.sink.add(true);
  }
  //endregion

  //region Check URL validation
  void checkUrlValidation() {
    if (trackingLinkTextCtrl.text.isEmpty) {
      isUrlValid = null;
    } else {
      isUrlValid =
          CommonMethods.urlValidationCheck(url: trackingLinkTextCtrl.text);
    }
    logisticCtrl.sink.add(true);
  }
  //endregion

  //region Dispose
  void dispose() {
    returnTrackingDetailCtrl.close();
    logisticCtrl.close();
    bottomSheetRefresh.close();
  }
  //endregion
}
