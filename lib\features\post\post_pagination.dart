import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/post_data_model/post_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/post/feed/feed_bloc.dart';
import 'package:swadesic/features/post/post_screen_bloc.dart';
import 'package:swadesic/features/post/recommened_posts/recommended_post_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/post_response/get_all_post_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/post_service/post_service.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum PostPaginationState { Loading, Done, Empty }

class PostPagination {
  //region Context
  late BuildContext context;
  late PostScreenBloc postScreenBloc;
  int limit = 10;
  // bool isLoadingPaginationData = false;
  PostPaginationState currentPaginationState = PostPaginationState.Loading;
  //endregion

  //region Controller
  final postPaginationStateCtrl =
      StreamController<PostPaginationState>.broadcast();
  //endregion

//region Constructor
  PostPagination(this.context, this.postScreenBloc) {
    //Add initial state in pagination state
    postPaginationStateCtrl.sink.add(PostPaginationState.Done);
    //Scroll listener
    // postScreenBloc.previousScrollController.addListener(() {
    //   scrollListener();
    // });
    postScreenBloc.postScreenScrollController.addListener(() {
      // //print("Post scroll ${postScreenBloc.postScreenScrollController.offset}");
      if (postScreenBloc.postScreenScrollController.offset > 10) {
        postScreenBloc.previousScrollController
            .jumpTo(postScreenBloc.postScreenScrollController.offset - 20);
      }

      //Call api if over scroll post controller
      // if (
      // postScreenBloc.postList.length >= 10 &&
      //     postScreenBloc.postScreenScrollController.offset >= postScreenBloc.postScreenScrollController.position.maxScrollExtent &&
      //     !postScreenBloc.postScreenScrollController.position.outOfRange) {
      //   //Increase offset
      //   scrollListener();
      // }

      scrollListener();
    });

    //Pagination controller listener
    postPaginationStateCtrl.stream.listen((PostPaginationState state) {
      postPaginationControllerListener(state: state);
    });
  }
//endregion

  //region Post page Controller listener
  void postPaginationControllerListener({required PostPaginationState state}) {
    currentPaginationState = state;
    //print("Status of pagination${state.name}");
  }
  //endregion

  //region Scroll listener
  void scrollListener() async {
    //If Page state is empty then return
    if (currentPaginationState == PostPaginationState.Empty) {
      return;
    }

    if (
        // currentPaginationState != FeedPaginationState.Loading &&
        postScreenBloc.postList.length >= 10 &&
            postScreenBloc.postScreenScrollController.offset >=
                postScreenBloc
                    .postScreenScrollController.position.maxScrollExtent &&
            // postScreenBloc.scrollController.offset >= MediaQuery.of(context).size.height &&
            !postScreenBloc.postScreenScrollController.position.outOfRange) {
      //Increase offset
      postScreenBloc.offset = limit + postScreenBloc.offset;
      // Fetch more feed posts when list is scrolled to the bottom
      await getPaginationPosts();
    }
  }
  //endregion

  //region Get pagination posts
  Future<void> getPaginationPosts() async {
    // Get reference to the PostDataModel
    var postDataModel = Provider.of<PostDataModel>(context, listen: false);
    //Get reference to Product data model
    var productDataModel =
        Provider.of<ProductDataModel>(context, listen: false);
    try {
      //Loading
      postPaginationStateCtrl.sink.add(PostPaginationState.Loading);
      // await Future.delayed(Duration(seconds: 2));
      //Api call
      List<dynamic> responseList = [];

      //If repost
      if (postScreenBloc.isRepost) {
        responseList = await PostService().getStoreOrUserRePosts(
            creatorReference: postScreenBloc.creatorReference,
            limit: limit,
            offset: postScreenBloc.offset,
            context: postScreenBloc.context);
      }
      //Else post
      else {
        responseList = await PostService().getStoreOrUserPosts(
            creatorReference: postScreenBloc.creatorReference,
            limit: limit,
            offset: postScreenBloc.offset);
      }

      //Add response in post list
      postScreenBloc.postList.addAll(responseList);
      //Add data in post data model
      // postDataModel.addPostIntoList(postList:responseList);

      //Filter post and product
      for (var postAndProduct in responseList) {
        if (postAndProduct is PostDetail) {
          //Add data in post data model feed
          postDataModel.addPostIntoList(postList: [postAndProduct]);
          // //print('Post: ${postAndProduct.postOrCommentReference}');
        } else if (postAndProduct is Product) {
          //Add data to product data model
          productDataModel.addProductIntoList(products: [postAndProduct]);
          // //print('Product: ${postAndProduct.productReference}');
        }
      }

      //If response list is empty
      if (responseList.isEmpty) {
        currentPaginationState = PostPaginationState.Empty;
        // isLoadingPaginationData = false;
        return postPaginationStateCtrl.sink.add(PostPaginationState.Empty);
      }
      // isLoadingPaginationData = false;
      //Done
      postPaginationStateCtrl.sink.add(PostPaginationState.Done);
    } on ApiErrorResponseMessage catch (error) {
      // isLoadingPaginationData = false;
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      //Done
      postPaginationStateCtrl.sink.add(PostPaginationState.Done);
    } catch (error) {
      // isLoadingPaginationData = false;
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
      //Done
      postPaginationStateCtrl.sink.add(PostPaginationState.Done);
    }
  }
//endregion
}
