import 'dart:async';
import 'dart:convert';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:swadesic/features/bottom_navigations/store_bottom_navigation/store_bottom_navigation.dart';
import 'package:swadesic/features/bottom_navigations/user_bottom_navigation/user_bottom_navigation.dart';
import 'package:swadesic/features/buyers/shopping_cart/cart_delivery_address/cart_delivery_address_bloc.dart';
import 'package:swadesic/features/buyers/shopping_cart/secure_checkout/secure_checkout_bloc.dart';
import 'package:swadesic/features/buyers/shopping_cart/shopping_cart_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/search/search_screen.dart';
import 'package:swadesic/features/mobile_number_otp/intro_slider/intro_slider_screen.dart';
import 'package:swadesic/features/mobile_number_otp/mobile_number_otp_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/india_state_and_union_response/india_state_and_union_response.dart';
import 'package:swadesic/model/shopping_cart_address_response/shopping_cart_address_response.dart';
import 'package:swadesic/model/user_details_response/city_response.dart';
import 'package:swadesic/services/get_app_data_services/get_app_data_service.dart';
import 'package:swadesic/services/user_address_services/user_address_service.dart';
import 'package:swadesic/services/user_detail_services/user_detail_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/no_internet_handle/no_internet_handle.dart';
import 'package:swadesic/util/universal_link/universal_link.dart';

class AddEditCartAddressBloc {
  // region Common Methods
  BuildContext context;
  final CartDeliveryAddressBloc cartDeliveryAddressBloc;
  final ShoppingCartAddress? shoppingCartAddress;
  late CityResponse cityResponse;
  late IndiaStateAndUnionResponse indiaStateAndUnionResponse =
      IndiaStateAndUnionResponse();
  List<String> stateList = [];

  // endregion

  //region Controller
  final refreshCtrl = StreamController<bool>.broadcast();
  //endregion

  //region Text ctrl
  final TextEditingController firstNameTextCtrl = TextEditingController();
  final TextEditingController phoneNumberTextCtrl = TextEditingController();
  final TextEditingController addressTextCtrl = TextEditingController();
  final TextEditingController cityTextCtrl = TextEditingController();
  final TextEditingController pinCodeTextCtrl = TextEditingController();
  final TextEditingController stateTextCtrl = TextEditingController();
  //endregion

  // region | Constructor |
  AddEditCartAddressBloc(
      this.context, this.cartDeliveryAddressBloc, this.shoppingCartAddress);
  // endregion

  // region Init
  init() {
    getCityApiCall();
    indiaStateAndUnion();
    addDataToTextField();
  }
  // endregion

  //region Add data to text field
  void addDataToTextField() {
    if (shoppingCartAddress != null) {
      firstNameTextCtrl.text = shoppingCartAddress!.name!;
      phoneNumberTextCtrl.text = shoppingCartAddress!.phoneNumber!;
      addressTextCtrl.text = shoppingCartAddress!.address!;
      cityTextCtrl.text = shoppingCartAddress!.city!;
      pinCodeTextCtrl.text = shoppingCartAddress!.pincode!;
      stateTextCtrl.text = shoppingCartAddress!.state!;
    }
  }
  //endregion

  //region Get city api call
  getCityApiCall() async {
    try {
      cityResponse = await UserDetailsServices().getCity();
    } on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }
//endregion

  //region On tap city
  void onTapCity() {
    List<String> dataList = [];
    for (var data in cityResponse.data!) {
      dataList.add(data.city!);
    }

    var screen = SearchScreen(
      dataList: dataList,
      searchTitle: AppStrings.searchCity,
      isAddFeatureEnable: true,
    );

    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route)
        .then((value) {
      if (value == null) {
        return;
      }
      cityTextCtrl.text = value;
      refreshCtrl.sink.add(true);
      // selectedCategory = value;
      // giveFeedbackCtrl.sink.add(GivefeedbackState.Success);
    });
  }
  //endregion

  //region Get india.json state and union
  void indiaStateAndUnion() async {
    final String response = await rootBundle
        .loadString('assets/india_locations/india_state_and_union.json');
    Map<String, dynamic> jsonResponse = json.decode(response);
    indiaStateAndUnionResponse =
        IndiaStateAndUnionResponse.fromJson(jsonResponse);
    //Add all state list
    stateList.addAll(indiaStateAndUnionResponse.indiaStateAndUnion!);
    //Refresh
    //Success
    refreshCtrl.sink.add(true);
  }
  //endregion

  //region On tap state
  void onTapState() {
    var screen = SearchScreen(
      dataList: stateList,
      searchTitle: AppStrings.state,
      isAddFeatureEnable: false,
    );

    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route)
        .then((value) {
      if (value == null) {
        return;
      }
      stateTextCtrl.text = value;
      //Success
      refreshCtrl.sink
          .add(true); // giveFeedbackCtrl.sink.add(GivefeedbackState.Success);
    });
  }
  //endregion

//region Dispose
  void dispose() {
    refreshCtrl.close();
  }
//endregion
}
