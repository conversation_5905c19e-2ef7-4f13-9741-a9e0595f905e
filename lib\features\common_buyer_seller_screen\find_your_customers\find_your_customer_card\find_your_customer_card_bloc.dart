import 'dart:async';

import 'package:contacts_service/contacts_service.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:swadesic/features/buyers/buyer_view_store/buyer_view_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/find_your_customer_or_friends_disclaimer/find_your_customer_or_friends_disclaimer.dart';
import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/find_your_customers_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/find_your_customers_response/get_contact_user_and_store_info_response.dart';
import 'package:swadesic/model/maintenance_response/maintenance_response.dart';
import 'package:swadesic/services/app_permission_handler/app_permission_handler.dart';
import 'package:swadesic/services/find_your_customers_services/find_your_customers_services.dart';
import 'package:swadesic/services/maintenance_services/maintenance_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_enums.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum FindYourCustomerCardState { Loading, Success, Failed }

class FindYourCustomerCardBloc {
  // region Common Methods
  BuildContext context;
  final ContactUserAndStoreInfo contactUserAndStoreInfo;
  final FindYourCustomersBloc findYourCustomersBloc;
  final String reference;
  List<String> followingSupportingStatusList = ["Following", "Supporting"];

  // endregion
  //region Controller
  final findYourCustomerCardStateCtrl =
      StreamController<FindYourCustomerCardState>.broadcast();

  //endregion

  // region | Constructor |
  FindYourCustomerCardBloc(this.context, this.contactUserAndStoreInfo,
      this.findYourCustomersBloc, this.reference);

  // endregion

  // region Init
  init() {}

  // endregion

  ///1
  //region On tap action
  void onTapAction(
      {required ContactUserAndStoreInfo contactUserAndStoreInfo}) async {
    //Un-Register user follow api call
    if (contactUserAndStoreInfo.entityType == EntityType.UNREGISTERED.name) {
      await followAndUnFollowUNRegisterUser(
          contactUserAndStoreInfo: contactUserAndStoreInfo);
    } else {
      await followAndUnFollowUserAndStore(
          contactUserAndStoreInfo: contactUserAndStoreInfo);
    }
  }

  //endregion

  /// 2
  //Un-register user
  //region Follow un-follow unregister user
  Future<void> followAndUnFollowUNRegisterUser(
      {required ContactUserAndStoreInfo contactUserAndStoreInfo}) async {
    //region Try
    try {
      //Loading
      findYourCustomerCardStateCtrl.sink.add(FindYourCustomerCardState.Loading);
      //Api call
      // await FindYourCustomersServices().followUnFollowUnRegisteredUser(contactList: [
      //   {"name": contactUserAndStoreInfo.name, "phonenumber": contactUserAndStoreInfo.phoneNumber}
      // ], toFollow: contactUserAndStoreInfo.followStatus == "Follow", reference: reference);
      //Update status
      // contactUserAndStoreInfo.followStatus = findYourCustomersBloc.updatedFollowStatus(
      //     action: contactUserAndStoreInfo.followStatus!,
      //     isActiveUser: contactUserAndStoreInfo.referenceType == "user",
      //     isInActiveUser: contactUserAndStoreInfo.referenceType == "contact")['updatedStatus'];
      //Success
      findYourCustomerCardStateCtrl.sink.add(FindYourCustomerCardState.Success);
    }
    //endregion
    on ApiErrorResponseMessage {
      //Message
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
      // findYourCustomerStateCtrl.sink.add(FindYourCustomersState.Success);
    } catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
    }
  }

//endregion

  /// 2
  //Register use and store
  //region Follow un-follow register user and store
  Future<void> followAndUnFollowUserAndStore(
      {required ContactUserAndStoreInfo contactUserAndStoreInfo}) async {
    try {
      //Loading
      findYourCustomerCardStateCtrl.sink.add(FindYourCustomerCardState.Loading);

      // String currentFollowStatus =
      // String? currentFollowStatus = await FindYourCustomersServices().followAndUnFollowRegisterUserAndStore(
      //     referencesList: [contactUserAndStoreInfo.reference!],
      //     toFollow: !["Following", "Supporting"].contains(contactUserAndStoreInfo.followStatus!),
      //     reference: reference);
      //Update local status
      // contactUserAndStoreInfo.followStatus = currentFollowStatus;
      //Success
      findYourCustomerCardStateCtrl.sink.add(FindYourCustomerCardState.Success);
    } on ApiErrorResponseMessage catch (e) {
      //Message
      context.mounted
          ? CommonMethods.toastMessage(e.message.toString(), context)
          : null;
      //Failed
      findYourCustomerCardStateCtrl.sink.add(FindYourCustomerCardState.Failed);
    } catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
      //Failed
      findYourCustomerCardStateCtrl.sink.add(FindYourCustomerCardState.Failed);
    }
  }

//endregion

  //region On tap Register user and store icon
  void onTapRegisterUserAndStoreIcon(
      {required ContactUserAndStoreInfo contactUserAndStoreInfo}) {
    //Screen
    late StatefulWidget screen;
    //If store
    // if(contactUserAndStoreInfo.referenceType =="store"){
    //    screen = BuyerViewStoreScreen(storeReference:contactUserAndStoreInfo.reference ,);
    // }
    //User
    // else if(contactUserAndStoreInfo.referenceType =="user"){
    //   screen = BuyerViewStoreScreen(storeReference:contactUserAndStoreInfo.reference ,isStoreOwnerView: contactUserAndStoreInfo.reference == AppConstants.appData.userReference,);
    // }
    //Else return
    // else{
    //   return;
    // }

    // var route = MaterialPageRoute(builder: (context) => screen);
    // Navigator.push(context, route);
  }
  //endregion

//region Dispose
  void dispose() {
    findYourCustomerCardStateCtrl.close();
  }
//endregion
}
