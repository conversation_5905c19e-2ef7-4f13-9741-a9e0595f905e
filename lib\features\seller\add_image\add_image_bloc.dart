import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/app_config_data_model/app_config_data_model.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';


class AddImageBloc {
  // region Common Variables
  BuildContext context;
  final ImagePicker picker = ImagePicker();
  //Get app config reference from data model
  late AppConfigDataModel appConfigDataModel ;
  // var selectedImage;
  // late List<XFile> selectedImage;

  // endregion


  //region Controller
  final blankCtrl = StreamController<String>.broadcast();



  //endregion

  // region | Constructor |
  AddImageBloc(this.context);

  // endregion

  // region Init
  void init() async{
    appConfigDataModel = Provider.of<AppConfigDataModel>(context, listen: false);


  }
// endregion


//region Open Gallery
  void openGallery() async {
    int imageLimit = appConfigDataModel.appConfig!.productImageLimit;

    try {
      // Get the current number of selected images
      int currentImageCount = AppConstants.multipleSelectedImage.length;

      // Check if image count has already reached the limit of 6
      if (currentImageCount >= imageLimit) {
        return CommonMethods.toastMessage("${AppStrings.youCantAddMoreThen} $imageLimit images", context);
      }

      // Pick multiple images from the gallery
      List<XFile> galleryImages = await picker.pickMultiImage(requestFullMetadata: true);

      // If no images were selected, return early
      if (galleryImages == null || galleryImages.isEmpty) return;

      // Calculate how many more images can be added without exceeding the limit
      int remainingSlots = imageLimit - currentImageCount;

      // If more images were selected than can be added, show a message and limit the selection
      if (galleryImages.length > remainingSlots) {
         CommonMethods.toastMessage("${AppStrings.youCantAddMoreThen} $imageLimit images", context);
        galleryImages = galleryImages.sublist(0, remainingSlots); // Limit the selection
      }

      // Compress and add the selected images
      for (var image in galleryImages) {
        // Compress the image
        var newFile = await CommonMethods.compressImage(originalFile: File(image.path));
        //print(newFile.path);

        // Add the compressed image to the list
        AppConstants.multipleSelectedImage.add(XFile(newFile.path));
      }

      // Pop the gallery screen and return to the previous screen
      Navigator.pop(context);

      // Print the number of images added
      //print(AppConstants.multipleSelectedImage.length);
    } catch (e) {
      //print("Error: $e");
    }
  }
//endregion


//region Open Gallery Single Image Pick
  void openGallerySingleImage()async{
    try{
      //AppConstants.selectedSingleImage = null;
      XFile singleImage = (await picker.pickImage(source: ImageSource.gallery, requestFullMetadata: true))!;

      //Compress image
      var newFile = await CommonMethods.compressImage(originalFile: File(singleImage.path));
      //print(newFile.path);
      //Add Compressed image
      AppConstants.selectedSingleImage = XFile(newFile.path);

      if(AppConstants.selectedSingleImage == null) return;
      //print(galleryImage.toString());
      // selectedImage = File(image.path);
      // AppConstants.captureImages.insert(0,XFile(""));

      //print(  AppConstants.multipleSelectedImage.length);
      // var screen =  SelectedImagePreviewScreen();
      // var route = MaterialPageRoute(builder: (context) => screen);
      Navigator.pop(context);
    }
    catch(e){
      //print("Error is $e");
    }

  }

//endregion


  //region Open Camera Select Single Image
  void openCameraSelectSingleImage()async{
    try{
      //AppConstants.selectedSingleImage = null;
     XFile singleImage = (await picker.pickImage(source: ImageSource.camera, requestFullMetadata: true))!;


    //Compress image
    var newFile = await CommonMethods.compressImage(originalFile: File(singleImage.path));
    //print(newFile.path);
    //Add Compressed image
     AppConstants.selectedSingleImage = XFile(newFile.path);

      if(  AppConstants.selectedSingleImage == null) return;
      ///
      // selectedImage = File(image.path);
      // //print(AppConstants.captureImages.length);
      //  var screen =  SelectedImagePreviewScreen();
      //  var route = MaterialPageRoute(builder: (context) => screen);
      //  Navigator.pushReplacement(context, route);
      // var route = MaterialPageRoute(builder: (context) => screen);
      Navigator.pop(context);
      // Navigator.pushReplacement(context, MaterialPageRoute(builder: (BuildContext context) => SelectedImageScreen(image: selectedImage)));
    }
    catch(e){
      //print(e);
    }

  }

//endregion



//region Open Camera
  void openCamera()async{
    int imageLimit = appConfigDataModel.appConfig!.productImageLimit;

    try{

      //Check if image count has reached the limit of 10
      if (AppConstants.multipleSelectedImage.length >= imageLimit) {
        return CommonMethods.toastMessage("${AppStrings.youCantAddMoreThen} $imageLimit images", context);
      }

      XFile singleImage = (await picker.pickImage(source: ImageSource.camera, requestFullMetadata: true))!;
      

      //Compress image
      var newFile = await CommonMethods.compressImage(originalFile: File(singleImage.path));
      //Add Compressed image
      AppConstants.multipleSelectedImage.add(XFile(newFile.path));
      if(singleImage == null) return;

     // //print(AppConstants.captureImages.length);
     //  var screen =  SelectedImagePreviewScreen();
     //  var route = MaterialPageRoute(builder: (context) => screen);
     //  Navigator.pushReplacement(context, route);
      // var route = MaterialPageRoute(builder: (context) => screen);
      Navigator.pop(context);
      // Navigator.pushReplacement(context, MaterialPageRoute(builder: (BuildContext context) => SelectedImageScreen(image: selectedImage)));
    }
    catch(e){
      //print(e);
    }

  }

//endregion








//region Go to Add Product

//endregion



}
