import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/external_reviews/review_submitted_success_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/get_buyer_single_product_and_image/get_single_product_detail_response.dart';
import 'package:swadesic/model/store_product_response/store_product_response.dart';
import 'package:swadesic/services/external_review_service/external_review_service.dart';
import 'package:swadesic/services/single_product_and_image_service/single_product_and_image_service.dart';
import 'package:swadesic/services/user_detail_services/user_detail_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum ExternalReviewState { initial, loading, success, failed }

class ExternalReviewRequestBloc {
  // region Common Variables
  BuildContext context;
  String token;
  String productReference;
  String userReference;

  // Product details
  Product product = Product();
  GetSingleProductDetailResponse? productDetailResponse;

  // Review details
  TextEditingController reviewController = TextEditingController();
  double rating = 0.0;
  List<File> selectedImages = [];

  // State controller
  final stateCtrl = StreamController<ExternalReviewState>.broadcast();

  // Is user logged in
  bool isStaticUser = (CommonMethods().isStaticUser());

  // Services
  final ExternalReviewService _externalReviewService = ExternalReviewService();
  final UserDetailsServices _userDetailsServices = UserDetailsServices();

  // endregion

  // region | Constructor |
  ExternalReviewRequestBloc(
      this.context, this.token, this.productReference, this.userReference) {
    _loadProductDetails();
  }
  // endregion

  // region Load product details
  Future<void> _loadProductDetails() async {
    try {
      stateCtrl.sink.add(ExternalReviewState.loading);

      // Get product details using the existing method
      productDetailResponse =
          await SingleProductAndImageService().getSingleProductInfo(
        productReference: productReference,
        pinCode: '000000', // Default pincode
      );

      // Set product details
      product = productDetailResponse!.singleProduct!;

      // Get product images
      var productImageResponse = await SingleProductAndImageService()
          .getSingleProductImage(productReference, "");

      // Add images to product
      product.prodImages = productImageResponse.data!;

      // Add to product data model for caching
      // Store a reference to the context before the async gap
      final currentContext = context;
      if (currentContext.mounted) {
        var productDataModel =
            Provider.of<ProductDataModel>(currentContext, listen: false);
        productDataModel.addProductIntoList(products: [product]);
      }

      stateCtrl.sink.add(ExternalReviewState.success);
    } on ApiErrorResponseMessage catch (error) {
      stateCtrl.sink.add(ExternalReviewState.failed);
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
    } catch (error) {
      stateCtrl.sink.add(ExternalReviewState.failed);
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
    }
  }
  // endregion

  // region On tap add images
  Future<void> onTapAddImages() async {
    final ImagePicker picker = ImagePicker();

    try {
      final List<XFile> images = await picker.pickMultiImage();

      if (images.isNotEmpty) {
        for (var image in images) {
          selectedImages.add(File(image.path));
        }
        stateCtrl.sink.add(ExternalReviewState.success); // Refresh UI
      }
    } catch (e) {
      context.mounted
          ? CommonMethods.toastMessage("Failed to pick images", context)
          : null;
    }
  }
  // endregion

  // region On tap remove image
  void onTapRemoveImage(int index) {
    if (index >= 0 && index < selectedImages.length) {
      selectedImages.removeAt(index);
      stateCtrl.sink.add(ExternalReviewState.success); // Refresh UI
    }
  }
  // endregion

  // region On rating changed
  void onRatingChanged(double value) {
    rating = value;
    stateCtrl.sink.add(ExternalReviewState.success); // Refresh UI
  }
  // endregion

  // region On tap submit review
  Future<void> onTapSubmitReview() async {
    try {
      // Validate input
      if (rating == 0) {
        CommonMethods.toastMessage("Please add a rating", context);
        return;
      }

      if (reviewController.text.trim().isEmpty) {
        CommonMethods.toastMessage("Please write a review", context);
        return;
      }

      stateCtrl.sink.add(ExternalReviewState.loading);

      // Validate if the user reference belongs to the current logged-in user
      if (!await _validateUserReference()) {
        stateCtrl.sink.add(ExternalReviewState.failed);
        context.mounted
            ? CommonMethods.toastMessage(
                "You are not authorized to submit this review. This review link was created for a different user.",
                context)
            : null;
        return;
      }

      // Submit review
      await _externalReviewService.addExternalReview(
        productReference: productReference,
        commentText: reviewController.text.trim(),
        ratingCount: rating.toInt(),
        token: token,
        entityReference: AppConstants.appData.userReference!,
        images: selectedImages.isNotEmpty ? selectedImages : null,
      );

      stateCtrl.sink.add(ExternalReviewState.success);

      // Show success message
      context.mounted
          ? CommonMethods.toastMessage("Review submitted successfully", context)
          : null;

      // Navigate to success screen
      if (context.mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => ReviewSubmittedSuccessScreen(
              productReference: productReference,
              productName: product.productName ?? '',
            ),
          ),
        );
      }
    } on ApiErrorResponseMessage catch (error) {
      stateCtrl.sink.add(ExternalReviewState.failed);
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
    } catch (error) {
      stateCtrl.sink.add(ExternalReviewState.failed);
      context.mounted
          ? CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
              commonErrorMessage: true)
          : null;
    }
  }
  // endregion

  // region On tap sign in
  void onTapSignIn() {
    // Save the current screen parameters to return after sign-in
    AppConstants.externalReviewParams = {
      'token': token,
      'productReference': productReference,
      'userReference': userReference,
    };

    // Navigate to sign-in screen
    CommonMethods().goToSignUpFlow();
  }
  // endregion

  // region Validate user reference
  Future<bool> _validateUserReference() async {
    // If user is static, we don't need to validate
    if (isStaticUser) {
      return false;
    }

    try {
      // Get current user details
      final currentUserDetails =
          await _userDetailsServices.getLoggedInUserDetail(
        userReference: AppConstants.appData.userReference!,
      );

      // Check if the user reference matches any of the user identifiers
      final currentUser = currentUserDetails.userDetail!;

      // The user reference could be a username, email, or phone number
      // Check if it matches any of these fields
      if (userReference == currentUser.userReference ||
          userReference == currentUser.userName ||
          userReference == currentUser.email ||
          userReference == currentUser.phonenumber) {
        return true;
      }

      // If we get here, the user reference doesn't match any of the current user's identifiers
      return false;
    } catch (e) {
      // If there's an error, we'll assume the validation failed
      return false;
    }
  }

  // region Dispose
  void dispose() {
    reviewController.dispose();
    stateCtrl.close();
  }
  // endregion
}
