import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/navigation/navigation_router.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/add_user_profile_picture/add_user_profile_picture.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/recommended_stores/recommended_store_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/find_your_customers/find_your_customers_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/search/search_screen.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/external_reviews/external_review_request_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/user_details_response/city_response.dart';
import 'package:swadesic/model/user_details_response/user_details_response.dart';
import 'package:swadesic/model/user_profile/user_create_profile_response.dart';
import 'package:swadesic/services/cache_storage/cache_storage_service.dart';
import 'package:swadesic/services/get_app_data_services/get_app_data_service.dart';
import 'package:swadesic/services/user_detail_services/user_detail_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum BuyerOnBoardingState { Loading, Success, Failed }

class BuyerOnBoardingBloc {
  // region Common Methods
  BuildContext context;
  String gender = "Female";

  final String userReference;
  late CityResponse cityResponse;
  bool isExistUser = true;
  List<String> selectedRoles = [];
  final String? icon;

  ///User Details
  late UserDetailsServices userDetailsServices;
  static GetUserDetailsResponse userDetailsResponse = GetUserDetailsResponse();

  late UserCreateProfileResponse userCreateProfileResponse;

  // late UserProfileService userProfileService;

  // late var uploadFileService = UploadFileService();
  late CacheStorageService cacheStorageService;

  // endregion

  //region Controller
  final buyerInfoCtrl = StreamController<BuyerOnBoardingState>.broadcast();
  final genderCtrl = StreamController<String>.broadcast();
  final refreshCtrl = StreamController<bool>.broadcast();

  //endregion

  //region Text Controller
  TextEditingController nameTextCtrl = TextEditingController();
  TextEditingController referralCodeTextCtrl = TextEditingController();
  TextEditingController userNameTextCtrl = TextEditingController();
  TextEditingController cityTextCtrl = TextEditingController();

  //endregion

  // region | Constructor |
  BuyerOnBoardingBloc(this.context, this.userReference, this.icon);

  // endregion

  // region Init
  void init() {
    cacheStorageService = CacheStorageService();
    // userProfileService = UserProfileService();
    userDetailsServices = UserDetailsServices();
    getCityApiCall();
    getReferralCode();
  }
  // endregion

  //region get referral code
  void getReferralCode() async {
    String referralCode = await AppDataService().getReferralCode();
    DateTime date = await AppDataService().getReferralCodeSavedDate();
    if (referralCode.isNotEmpty &&
        (DateTime.now().difference(date).inDays < 10)) {
      referralCodeTextCtrl.text = referralCode;
    }
  }
  //endregion

  //region Create User Profile Api call
  createUserProfileApiCall() async {
    try {
      if (nameTextCtrl.text.isEmpty ||
          cityTextCtrl.text.isEmpty ||
          gender.isEmpty ||
          userNameTextCtrl.text.trim().isEmpty) {
        CommonMethods.toastMessage(AppStrings.fieldEmpty, context);
        return;
      }
      //Check role selection
      if (selectedRoles.isEmpty) {
        CommonMethods.toastMessage(
            AppStrings.pleaseSelectAtLeaseOneRole, context);
        return;
      }

      //Use user invalid
      if (userNameTextCtrl.text.trim().isNotEmpty && isExistUser) {
        CommonMethods.toastMessage(AppStrings.enterValidUserName, context);
        return;
      }
      buyerInfoCtrl.sink.add(BuyerOnBoardingState.Loading);
      var body = {
        "user_reference": userReference,
        "gender": gender,
        "user_location": cityTextCtrl.text,
        "first_name": nameTextCtrl.text,
        "display_name": nameTextCtrl.text,
        "user_name": userNameTextCtrl.text.trim(),
        "user_roles": selectedRoles,
      };

      if (referralCodeTextCtrl.text.trim().isNotEmpty) {
        body["invited_by_code"] = referralCodeTextCtrl.text.trim();
      }
      //Update user info api
      await userDetailsServices.editUserProfile(
          data: body, userReference: userReference);
      //Get user info back
      await getLoggedInUserDetail();
      //Success
      buyerInfoCtrl.sink.add(BuyerOnBoardingState.Success);
    } on ApiErrorResponseMessage catch (error) {
      buyerInfoCtrl.sink.add(BuyerOnBoardingState.Failed);
      CommonMethods.toastMessage(error.message.toString(), context);

      return;
    } catch (error) {
      buyerInfoCtrl.sink.add(BuyerOnBoardingState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }

  //endregion

  //region Get logged in user detail
  // Future<void> getLoggedInUserDetail() async {
  //   try {
  //     //selectedAddressRefreshCtrl.sink.add(SellerReturnWarrantyState.Loading);
  //     userDetailsResponse = await userDetailsServices.getLoggedInUserDetail(userReference: userReference);
  //
  //     ///Save user info to global and share pref
  //     saveUserInfoInGlobalAndSharePref();
  //   }
  //   //endregion
  //   on ApiErrorResponseMessage catch (error) {
  //     //print(error);
  //     CommonMethods.toastMessage(AppStrings.commonErrorMessage, context, commonErrorMessage: true);
  //     return;
  //     return;
  //     //print(error.message);
  //     CommonMethods.toastMessage(AppStrings.commonErrorMessage, context, commonErrorMessage: true);
  //     return;
  //   } catch (error) {
  //     //print(error);
  //     CommonMethods.toastMessage(AppStrings.commonErrorMessage, context, commonErrorMessage: true);
  //     return;
  //   }
  // }
  //endregion

  //region Get logged in user detail
  Future<void> getLoggedInUserDetail() async {
    late LoggedInUserInfoDataModel loggedInUserInfoDataModel =
        Provider.of<LoggedInUserInfoDataModel>(context, listen: false);
    //Get reference to Product data model
    var productDataModel =
        Provider.of<ProductDataModel>(context, listen: false);
    //region Try
    try {
      //selectedAddressRefreshCtrl.sink.add(SellerReturnWarrantyState.Loading);
      userDetailsResponse = await UserDetailsServices()
          .getLoggedInUserDetail(userReference: userReference);

      ///Add user info to logged in user data model
      loggedInUserInfoDataModel.setUserInfoResponse(
          data: userDetailsResponse.userDetail!);
      //Update the buy button to refresh in all loaded product
      for (var product in productDataModel.allProducts) {
        product.isPinCodeChanged = true;
      }
      //Update ui
      productDataModel.updateUi();

      ///Save user info to global and share pref
      saveUserInfoInGlobalAndSharePref();
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      context.mounted
          ? CommonMethods.toastMessage(error.message.toString(), context)
          : null;
      debugPrint(error.message);
    } catch (error) {
      debugPrint(error.toString());
    }
  }
  //endregion

  ///Save user info in global variable and in share preference
  //region Save info in global variable and share preference
  void saveUserInfoInGlobalAndSharePref() {
    //Add user id
    AppConstants.appData.userId = userDetailsResponse.userDetail!.userid!;
    //Add user reference
    AppConstants.appData.userReference =
        userDetailsResponse.userDetail!.userReference!;
    //Mark user view is true
    AppConstants.appData.isUserView = true;
    //Mark store view is false
    AppConstants.appData.isStoreView = false;
    //Pin code
    AppConstants.appData.pinCode = userDetailsResponse.userDetail!.pincode;
    //Add all data to share pref
    AppDataService().addAppData();
    //Go to find your friends
    kIsWeb
        ? goToBottomNavigation()
        : icon == null
            ? goToAddUserProfilePicture()
            : goToFindYourFriend();
  }

  //endregion

  //region Go to Bottom Navigation
  void goToBottomNavigation() {
    //If sign up process from static user
    if (AppConstants.isSignInScreenOpenedForStatisUser) {
      // Check if we need to navigate to the external review screen
      if (AppConstants.externalReviewParams.isNotEmpty) {
        // Get the saved parameters
        String token = AppConstants.externalReviewParams['token'] ?? '';
        String productReference =
            AppConstants.externalReviewParams['productReference'] ?? '';
        String userReference =
            AppConstants.externalReviewParams['userReference'] ?? '';

        // Clear the parameters to avoid navigation loops
        AppConstants.externalReviewParams.clear();

        // Navigate to the external review screen
        if (token.isNotEmpty &&
            productReference.isNotEmpty &&
            userReference.isNotEmpty) {
          // Use a post-frame callback to ensure navigation happens after the current frame
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Navigator.of(AppConstants.userStoreCommonBottomNavigationContext)
                .popUntil((route) => route.isFirst);

            // Push the external review screen
            Navigator.of(AppConstants.userStoreCommonBottomNavigationContext)
                .push(
              MaterialPageRoute(
                builder: (context) => ExternalReviewRequestScreen(
                  token: token,
                  productReference: productReference,
                  userReference: userReference,
                ),
              ),
            );
          });
          return;
        }
      }

      // If no external review parameters, just go back to the home screen
      Navigator.of(AppConstants.userStoreCommonBottomNavigationContext)
          .popUntil((route) => route.isFirst);
      return;
    }
    Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const NavigationRouter()),
        (Route<dynamic> route) => false);
    // var screen =   UserBottomNavigation();
    // var route = MaterialPageRoute(builder: (context) => screen);
    // Navigator.push(context, route);
  }

  //endregion

  // region Go To Recommended Store
  void goToRecommendedStore() {
    //If sign up process from static user
    if (AppConstants.isSignInScreenOpenedForStatisUser) {
      // Navigator.pop(context, true);
      // Navigator.pop(context, true);
      // Navigator.pop(context, true);
      Navigator.of(AppConstants.userStoreCommonBottomNavigationContext)
          .popUntil((route) => route.isFirst);

      return;
    }
    Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const RecommendedStoreScreen()),
        (Route<dynamic> route) => false);
    // var screen = const RecommendedStoreScreen();
    // var route = MaterialPageRoute(builder: (context) => screen);
    // Navigator.push(context, route);
  }

// endregion

  //region On Select Gender
  void onSelectGender(String selectedGender) {
    gender = selectedGender;
    genderCtrl.sink.add(gender);
  }

//endregion

  //region Get city api call
  getCityApiCall() async {
    try {
      cityResponse = await userDetailsServices.getCity();
    } on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    } catch (error) {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }

//endregion

  //region On tap city
  void onTapCity() async {
    List<String> dataList = [];
    for (var data in cityResponse.data!) {
      dataList.add(data.city!);
    }

    var screen = SearchScreen(
      dataList: dataList,
      searchTitle: AppStrings.searchCity,
      isAddFeatureEnable: true,
    );

    var route = CupertinoPageRoute(builder: (context) => screen);
    Navigator.push(context, route).then((value) {
      if (value == null) {
        return;
      }
      cityTextCtrl.text = value;
      refreshCtrl.sink.add(true);
      // giveFeedbackCtrl.sink.add(GivefeedbackState.Success);
    });
  }

  //endregion

//region Go to find your friend
  void goToFindYourFriend() {
    Navigator.of(context).push(
      MaterialPageRoute(
          builder: (context) => FindYourCustomersScreen(
                visibleNext: true,
                title: AppStrings.findYourFriendsOnSwadesic,
                iSFromOnboarding: true,
              )),
    );
  }

//endregion

//region Go to add user profile picture
  void goToAddUserProfilePicture() {
    var screen = AddUserProfilePicture(
        userName: userDetailsResponse.userDetail!.userName!,
        userReference: userDetailsResponse.userDetail!.userReference!);
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
//endregion
}
