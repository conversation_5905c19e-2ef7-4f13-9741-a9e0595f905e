import 'package:flutter/material.dart';
import 'package:swadesic/model/user_details_response/user_details_response.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/common_methods.dart';

class LoggedInUserInfoDataModel with ChangeNotifier {
  UserDetail? userDetail = UserDetail();

  UserDetail? get getUserInfo => userDetail;

  //Set the data
  void setUserInfoResponse({required UserDetail data}) {
    userDetail = data;
    notifyListeners();
  }

  // Method to clear userDetail
  void clearUserInfo() {
    userDetail = UserDetail();
    notifyListeners();
  }

  // Check if user is truly logged in with valid data
  bool get isValidLoggedInUser {
    return userDetail != null &&
        userDetail!.userReference != null &&
        userDetail!.userReference!.isNotEmpty &&
        userDetail!.userReference !=
            'U9999999999999' && // Static user reference
        !CommonMethods().isStaticUser();
  }

  // Clear user data when switching to static user mode
  void switchToStaticUser() {
    userDetail = UserDetail();
    notifyListeners();
  }

  //region Update ui
  void updateUi() {
    notifyListeners();
  }
//endregion
}
