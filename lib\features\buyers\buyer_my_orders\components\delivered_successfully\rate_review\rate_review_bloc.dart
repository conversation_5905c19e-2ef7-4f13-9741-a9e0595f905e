import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:swadesic/features/buyers/buyer_add_image/buyer_add_image_screen.dart';
import 'package:swadesic/features/buyers/buyer_my_orders/buyer_sub_order/buyer_sub_order_bloc.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/order_response/get_order_response.dart';
import 'package:swadesic/model/order_response/sub_order.dart';
import 'package:swadesic/model/product_comment_response/add_parent_comment_response.dart';
import 'package:swadesic/model/product_comment_response/product_all_comment_response.dart';
import 'package:swadesic/services/product_comment_services/product_comment_services.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum RateReviewState { Loading, Success, Failed }

class RateReviewBloc {
  // region Common Variables
  BuildContext context;
  bool isProductListDropDownVisible = true;
  final List<SubOrder> subOrderList;
  late List<SubOrder> noReviewSubOrders = [];
  late List<SubOrder> alreadyHaveReviewSubOrders = [];
  final BuyerSubOrderBloc buyerSubOrderBloc;
  final Order order;
  late ProductCommentServices productCommentServices;
  late ProductAllCommentResponse productAllCommentResponse;

  ///Add Parent comment Response
  late AddParentCommentResponse addParentCommentResponse;

  ///Upload service
  late var uploadFileService = UploadFileService();

  // endregion

  //region Controller
  final rateReviewStateCtrl = StreamController<RateReviewState>.broadcast();

  //endregion

  //region Text Controller

  //endregion

  // region | Constructor |
  RateReviewBloc(
      this.context, this.subOrderList, this.buyerSubOrderBloc, this.order);

  // endregion

  // region Init
  void init() {
    productCommentServices = ProductCommentServices();
    markWhichHasNoReview();
  }

// endregion

  //region Get comment and check is review already exist or not
  Future<void> markWhichHasNoReview() async {
    //region Try
    try {
      //Loading
      // rateReviewStateCtrl.sink.add(RateReviewState.Loading);
      //Add all sub sub order in to noReviewSubOrders
      // noReviewSubOrders.addAll(subOrderList);
      /*
      We need to run a loop to check where sub order has review or not
       */
      for (var data in subOrderList) {
        //Get all comment api call
        productAllCommentResponse =
            await productCommentServices.getProductComment(
                productRef: data.productReference!,
                userReference: AppConstants.appData.userReference!);
        //print(productAllCommentResponse);
        //If comment list is empty
        if (productAllCommentResponse.data!.isEmpty) {
          noReviewSubOrders.add(data);
        }
        //Run a loop to check is this user had not added a comment type is "review"
        for (var comment in productAllCommentResponse.data!) {
          if (comment.commentType == "review" &&
              comment.reference == AppConstants.appData.userReference) {
            //Add sub orders where review already added.
            data.replyAndComments = comment;
            alreadyHaveReviewSubOrders.add(data);
          } else {
            noReviewSubOrders.removeWhere(
                (element) => element.productReference == data.productReference);
          }
        }
      }
      //Remove duplication
      noReviewSubOrders = noReviewSubOrders.toSet().toList();
      alreadyHaveReviewSubOrders = alreadyHaveReviewSubOrders.toSet().toList();

      //Refresh screen
      rateReviewStateCtrl.sink.add(RateReviewState.Success);
      //print(noReviewSubOrders.length);
      //print(alreadyHaveReviewSubOrders.length);
    }
    //endregion
    on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    } catch (error) {
      //print(error);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    }
  }

//endregion

//region On tap Product list Drop down
  void onTapProductList() {
    isProductListDropDownVisible = !isProductListDropDownVisible;
    rateReviewStateCtrl.sink.add(RateReviewState.Success);
  }

//endregion

//region Go to Buyer Add Image Screen
  void goToBuyerAddImageScreen({required SubOrder subOrder}) {
    var screen = const BuyerAddImageScreen();
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(AppConstants.userStoreCommonBottomNavigationContext, route)
        .then((value) {
      //Add all XFiles to list of file in suborders
      for (var data in AppConstants.multipleSelectedImage) {
        //print(data.path);
        subOrder.reviewImages.add(File(data.path));
      }
      //print("Review images leangth are ${subOrder.reviewImages.length}");
      //Clear global images
      AppConstants.multipleSelectedImage.clear();
      //Refresh screen
      rateReviewStateCtrl.sink.add(RateReviewState.Success);
    });
  }

//endregion

//region Remove suborder image
  void removeReviewImage(
      {required SubOrder subOrder, required int imageIndex}) {
    //Remove image where the index is same
    subOrder.reviewImages.removeAt(imageIndex);
    //Refresh
    rateReviewStateCtrl.sink.add(RateReviewState.Success);

    //If review image is empty
  }

//endregion

//region On tap add your review
  void onTapAddReview() async {
    List<int> subOrderHAdRation = [];

    ///Check is all suborder are un touched
    for (var data in noReviewSubOrders) {
      if (data.ratingValue != 0.0) {
        subOrderHAdRation.add(1);
      }
    }
    if (subOrderHAdRation.isEmpty) {
      return CommonMethods.toastMessage(
          AppStrings.pleaseAddYourReview, context);
    }

    ///Api call in a loop
    for (var data in noReviewSubOrders) {
      //check the rating value
      /*
      If the rating is 0.0 then skip .

       */
      if (data.ratingValue == 0.0) {
        //print(data.productName);
        continue;
      } else {
        //print(data.productName);
        await addReview(subOrder: data);
      }
    }
    //Review added
    CommonMethods.toastMessage(AppStrings.reviewAdded, context);
    Navigator.pop(context);
    //Get buyer sub orders
    buyerSubOrderBloc.getSubOrders();
  }

//endregion

  ///1
//region Add review
  Future<void> addReview({required SubOrder subOrder}) async {
    //Check rate is added or not
    if (subOrder.ratingValue == 0.0) {
      return CommonMethods.toastMessage(AppStrings.pleaseRateTheProductFirst,
          AppConstants.userStoreCommonBottomNavigationContext);
    }
    try {
      ///Add review comment
      addParentCommentResponse = await productCommentServices.addParentComment(
          subOrder.productReference!,
          subOrder.reviewComment.isEmpty ? null : subOrder.reviewComment,
          "review");

      ///Add star ratting
      await productCommentServices.productRating(subOrder.ratingValue.round(),
          addParentCommentResponse.data!.commentid!);

      ///Upload image only if images are selected
      if (subOrder.reviewImages.isNotEmpty) {
        await addCommentImage(
            commentId: addParentCommentResponse.data!.commentid!,
            suborder: subOrder);
      }

      ///Message
      CommonMethods.toastMessage(AppStrings.yourFeedbackIsGreatly,
          AppConstants.userStoreCommonBottomNavigationContext);
      //Remove object from list
      noReviewSubOrders.removeWhere(
          (element) => element.suborderNumber == subOrder.suborderNumber);

      ///Call init
      //Navigator.pop(context);
      markWhichHasNoReview();
    } on ApiErrorResponseMessage {
      //buyerViewProductCtrl.sink.add(BuyerViewProductState.Failed);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);
      return;
    } catch (error) {
      //print(error);

      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);

      return;
    }
  }

//endregion

//region Add Comment Image
  addCommentImage({required int commentId, required SubOrder suborder}) async {
    try {
      for (int i = 0; i < suborder.reviewImages.length; i++) {
        //print("File name with extn ${suborder.reviewImages[i].path.split('/').last}");

        await uploadFileService.addCommentImage(
            filePath: suborder.reviewImages[i].path,
            url: AppConstants.addCommentImage,
            fileNameWithExtension:
                suborder.reviewImages[i].path.split('/').last,
            commentId: commentId);
        //print(i);
      }
    } on ApiErrorResponseMessage {
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);

      return;
    } catch (error) {
      //print(error);
      CommonMethods.toastMessage(AppStrings.commonErrorMessage, context,
          commonErrorMessage: true);

      return;
    }
  }
//endregion
}
